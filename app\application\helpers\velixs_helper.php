<?php
// SCRIPT BY VELIXS.COM 
// BELI SOURCE CODE INI KE VELIXS.COM UNTUK MENDAPAKAN UPDATE GRATIS

function base_node()
{
    $ci = &get_instance();
    $row = $ci->db->get_where('settings', ['id' => 1])->row();
    return $row->base_node;
}

function is_login($redirect = true)
{
    $ci = &get_instance();
    if ($ci->session->userdata('id_login')) {
        // Periksa status expired untuk session login
        $user = $ci->db->get_where('account', ['id' => $ci->session->userdata('id_login')])->row();
        if ($user) {
            $current_date = date('Y-m-d H:i:s');
            if (($user->expired != null && $current_date > $user->expired) || $user->status == 'expired') {
                // Update status menjadi expired jika belum
                if ($user->status != 'expired') {
                    $ci->db->update('account', ['status' => 'expired'], ['id' => $user->id]);
                }
                $ci->session->sess_destroy();
                if ($redirect) {
                    $ci->session->set_flashdata('error', 'Your account has expired.');
                    redirect(base_url('login'));
                }
                return false;
            }
            return true;
        }
        return false;
    } else if (isset($_COOKIE['walix_id']) && isset($_COOKIE['walix_token'])) {
        $id = $_COOKIE['walix_id'];
        $token = $_COOKIE['walix_token'];
        $db = $ci->db->query("SELECT * FROM account WHERE id = '$id'")->row();
        if ($db) {
            // Periksa status expired untuk cookie login
            $current_date = date('Y-m-d H:i:s');
            if (($db->expired != null && $current_date > $db->expired) || $db->status == 'expired') {
                // Update status menjadi expired jika belum
                if ($db->status != 'expired') {
                    $ci->db->update('account', ['status' => 'expired'], ['id' => $db->id]);
                }
                // Hapus cookie
                setcookie('walix_token', NULL, -1, '/');
                setcookie('walix_id', NULL, -1, '/');
                if ($redirect) {
                    $ci->session->set_flashdata('error', 'Your account has expired.');
                    redirect(base_url('login'));
                }
                return false;
            }
            
            if ($token === hash('ripemd160', $db->password)) {
                $ci->session->set_userdata(array('id_login' => $db->id, 'status_login' => true));
                return true;
            } else {
                if ($redirect) {
                    redirect(base_url('login'));
                } else {
                    return false;
                }
            }
        } else {
            if ($redirect) {
                redirect(base_url('login'));
            } else {
                return false;
            }
        }
    } else {
        if ($redirect) {
            redirect(base_url('login'));
        } else {
            return false;
        }
    }
}

function view($view, $data = [])
{
    $ci = &get_instance();
    $data['user'] = $ci->db->get_where('account', ['id' => $ci->session->userdata('id_login')])->row();
    
    // Periksa apakah user sudah expired berdasarkan tanggal
    $current_date = date('Y-m-d H:i:s');
    if ($data['user']->expired != null && $current_date > $data['user']->expired) {
        // Update status menjadi expired
        $ci->db->update('account', ['status' => 'expired'], ['id' => $data['user']->id]);
        $ci->session->set_flashdata('error', 'Your account has expired.');
        redirect(base_url('logout'));
    } else if ($data['user']->status == 'expired') {
        $ci->session->set_flashdata('error', 'Your account has expired.');
        redirect(base_url('logout'));
    } else if ($data['user']->status == 'inactive') {
        $ci->session->set_flashdata('error', 'Your account has not been activated.');
        redirect(base_url('logout'));
    }
    
    $ci->load->view($view, $data);
}

function _assets($url = '')
{
    if ($url) {
        return base_url("assets/$url");
    } else {
        return base_url("assets");
    }
}

function _alert()
{
    $ci = &get_instance();
    if ($ci->session->flashdata('success')) {
        $message = $ci->session->flashdata('success');
        // Hapus flash data
        $ci->session->unset_userdata('success');
        return '<div class="alert alert-success alert-style-light" role="alert">
                    <button type="button" class="close" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    ' . $message . '
                </div>';
    } else if ($ci->session->flashdata('error')) {
        $message = $ci->session->flashdata('error');
        // Hapus flash data
        $ci->session->unset_userdata('error');
        return '<div class="alert alert-danger alert-style-light" role="alert">
                    <button type="button" class="close" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    ' . $message . '
                </div>';
    }
    return '';
}

function _POST($par)
{
    $ci = &get_instance();
    $par = $ci->input->post($par);
    $par = htmlspecialchars($par);
    $par = str_replace("'", "", $par);
    return $par;
}

function _GET($par)
{
    $ci = &get_instance();
    $par = $ci->input->get($par);
    $par = htmlspecialchars($par);
    $par = str_replace("'", "", $par);
    return $par;
}

function _uploadMedia($reqired = false)
{
    if ($reqired == false) {
        return '                        <span class="d-flex justify-content-center mt-3 text-center" id="live_preview">
    <button onclick="mediamodal()" type="button" class="btn btn-primary">Upload Media</button>
    </span>
    <input type="hidden" id="inputmedia" name="media">';
    } else {
        return '                        <span class="d-flex justify-content-center mt-3 text-center" id="live_preview">
    <button onclick="mediamodal()" type="button" class="btn btn-primary">Upload Media</button>
    </span>
    <div class="text-center">
        <small>Required to be filled in.</small>
    </div>
    <input type="hidden" id="inputmedia" name="media" required>';
    }
}

function _storage()
{
    return base_url("storage/");
}

function uploadimg($par = [])
{
    $ci = &get_instance();
    $config['upload_path'] = "$par[path]";
    $config['allowed_types'] = 'jpg|png|jpeg|pdf';
    $config['encrypt_name'] = TRUE;
    $ci->load->library('upload', $config);
    $ci->upload->initialize($config);

    if (!empty($_FILES["$par[name]"]['name'])) {
        if ($ci->upload->do_upload("$par[name]")) {
            $img = $ci->upload->data();
            if ($par['compress'] == true) {
                //Compress Image
                $config['image_library'] = 'gd2';
                $config['source_image'] = "$par[path]" . $img['file_name'];
                $config['create_thumb'] = FALSE;
                $config['maintain_ratio'] = FALSE;
                $config['quality'] = FALSE;
                $config['width'] = $par['width'];
                $config['height'] = $par['height'];
                $config['new_image'] = "$par[path]" . $img['file_name'];
                $ci->load->library('image_lib', $config);
                $ci->image_lib->resize();
            }

            $image = $img['file_name'];
            return array('result' => 'success', 'nama_file' => $image, 'error' => '');
        } else {
            return array('result' => 'error', 'file' => '', 'error' => $ci->upload->display_errors());
        }
    } else {
        return array('result' => 'noimg');
    }
}

function SendBlast($sender)
{
    $url = base_node() . "/blast";  // jika instal di local
    $data = [
        "sender" => $sender,
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_URL, $url);
    //  curl_setopt($ch, CURLOPT_TIMEOUT_MS, 10000);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    $result = curl_exec($ch);
    curl_close($ch);
    return json_decode($result, true);
}

// Fungsi untuk memeriksa status koneksi device
function checkDeviceConnection($sender) {
    $CI =& get_instance();
    
    // Periksa status di database
    $device = $CI->db->get_where('device', ['nomor' => $sender])->row();
    if (!$device) {
        return false;
    }
    
    // Coba ping device melalui Node.js server
    $url = base_node() . "/check-connection";
    $data = [
        "sender" => $sender
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_TIMEOUT_MS, 5000); // 5 detik timeout
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    $result = curl_exec($ch);
    curl_close($ch);
    
    $response = json_decode($result, true);
    
    // Jika device tidak terhubung, coba hubungkan kembali
    if (!$response || !$response['status']) {
        // Kirim permintaan untuk menghubungkan kembali
        $url = base_node() . "/reconnect-device";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, 10000); // 10 detik timeout
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $result = curl_exec($ch);
        curl_close($ch);
        
        $reconnectResponse = json_decode($result, true);
        return $reconnectResponse && $reconnectResponse['status'];
    }
    
    return $response && $response['status'];
}

function sendMSG($number, $msg, $sender)
{
    // Periksa koneksi device terlebih dahulu
    if (!checkDeviceConnection($sender)) {
        error_log("Device not connected: $sender");
        return [
            "status" => "false",
            "msg" => "Device not connected"
        ];
    }
    
    $url = base_node() . "/send-message";
    $data = [
        "sender" => $sender,
        "number" => $number,
        "message" => $msg
    ];
    
    error_log("Sending message to $number from $sender: " . json_encode($data));
    
    // Kode curl yang sudah ada
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    $result = curl_exec($ch);
    
    if (curl_errno($ch)) {
        error_log("Curl error: " . curl_error($ch));
    }
    
    curl_close($ch);
    $response = json_decode($result, true);
    error_log("Response from node server: " . json_encode($response));
    
    return $response;
}

function SendBC($sender, $message, $filetype = '', $filename = '', $urll = '')
{
    $url = base_node() . "/send-broadcast";
    //$url = url_wa() . 'send-media';
    $data = [
        'sender' => $sender,
        'message' => $message,
        'url' => $urll,
        'filename' => $filename,
        'filetype' => $filetype,
    ];
    //var_dump($data); die;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    $result = curl_exec($ch);
    curl_close($ch);
    return json_decode($result, true);
}

function sendBTN($number, $sender, $msg, $footer, $btn1, $btn2)
{
    $url = base_node() . "/send-button";
    $data = [
        "sender" => $sender,
        "number" => $number,
        "message" => $msg,
        "footer" => $footer,
        "btn1" => $btn1,
        "btn2" => $btn2
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_URL, $url);
    //  curl_setopt($ch, CURLOPT_TIMEOUT_MS, 10000);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    $result = curl_exec($ch);
    curl_close($ch);
    return json_decode($result, true);
}

function sendMedia($number, $message, $sender, $filetype, $filename, $urll)
{
    $url = base_node() . "/send-media";
    $data = [
        'sender' => $sender,
        'number' => $number,
        'caption' => $message,
        'url' => $urll,
        'filename' => $filename,
        'filetype' => $filetype,
    ];
    //var_dump($data); die;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    $result = curl_exec($ch);
    curl_close($ch);
    return json_decode($result, true);
}

// Fungsi untuk memeriksa apakah user masih memiliki limit pesan
function check_message_limit($user_id) {
    $ci = &get_instance();
    $user = $ci->db->get_where('account', ['id' => $user_id])->row();
    
    // Jika superadmin, tidak perlu cek limit
    if ($user->level == '0') {
        return true;
    }
    
    // Jika limit 0, berarti unlimited
    if ($user->limit_message == 0) {
        return true;
    }
    
    // Cek apakah masih ada sisa limit
    return ($user->used_message < $user->limit_message);
}

// Fungsi untuk menambah jumlah pesan yang sudah digunakan
function increment_message_count($user_id, $count = 1) {
    $ci = &get_instance();
    $ci->db->set('used_message', 'used_message + ' . $count, FALSE);
    $ci->db->where('id', $user_id);
    $ci->db->update('account');
}

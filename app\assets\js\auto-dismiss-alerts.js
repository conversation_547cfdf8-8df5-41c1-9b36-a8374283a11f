$(document).ready(function() {
    // Fungsi untuk menghilangkan semua alert
    function dismissAlerts() {
        $('.alert').fadeOut('slow', function() {
            $(this).remove(); // Hapus elemen dari DOM setelah fade out
        });
    }
    
    // Menghilangkan alert secara otomatis setelah 5 detik (5000ms)
    setTimeout(dismissAlerts, 5000);
    
    // Tambahkan event listener untuk AJAX requests
    $(document).on("ajaxComplete", function() {
        // Menghilangkan alert yang muncul setelah AJAX request
        setTimeout(dismissAlerts, 5000);
    });
    
    // Tambahkan event listener untuk tombol close pada alert
    $(document).on("click", ".alert .close", function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).closest('.alert').fadeOut('fast', function() {
            $(this).remove();
        });
        return false; // Mencegah event bubbling
    });
    
    // Tambahkan event listener langsung ke tombol close yang sudah ada
    $('.alert .close').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).closest('.alert').fadeOut('fast', function() {
            $(this).remove();
        });
        return false;
    });
    
    // Cek setiap 2 detik apakah ada alert baru yang muncul
    setInterval(function() {
        if ($('.alert:visible').length > 0) {
            setTimeout(function() {
                $('.alert:visible').fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000);
        }
    }, 2000);
});





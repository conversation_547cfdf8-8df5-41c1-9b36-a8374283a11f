module.exports = {
  apps: [{
    name: 'walix-gateway',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: true,
    ignore_watch: ['node_modules', 'logs', 'app_node/session'],
    max_memory_restart: '512M',
    env: {
      NODE_ENV: 'development',
      PORT: 7001,
      HOST: '127.0.0.1'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    cwd: '/www/wwwroot/walix'
  }]
};

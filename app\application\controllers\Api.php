<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Api extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        date_default_timezone_set('Asia/Jakarta');
        // Uncomment baris berikut untuk debugging
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
    }

    // Fungsi untuk validasi API key
    private function _validate_api_key($key)
    {
        $cek = $this->db->get_where('account', ['api_key' => $key]);
        if ($cek->num_rows() == 0) {
            return false;
        }
        return $cek->row()->id;
    }

    // Fungsi untuk validasi device
    private function _validate_device($sender, $id_users)
    {
        $cek = $this->db->get_where('device', ['nomor' => $sender, 'pemilik' => $id_users]);
        if ($cek->num_rows() == 0) {
            return false;
        }
        return true;
    }

    // Fungsi untuk mendapatkan parameter dari request
    private function _get_params()
    {
        if ($this->input->method() == 'post') {
            // Coba baca input JSON
            $json_input = file_get_contents('php://input');
            $json_data = json_decode($json_input, true);
            
            // Log untuk debugging
            error_log("Raw JSON input: " . $json_input);
            error_log("Decoded JSON: " . print_r($json_data, true));
            
            // Jika JSON valid, gunakan itu
            if ($json_data) {
                return $json_data;
            }
            
            // Jika bukan JSON, coba form data biasa
            return $this->input->post();
        } else {
            return $this->input->get();
        }
    }

    // Endpoint untuk mengirim pesan teks
    public function send_message()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $sender = isset($params['sender']) ? $params['sender'] : null;
        $number = isset($params['number']) ? $params['number'] : (isset($params['receiver']) ? $params['receiver'] : null);
        $message = isset($params['message']) ? $params['message'] : null;
        
        header('Content-Type: application/json');
        
        // Log parameter untuk debugging
        error_log("API Request: " . json_encode($params));
        
        if (!isset($sender) || !isset($number) || !isset($message) || !isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "Incorrect parameters!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Cek limit pesan
        if (!check_message_limit($id_users)) {
            $ret['status'] = false;
            $ret['msg'] = "You have reached your message sending limit. Please contact administrator.";
            echo json_encode($ret, true);
            exit;
        }
        
        // Kirim pesan
        $res = sendMSG($number, $message, $sender);
        
        // Pastikan selalu ada respons
        if ($res && isset($res['status']) && $res['status'] == "true") {
            $datainsert = [
                'device' => $sender,
                'receiver' => $number,
                'message' => $message,
                'type' => 'api',
                'status' => 'Sent',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->db->insert('reports', $datainsert);
            
            // Tambahkan jumlah pesan yang digunakan
            increment_message_count($id_users);
            
            $ret['status'] = true;
            $ret['msg'] = "Message sent successfully";
        } else {
            $datainsert = [
                'device' => $sender,
                'receiver' => $number,
                'message' => $message,
                'type' => 'api',
                'status' => 'Failed',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->db->insert('reports', $datainsert);
            
            $ret['status'] = false;
            $ret['msg'] = isset($res['msg']) ? $res['msg'] : "Failed to send message";
        }
        
        echo json_encode($ret, true);
        exit;
    }

    public function send_media()
    {
        if ($this->input->post()) {
            // Takes raw data from the request
            $data = json_decode(file_get_contents('php://input'), true);
            $sender = $data['sender'];
            $nomor = $data['number'];
            $caption = $data['message'];
            //$filetype = $data['filetype'];
            $key = $data['api_key'];
            $url = $data['url'];
            header('Content-Type: application/json');
        } else {
            $sender = $this->input->get('sender');
            $nomor = $this->input->get('receiver');
            $caption = $this->input->get('message');
            //$filetype = $data['filetype'];
            $key = $this->input->get('apikey');
            $url = $this->input->get('url');
            header('Content-Type: application/json');
        }

        if (!isset($nomor) ||  !isset($sender) || !isset($key)  || !isset($url)) {
            $ret['status'] = false;
            $ret['msg'] = "Parameter salah!";
            echo json_encode($ret, true);
            exit;
        }

        $a = explode('/', $url);
        $filename = $a[count($a) - 1];
        $a2 = explode('.', $filename);
        $namefile = $a2[count($a2) - 2];
        $ext = $a2[count($a2) - 1];

        if ($ext != 'jpg' && $ext != 'pdf' && $ext != 'png') {
            $ret['status'] = false;
            $ret['msg'] = "Only support jpg and pdf";
            echo json_encode($ret, true);
            exit;
        }

        $cek = $this->db->get_where('account', ['api_key' => $key]);
        if ($cek->num_rows() == 0) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        $id_users = $cek->row()->id;
        $cek2 = $this->db->get_where('device', ['nomor' => $sender, 'pemilik' => $id_users]);
        if ($cek2->num_rows() == 0) {
            $ret['status'] = false;
            $ret['msg'] = "Device not found!";
            echo json_encode($ret, true);
            exit;
        }
        $res = sendMedia($nomor, $caption, $sender, $ext, $namefile, $url);
        if ($res['status'] == "true") {
            $datainsert = [
                'device' => $sender,
                'receiver' => $nomor,
                'message' => $caption,
                'media' => $url,
                'type' => 'api',
                'status' => 'Sent',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->db->insert('reports', $datainsert);
            $ret['status'] = true;
            $ret['msg'] = "Message sent successfully";
            echo json_encode($ret, true);
            exit;
        } else {
            $datainsert = [
                'device' => $sender,
                'receiver' => $nomor,
                'message' => $caption,
                'media' => $url,
                'type' => 'api',
                'status' => 'Failed',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->db->insert('reports', $datainsert);
            $ret['status'] = false;
            $ret['msg'] = 'Device not connected';
            echo json_encode($ret, true);
            exit;
        }
    }

    public function send_button()
    {
        if ($this->input->post()) {
            $data = json_decode(file_get_contents('php://input'), true);
            $sender = $data['sender'];
            $nomor = $data['number'];
            $pesan = $data['message'];
            $footer = $data['footer'];
            $button1 = $data['button1'];
            $button2 = $data['button2'];
            $key = $data['api_key'];
            header('Content-Type: application/json');
        } else {
            $sender = $this->input->get('sender');
            $nomor = $this->input->get('receiver');
            $pesan = $this->input->get('message');
            $footer = $this->input->get('footer');
            $button1 = $this->input->get('btn1');
            $button2 = $this->input->get('btn2');
            $key = $this->input->get('apikey');
            header('Content-Type: application/json');
        }

        if (!isset($nomor) && !isset($pesan) && !isset($sender) && !isset($key) && !isset($button1) && !isset($button2)) {
            $ret['status'] = false;
            $ret['msg'] = "Incorrect parameters!";
            echo json_encode($ret, true);
            exit;
        }

        $cek = $this->db->get_where('account', ['api_key' => $key]);
        if ($cek->num_rows() == 0) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        $id_users = $cek->row()->id;
        $cek2 = $this->db->get_where('device', ['nomor' => $sender, 'pemilik' => $id_users]);
        if ($cek2->num_rows() == 0) {
            $ret['status'] = false;
            $ret['msg'] = "Device not found!";
            echo json_encode($ret, true);
            exit;
        }
        $res = sendBTN($nomor, $sender, $pesan, $footer, $button1, $button2);
        if ($res['status'] == "true") {
            $datainsert = [
                'device' => $sender,
                'receiver' => $nomor,
                'message' => $pesan,
                'footer' => $footer,
                'btn1' => $button1,
                'btn2' => $button2,
                'btnid1' => $button1,
                'btnid2' => $button2,
                'type' => 'api',
                'status' => 'Sent',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->db->insert('reports', $datainsert);
            $ret['status'] = true;
            $ret['msg'] = "Message sent successfully";
            echo json_encode($ret, true);
            exit;
        } else {
            $datainsert = [
                'device' => $sender,
                'receiver' => $nomor,
                'message' => $pesan,
                'footer' => $footer,
                'btn1' => $button1,
                'btn2' => $button2,
                'btnid1' => $button1,
                'btnid2' => $button2,
                'type' => 'api',
                'status' => 'Failed',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->db->insert('reports', $datainsert);
            $ret['status'] = false;
            $ret['msg'] = 'Device not connected';
            echo json_encode($ret, true);
            exit;
        }
    }

    public function callback()
    {
        header('content-type: application/json');
        $data = json_decode(file_get_contents('php://input'), true);
        $sender =  preg_replace("/\D/", "", $data['id']);
        foreach ($data['data'] as $d) {
            $number = str_replace("@s.whatsapp.net", "", $d['id']);
            $nama = $d['name'];
            $cek = $this->db->get_where('all_contacts', ['sender' => $sender, 'number' => $number]);
            if ($cek->num_rows() == 0) {
                $this->db->query("INSERT INTO all_contacts VALUES(null,'$sender','$number','$nama','Personal')");
            }
        }
    }

    // ENDPOINT BARU MULAI DARI SINI

    // Endpoint untuk mendapatkan status device
    public function get_device_status()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $sender = isset($params['sender']) ? $params['sender'] : null;
        
        header('Content-Type: application/json');
        
        if (!isset($sender) || !isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "Incorrect parameters!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        $device = $this->db->get_where('device', ['nomor' => $sender, 'pemilik' => $id_users]);
        if ($device->num_rows() == 0) {
            $ret['status'] = false;
            $ret['msg'] = "Device not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        $device_data = $device->row();
        $ret['status'] = true;
        $ret['data'] = [
            'id' => $device_data->id,
            'number' => $device_data->nomor,
            'name' => $device_data->nama,
            'status' => $device_data->status == 1 ? 'connected' : 'disconnected',
            'created_at' => $device_data->created_at
        ];
        
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk mendapatkan daftar device
    public function get_devices()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        
        header('Content-Type: application/json');
        
        if (!isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "API Key is required!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        $devices = $this->db->get_where('device', ['pemilik' => $id_users])->result();
        $device_list = [];
        
        foreach ($devices as $device) {
            $device_list[] = [
                'id' => $device->id,
                'number' => $device->nomor,
                'name' => $device->nama,
                'status' => $device->status == 1 ? 'connected' : 'disconnected',
                'created_at' => $device->created_at
            ];
        }
        
        $ret['status'] = true;
        $ret['data'] = $device_list;
        
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk mendapatkan daftar kontak
    public function get_contacts()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $sender = isset($params['sender']) ? $params['sender'] : null;
        
        header('Content-Type: application/json');
        
        if (!isset($sender) || !isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "Incorrect parameters!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        if (!$this->_validate_device($sender, $id_users)) {
            $ret['status'] = false;
            $ret['msg'] = "Device not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        $contacts = $this->db->get_where('all_contacts', ['sender' => $sender])->result();
        $contact_list = [];
        
        foreach ($contacts as $contact) {
            $contact_list[] = [
                'id' => $contact->id,
                'number' => $contact->number,
                'name' => $contact->name,
                'type' => $contact->type
            ];
        }
        
        $ret['status'] = true;
        $ret['data'] = $contact_list;
        
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk mendapatkan riwayat pesan
    public function get_messages()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $sender = isset($params['sender']) ? $params['sender'] : null;
        $limit = isset($params['limit']) ? intval($params['limit']) : 50;
        
        header('Content-Type: application/json');
        
        if (!isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "API Key is required!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        $this->db->limit($limit);
        $this->db->order_by('created_at', 'DESC');
        
        if ($sender) {
            if (!$this->_validate_device($sender, $id_users)) {
                $ret['status'] = false;
                $ret['msg'] = "Device not found!";
                echo json_encode($ret, true);
                exit;
            }
            $messages = $this->db->get_where('reports', ['device' => $sender])->result();
        } else {
            // Get all devices owned by this user
            $devices = $this->db->get_where('device', ['pemilik' => $id_users])->result();
            $device_numbers = [];
            foreach ($devices as $device) {
                $device_numbers[] = $device->nomor;
            }
            
            if (empty($device_numbers)) {
                $ret['status'] = false;
                $ret['msg'] = "No devices found for this user!";
                echo json_encode($ret, true);
                exit;
            }
            
            $this->db->where_in('device', $device_numbers);
            $messages = $this->db->get('reports')->result();
        }
        
        $message_list = [];
        foreach ($messages as $message) {
            $message_data = [
                'id' => $message->id,
                'device' => $message->device,
                'receiver' => $message->receiver,
                'message' => $message->message,
                'type' => $message->type,
                'status' => $message->status,
                'created_at' => $message->created_at
            ];
            
            // Add media if exists
            if (!empty($message->media)) {
                $message_data['media'] = $message->media;
            }
            
            // Add button data if exists
            if (!empty($message->footer) || !empty($message->btn1) || !empty($message->btn2)) {
                $message_data['button_data'] = [
                    'footer' => $message->footer,
                    'button1' => $message->btn1,
                    'button2' => $message->btn2
                ];
            }
            
            $message_list[] = $message_data;
        }
        
        $ret['status'] = true;
        $ret['data'] = $message_list;
        
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk mengirim pesan ke banyak nomor (broadcast)
    public function broadcast()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $sender = isset($params['sender']) ? $params['sender'] : null;
        $numbers = isset($params['numbers']) ? $params['numbers'] : null;
        $message = isset($params['message']) ? $params['message'] : null;
        
        header('Content-Type: application/json');
        
        if (!isset($sender) || !isset($numbers) || !isset($message) || !isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "Incorrect parameters! Required: sender, numbers (array or comma-separated), message, api_key";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Konversi numbers ke array jika string
        if (!is_array($numbers)) {
            $numbers = explode(',', $numbers);
        }
        
        // Hitung jumlah pesan yang akan dikirim
        $message_count = count($numbers);
        
        // Cek limit pesan
        $user = $this->db->get_where('account', ['id' => $id_users])->row();
        if ($user->level != '0' && $user->limit_message > 0 && ($user->used_message + $message_count) > $user->limit_message) {
            $ret['status'] = false;
            $ret['msg'] = "This action would exceed your message limit. You can send " . ($user->limit_message - $user->used_message) . " more messages.";
            echo json_encode($ret, true);
            exit;
        }
        
        $results = [];
        $success_count = 0;
        $failed_count = 0;
        
        foreach ($numbers as $number) {
            $number = trim($number);
            $res = sendMSG($number, $message, $sender);
            
            if ($res['status'] == "true") {
                $datainsert = [
                    'device' => $sender,
                    'receiver' => $number,
                    'message' => $message,
                    'type' => 'api_broadcast',
                    'status' => 'Sent',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $this->db->insert('reports', $datainsert);
                $results[$number] = ['status' => true, 'msg' => 'Message sent successfully'];
                $success_count++;
            } else {
                $datainsert = [
                    'device' => $sender,
                    'receiver' => $number,
                    'message' => $message,
                    'type' => 'api_broadcast',
                    'status' => 'Failed',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $this->db->insert('reports', $datainsert);
                $results[$number] = ['status' => false, 'msg' => 'Failed to send message'];
                $failed_count++;
            }
        }
        
        $ret['status'] = $success_count > 0;
        $ret['summary'] = [
            'total' => count($numbers),
            'success' => $success_count,
            'failed' => $failed_count
        ];
        $ret['details'] = $results;
        
        // Setelah berhasil mengirim, tambahkan jumlah pesan yang digunakan
        increment_message_count($id_users, $message_count);
        
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk memeriksa status pesan
    public function check_message_status()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $message_id = isset($params['message_id']) ? $params['message_id'] : null;
        
        header('Content-Type: application/json');
        
        if (!isset($message_id) || !isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "Incorrect parameters! Required: message_id, api_key";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        $message = $this->db->get_where('reports', ['id' => $message_id])->row();
        
        if (!$message) {
            $ret['status'] = false;
            $ret['msg'] = "Message not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Check if this message belongs to one of the user's devices
        $device = $this->db->get_where('device', ['nomor' => $message->device, 'pemilik' => $id_users]);
        if ($device->num_rows() == 0) {
            $ret['status'] = false;
            $ret['msg'] = "You don't have permission to access this message!";
            echo json_encode($ret, true);
            exit;
        }
        
        $ret['status'] = true;
        $ret['data'] = [
            'id' => $message->id,
            'device' => $message->device,
            'receiver' => $message->receiver,
            'message' => $message->message,
            'type' => $message->type,
            'status' => $message->status,
            'created_at' => $message->created_at
        ];
        
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk webhook (menerima pesan masuk)
    public function webhook()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $url = isset($params['url']) ? $params['url'] : null;
        $events = isset($params['events']) ? $params['events'] : 'all';
        
        header('Content-Type: application/json');
        
        if (!isset($key) || !isset($url)) {
            $ret['status'] = false;
            $ret['msg'] = "Incorrect parameters! Required: api_key, url";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Validate URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            $ret['status'] = false;
            $ret['msg'] = "Invalid URL format!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Check if webhook already exists
        $webhook = $this->db->get_where('webhooks', ['user_id' => $id_users])->row();
        
        if ($webhook) {
            // Update existing webhook
            $this->db->where('id', $webhook->id);
            $this->db->update('webhooks', [
                'url' => $url,
                'events' => $events,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } else {
            // Create new webhook
            $this->db->insert('webhooks', [
                'user_id' => $id_users,
                'url' => $url,
                'events' => $events,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        $ret['status'] = true;
        $ret['msg'] = "Webhook configured successfully!";
        echo json_encode($ret, true);
        exit;
    }
    
    // Endpoint untuk menghapus webhook
    public function delete_webhook()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        
        header('Content-Type: application/json');
        
        if (!isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "API Key is required!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Delete webhook
        $this->db->where('user_id', $id_users);
        $this->db->delete('webhooks');
        
        $ret['status'] = true;
        $ret['msg'] = "Webhook deleted successfully!";
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk mendapatkan pesan masuk
    public function messages()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $sender = isset($params['sender']) ? $params['sender'] : null;
        $limit = isset($params['limit']) ? intval($params['limit']) : 50;
        
        header('Content-Type: application/json');
        
        if (!isset($key)) {
            $ret['status'] = false;
            $ret['msg'] = "API Key is required!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Dapatkan perangkat yang dimiliki oleh pengguna
        $devices = $this->db->get_where('device', ['pemilik' => $id_users])->result();
        $device_numbers = [];
        
        foreach ($devices as $device) {
            $device_numbers[] = $device->nomor;
        }
        
        if (empty($device_numbers)) {
            $ret['status'] = false;
            $ret['msg'] = "No devices found for this user!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Buat query untuk mendapatkan pesan
        $this->db->select('*');
        $this->db->from('messages');
        
        if ($sender) {
            // Jika sender ditentukan, filter berdasarkan sender
            $this->db->where('sender', $sender);
            $this->db->where_in('receiver', $device_numbers);
        } else {
            // Jika tidak, ambil semua pesan untuk semua perangkat pengguna
            $this->db->where_in('receiver', $device_numbers);
        }
        
        $this->db->order_by('timestamp', 'DESC');
        $this->db->limit($limit);
        
        $messages = $this->db->get()->result();
        
        $ret['status'] = true;
        $ret['msg'] = "Messages retrieved successfully";
        $ret['data'] = $messages;
        
        echo json_encode($ret, true);
        exit;
    }

    // Endpoint untuk memeriksa status pesan
    public function message_status()
    {
        $params = $this->_get_params();
        $key = isset($params['apikey']) ? $params['apikey'] : $params['api_key'];
        $message_id = isset($params['message_id']) ? $params['message_id'] : null;
        
        header('Content-Type: application/json');
        
        if (!isset($key) || !isset($message_id)) {
            $ret['status'] = false;
            $ret['msg'] = "API Key and message_id are required!";
            echo json_encode($ret, true);
            exit;
        }
        
        $id_users = $this->_validate_api_key($key);
        if (!$id_users) {
            $ret['status'] = false;
            $ret['msg'] = "Api Key is wrong/not found!";
            echo json_encode($ret, true);
            exit;
        }
        
        // Cari pesan di tabel pesan
        $message = $this->db->get_where('pesan', ['id' => $message_id])->row();
        
        if (!$message) {
            // Cari di tabel blast
            $message = $this->db->get_where('blast', ['id' => $message_id])->row();
            
            if (!$message) {
                $ret['status'] = false;
                $ret['msg'] = "Message not found!";
                echo json_encode($ret, true);
                exit;
            }
        }
        
        // Verifikasi bahwa pesan ini milik pengguna yang bersangkutan
        $device = $this->db->get_where('device', ['nomor' => $message->sender, 'pemilik' => $id_users])->row();
        
        if (!$device) {
            $ret['status'] = false;
            $ret['msg'] = "You don't have permission to access this message!";
            echo json_encode($ret, true);
            exit;
        }
        
        $ret['status'] = true;
        $ret['msg'] = "Message status retrieved successfully";
        $ret['data'] = [
            'id' => $message->id,
            'status' => $message->status
        ];
        
        echo json_encode($ret, true);
        exit;
    }
}

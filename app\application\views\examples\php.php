<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WALIX :: <?= $title ?></title>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/highlight/styles/github-gist.css" rel="stylesheet">
    <link href="<?= _assets() ?>/css/main.min.css" rel="stylesheet">
    <link href="<?= _assets() ?>/css/custom.css" rel="stylesheet">
</head>

<body>
    <?php require_once('application/views/include_head.php') ?>

    <div class="app-container">
        <div class="app-header">
            <nav class="navbar navbar-light navbar-expand-lg">
                <div class="container-fluid">
                    <div class="navbar-nav" id="navbarNav">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link hide-sidebar-toggle-button" href="#"><i class="material-icons">first_page</i></a>
                            </li>
                        </ul>
                    </div>
                    <div class="d-flex">
                        <ul class="navbar-nav">
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
        <div class="app-content">
            <div class="content-wrapper">
                <div class="container">
                    <div class="row">
                        <div class="col">
                            <div class="page-description p-0">
                                <h4><?= $title ?></h4>
                            </div>
                        </div>
                    </div>
                    <?= _alert() ?>
                    
                    <div class="card">
                        <div class="card-body">
                            <h5>Send Text Message</h5>
                            <div class="example-container">
                                <div class="example-code">
                                    <pre><code class="php"><?php
echo htmlspecialchars('<?php
// Contoh mengirim notifikasi teks sederhana

// Konfigurasi
$api_url = \'https://domain-anda.com/api/send-message\';
$api_key = \'' . $user->api_key . '\';
$sender = \'628xxxxxxxxx\'; // Nomor WhatsApp yang sudah terhubung (scan QR)
$recipient = \'628xxxxxxxxx\'; // Nomor penerima notifikasi

// Pesan notifikasi
$message = "Halo! Ini adalah notifikasi dari sistem Anda.";

// Metode POST dengan JSON
$data = [
    \'api_key\' => $api_key,
    \'sender\' => $sender,
    \'number\' => $recipient, // Perhatikan: parameter \'number\' untuk POST
    \'message\' => $message
];

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => \'\',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => \'POST\',
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [\'Content-Type: application/json\']
]);

$response = curl_exec($curl);
curl_close($curl);

// Proses response
$result = json_decode($response, true);
if ($result[\'status\']) {
    echo "Notifikasi berhasil dikirim!";
} else {
    echo "Gagal mengirim notifikasi: " . $result[\'msg\'];
}');
?></code></pre>
                                </div>
                            </div>
                            
                            <h5 class="mt-4">Send Media Message</h5>
                            <div class="example-container">
                                <div class="example-code">
                                    <pre><code class="php"><?php
echo htmlspecialchars('<?php
// Contoh mengirim notifikasi dengan gambar

// Konfigurasi
$api_url = \'https://domain-anda.com/api/send-media\';
$api_key = \'' . $user->api_key . '\';
$sender = \'628xxxxxxxxx\'; // Nomor WhatsApp yang sudah terhubung
$recipient = \'628xxxxxxxxx\'; // Nomor penerima notifikasi

// Pesan dan media
$caption = "Notifikasi dengan gambar dari sistem Anda.";
$image_url = "https://example.com/path/to/image.jpg"; // URL gambar yang dapat diakses publik

// Metode POST dengan JSON
$data = [
    \'api_key\' => $api_key,
    \'sender\' => $sender,
    \'number\' => $recipient,
    \'message\' => $caption,
    \'url\' => $image_url
];

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => \'\',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => \'POST\',
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [\'Content-Type: application/json\']
]);

$response = curl_exec($curl);
curl_close($curl);

// Proses response
$result = json_decode($response, true);
if ($result[\'status\']) {
    echo "Notifikasi dengan gambar berhasil dikirim!";
} else {
    echo "Gagal mengirim notifikasi: " . $result[\'msg\'];
}');
?></code></pre>
                                </div>
                            </div>
                            
                            <h5 class="mt-4">Send Button Message</h5>
                            <div class="example-container">
                                <div class="example-code">
                                    <pre><code class="php"><?php
echo htmlspecialchars('<?php
// Contoh mengirim notifikasi dengan tombol interaktif

// Konfigurasi
$api_url = \'https://domain-anda.com/api/send-button\';
$api_key = \'' . $user->api_key . '\';
$sender = \'628xxxxxxxxx\'; // Nomor WhatsApp yang sudah terhubung
$recipient = \'628xxxxxxxxx\'; // Nomor penerima notifikasi

// Pesan dan tombol
$message = "Pesanan #12345 telah selesai diproses.";
$footer = "Silakan pilih opsi berikut:";
$button1 = "Konfirmasi";
$button2 = "Hubungi CS";

// Metode POST dengan JSON
$data = [
    \'api_key\' => $api_key,
    \'sender\' => $sender,
    \'number\' => $recipient,
    \'message\' => $message,
    \'footer\' => $footer,
    \'button1\' => $button1,
    \'button2\' => $button2
];

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => \'\',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => \'POST\',
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [\'Content-Type: application/json\']
]);

$response = curl_exec($curl);
curl_close($curl);

// Proses response
$result = json_decode($response, true);
if ($result[\'status\']) {
    echo "Notifikasi dengan tombol berhasil dikirim!";
} else {
    echo "Gagal mengirim notifikasi: " . $result[\'msg\'];
}');
?></code></pre>
                                </div>
                            </div>
                            
                            <h5 class="mt-4">Send Broadcast</h5>
                            <div class="example-container">
                                <div class="example-code">
                                    <pre><code class="php"><?php
echo htmlspecialchars('<?php
// Contoh mengirim broadcast ke banyak penerima sekaligus

// Konfigurasi
$api_url = \'https://domain-anda.com/api/broadcast\';
$api_key = \'' . $user->api_key . '\';
$sender = \'628xxxxxxxxx\'; // Nomor WhatsApp yang sudah terhubung

// Daftar penerima (bisa dari database)
$recipients = [
    \'628xxxxxxxx1\',
    \'628xxxxxxxx2\',
    \'628xxxxxxxx3\'
];

// Pesan notifikasi
$message = "Pengumuman penting: Sistem akan maintenance pada tanggal 15 Juli 2023.";

// Metode POST dengan JSON
$data = [
    \'api_key\' => $api_key,
    \'sender\' => $sender,
    \'numbers\' => $recipients, // Array nomor penerima
    \'message\' => $message
];

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => \'\',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => \'POST\',
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [\'Content-Type: application/json\']
]);

$response = curl_exec($curl);
curl_close($curl);

// Proses response
$result = json_decode($response, true);
if ($result[\'status\']) {
    echo "Broadcast berhasil dikirim ke " . count($recipients) . " penerima!";
} else {
    echo "Gagal mengirim broadcast: " . $result[\'msg\'];
}');
?></code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Javascripts -->
    <script src="<?= _assets() ?>/plugins/jquery/jquery-3.5.1.min.js"></script>
    <script src="<?= _assets() ?>/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="<?= _assets() ?>/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
    <script src="<?= _assets() ?>/plugins/pace/pace.min.js"></script>
    <script src="<?= _assets() ?>/plugins/highlight/highlight.pack.js"></script>
    <script src="<?= _assets() ?>/js/main.min.js"></script>
    <script src="<?= _assets() ?>/js/custom.js"></script>
    <script>
        $(document).ready(function() {
            $('pre code').each(function(i, block) {
                hljs.highlightBlock(block);
            });
        });
    </script>
</body>

</html>
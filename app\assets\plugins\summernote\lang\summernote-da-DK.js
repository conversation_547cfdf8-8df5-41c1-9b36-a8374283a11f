/*!
 * 
 * Super simple wysiwyg editor v0.8.18
 * https://summernote.org
 * 
 * 
 * Copyright 2013- <PERSON> and other contributors
 * summernote may be freely distributed under the MIT license.
 * 
 * Date: 2020-05-20T18:09Z
 * 
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(window, function() {
return /******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 12);
/******/ })
/************************************************************************/
/******/ ({

/***/ 12:
/***/ (function(module, exports) {

(function ($) {
  $.extend($.summernote.lang, {
    'da-DK': {
      font: {
        bold: 'Fed',
        italic: 'Kursiv',
        underline: 'Understreget',
        clear: 'Fjern formatering',
        height: 'Højde',
        name: 'Skrifttype',
        strikethrough: 'Gennemstreget',
        subscript: 'Sænket skrift',
        superscript: 'Hævet skrift',
        size: 'Skriftstørrelse'
      },
      image: {
        image: 'Billede',
        insert: 'Indsæt billede',
        resizeFull: 'Original størrelse',
        resizeHalf: 'Halv størrelse',
        resizeQuarter: 'Kvart størrelse',
        floatLeft: 'Venstrestillet',
        floatRight: 'Højrestillet',
        floatNone: 'Fjern formatering',
        shapeRounded: 'Form: Runde kanter',
        shapeCircle: 'Form: Cirkel',
        shapeThumbnail: 'Form: Miniature',
        shapeNone: 'Form: Ingen',
        dragImageHere: 'Træk billede hertil',
        dropImage: 'Slip billede',
        selectFromFiles: 'Vælg billed-fil',
        maximumFileSize: 'Maks fil størrelse',
        maximumFileSizeError: 'Filen er større end maks tilladte fil størrelse!',
        url: 'Billede URL',
        remove: 'Fjern billede',
        original: 'Original'
      },
      video: {
        video: 'Video',
        videoLink: 'Video Link',
        insert: 'Indsæt Video',
        url: 'Video URL?',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)'
      },
      link: {
        link: 'Link',
        insert: 'Indsæt link',
        unlink: 'Fjern link',
        edit: 'Rediger',
        textToDisplay: 'Visningstekst',
        url: 'Hvor skal linket pege hen?',
        openInNewWindow: 'Åbn i nyt vindue'
      },
      table: {
        table: 'Tabel',
        addRowAbove: 'Tilføj række over',
        addRowBelow: 'Tilføj række under',
        addColLeft: 'Tilføj venstre kolonne',
        addColRight: 'Tilføj højre kolonne',
        delRow: 'Slet række',
        delCol: 'Slet kolonne',
        delTable: 'Slet tabel'
      },
      hr: {
        insert: 'Indsæt horisontal linje'
      },
      style: {
        style: 'Stil',
        p: 'p',
        blockquote: 'Citat',
        pre: 'Kode',
        h1: 'Overskrift 1',
        h2: 'Overskrift 2',
        h3: 'Overskrift 3',
        h4: 'Overskrift 4',
        h5: 'Overskrift 5',
        h6: 'Overskrift 6'
      },
      lists: {
        unordered: 'Punktopstillet liste',
        ordered: 'Nummereret liste'
      },
      options: {
        help: 'Hjælp',
        fullscreen: 'Fuld skærm',
        codeview: 'HTML-Visning'
      },
      paragraph: {
        paragraph: 'Afsnit',
        outdent: 'Formindsk indryk',
        indent: 'Forøg indryk',
        left: 'Venstrestillet',
        center: 'Centreret',
        right: 'Højrestillet',
        justify: 'Blokjuster'
      },
      color: {
        recent: 'Nyligt valgt farve',
        more: 'Flere farver',
        background: 'Baggrund',
        foreground: 'Forgrund',
        transparent: 'Transparent',
        setTransparent: 'Sæt transparent',
        reset: 'Nulstil',
        resetToDefault: 'Gendan standardindstillinger'
      },
      shortcut: {
        shortcuts: 'Genveje',
        close: 'Luk',
        textFormatting: 'Tekstformatering',
        action: 'Handling',
        paragraphFormatting: 'Afsnitsformatering',
        documentStyle: 'Dokumentstil',
        extraKeys: 'Extra keys'
      },
      help: {
        'insertParagraph': 'Indsæt paragraf',
        'undo': 'Undoes the last command',
        'redo': 'Redoes the last command',
        'tab': 'Tab',
        'untab': 'Untab',
        'bold': 'Set a bold style',
        'italic': 'Set a italic style',
        'underline': 'Set a underline style',
        'strikethrough': 'Set a strikethrough style',
        'removeFormat': 'Clean a style',
        'justifyLeft': 'Set left align',
        'justifyCenter': 'Set center align',
        'justifyRight': 'Set right align',
        'justifyFull': 'Set full align',
        'insertUnorderedList': 'Toggle unordered list',
        'insertOrderedList': 'Toggle ordered list',
        'outdent': 'Outdent on current paragraph',
        'indent': 'Indent on current paragraph',
        'formatPara': 'Change current block\'s format as a paragraph(P tag)',
        'formatH1': 'Change current block\'s format as H1',
        'formatH2': 'Change current block\'s format as H2',
        'formatH3': 'Change current block\'s format as H3',
        'formatH4': 'Change current block\'s format as H4',
        'formatH5': 'Change current block\'s format as H5',
        'formatH6': 'Change current block\'s format as H6',
        'insertHorizontalRule': 'Insert horizontal rule',
        'linkDialog.show': 'Vis Link Dialog'
      },
      history: {
        undo: 'Fortryd',
        redo: 'Annuller fortryd'
      },
      specialChar: {
        specialChar: 'SPECIAL CHARACTERS',
        select: 'Vælg special karakterer'
      }
    }
  });
})(jQuery);

/***/ })

/******/ });
});
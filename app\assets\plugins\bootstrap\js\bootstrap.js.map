{"version": 3, "file": "bootstrap.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/scrollbar.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = '#' + hrefAttr.split('#')[1]\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = (name, plugin) => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nexport {\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta3'\n\nclass BaseComponent {\n  constructor(element) {\n    element = typeof element === 'string' ? document.querySelector(element) : element\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    if (!element.classList.contains(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, 'transitionend', () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, 'transitionend', () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_RIGHT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_RIGHT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_NEXT ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_NEXT ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event) {\n      if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n        return\n      }\n\n      if (/input|select|textarea|form/i.test(event.target.tagName)) {\n        return\n      }\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event) {\n        // Don't close the menu if the clicked element or one of its parents is the dropdown button\n        if ([context._element].some(element => event.composedPath().includes(element))) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu shouldn't close the menu\n        if (event.type === 'keyup' && event.key === TAB_KEY && dropdownMenu.contains(event.target)) {\n          continue\n        }\n      }\n\n      const hideEvent = EventHandler.trigger(toggles[i], EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      Manipulator.removeDataAttribute(dropdownMenu, 'popper')\n      EventHandler.trigger(toggles[i], EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, 'transitionend', transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const isAnimated = this._isAnimated()\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (isAnimated) {\n        this._backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (isAnimated) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!isAnimated) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, 'transitionend', callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (isAnimated) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, 'transitionend', callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL()) || (this._isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!this._isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n      this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - this._scrollbarWidth)\n      this._setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    SelectorEngine.find(selector)\n      .forEach(element => {\n        if (element !== document.body && window.innerWidth > element.clientWidth + this._scrollbarWidth) {\n          return\n        }\n\n        const actualValue = element.style[styleProp]\n        const calculatedValue = window.getComputedStyle(element)[styleProp]\n        Manipulator.setDataAttribute(element, styleProp, actualValue)\n        element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n      })\n  }\n\n  _resetScrollbar() {\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n    this._resetElementAttributes('body', 'paddingRight')\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    SelectorEngine.find(selector).forEach(element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined' && element === document.body) {\n        element.style[styleProp] = ''\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    })\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.get(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  document.body.style.overflow = 'hidden'\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n    })\n}\n\nconst reset = () => {\n  document.body.style.overflow = 'auto'\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  _resetElementAttributes('body', 'paddingRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined' && element === document.body) {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_BACKDROP_BODY = 'offcanvas-backdrop'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_TOGGLING = 'offcanvas-toggling'\nconst OPEN_SELECTOR = '.offcanvas.show'\nconst ACTIVE_SELECTOR = `${OPEN_SELECTOR}, .${CLASS_NAME_TOGGLING}`\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    if (this._config.backdrop) {\n      document.body.classList.add(CLASS_NAME_BACKDROP_BODY)\n    }\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n      this._enforceFocusOnElement(this._element)\n    }\n\n    setTimeout(completeCallBack, getTransitionDurationFromElement(this._element))\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (this._config.backdrop) {\n        document.body.classList.remove(CLASS_NAME_BACKDROP_BODY)\n      }\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n    }\n\n    setTimeout(completeCallback, getTransitionDurationFromElement(this._element))\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(document, 'keydown', event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n\n    EventHandler.on(document, EVENT_CLICK_DATA_API, event => {\n      const target = SelectorEngine.findOne(getSelectorFromElement(event.target))\n      if (!this._element.contains(event.target) && target !== this._element) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(ACTIVE_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    return\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(NAME, Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this.config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this.config.placement === 'function' ?\n      this.config.placement.call(this, tip, this._element) :\n      this.config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const container = this._getContainer()\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop())\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(this.tip)\n      EventHandler.one(this.tip, 'transitionend', complete)\n      emulateTransitionEnd(this.tip, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, 'transitionend', complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this.config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            altBoundary: true,\n            fallbackPlacements: this.config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this.config.popperConfig === 'function' ? this.config.popperConfig(defaultBsPopperConfig) : this.config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      isDisabled(this._element)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, 'transitionend', complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  <PERSON><PERSON>,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "onDOMContentLoaded", "callback", "readyState", "isRTL", "dir", "defineJQueryPlugin", "name", "plugin", "$", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "elementMap", "Map", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "isNative", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "VERSION", "BaseComponent", "constructor", "_element", "Data", "DATA_KEY", "dispose", "getInstance", "NAME", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "handle<PERSON><PERSON><PERSON>", "alertInstance", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_INDICATOR", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "slideEvent", "carouselInterface", "action", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_NAVBAR", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "modifiers", "modifier", "enabled", "createPopper", "focus", "hideEvent", "destroy", "update", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "map", "popperData", "defaultBsPopperConfig", "placement", "options", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "some", "<PERSON><PERSON><PERSON>", "dataApiKeydownHandler", "stopPropagation", "click", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_isAnimated", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "isAnimated", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_setElementAttributes", "calculatedValue", "styleProp", "clientWidth", "actualValue", "_resetElementAttributes", "scrollDiv", "scrollbarWidth", "width", "getWidth", "documentWidth", "overflow", "reset", "removeProperty", "scroll", "CLASS_NAME_BACKDROP_BODY", "CLASS_NAME_TOGGLING", "OPEN_SELECTOR", "ACTIVE_SELECTOR", "<PERSON><PERSON><PERSON>", "scrollBarHide", "completeCallBack", "_enforceFocusOnElement", "blur", "completeCallback", "scrollBarReset", "allReadyOpen", "el", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacements", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "Error", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "altBoundary", "phase", "_handlePopperPlacementChange", "onFirstUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "state", "popper", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMA,OAAO,GAAG,OAAhB;EACA,MAAMC,uBAAuB,GAAG,IAAhC;EACA,MAAMC,cAAc,GAAG,eAAvB;;EAGA,MAAMC,MAAM,GAAGC,GAAG,IAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,WAAQ,GAAED,GAAI,EAAd;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;;;EAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;EACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EAQA,MAAMM,WAAW,GAAGC,OAAO,IAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAIE,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAf,CADiC;EAIjC;EACA;EACA;;EACA,QAAI,CAACC,QAAD,IAAc,CAACA,QAAQ,CAACC,QAAT,CAAkB,GAAlB,CAAD,IAA2B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA9C,EAAyE;EACvE,aAAO,IAAP;EACD,KATgC;;;EAYjC,QAAIF,QAAQ,CAACC,QAAT,CAAkB,GAAlB,KAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoB,GAApB,CAA/B,EAAyD;EACvDF,MAAAA,QAAQ,GAAG,MAAMA,QAAQ,CAACG,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAjB;EACD;;EAEDL,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACI,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAON,QAAP;EACD,CAvBD;;EAyBA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOJ,QAAQ,CAACY,aAAT,CAAuBR,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAMS,sBAAsB,GAAGV,OAAO,IAAI;EACxC,QAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACY,aAAT,CAAuBR,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,MAAMU,gCAAgC,GAAGX,OAAO,IAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAMlD,MAAI;EAAEY,IAAAA,kBAAF;EAAsBC,IAAAA;EAAtB,MAA0CC,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAA9C;EAEA,QAAMgB,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC;EACA,QAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;EAYlD,MAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAdiD;;;EAiBlDP,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACN,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAO,EAAAA,eAAe,GAAGA,eAAe,CAACP,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACW,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,IAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+E9B,uBAAtF;EACD,CArBD;;EAuBA,MAAMqC,oBAAoB,GAAGpB,OAAO,IAAI;EACtCA,EAAAA,OAAO,CAACqB,aAAR,CAAsB,IAAIC,KAAJ,CAAUtC,cAAV,CAAtB;EACD,CAFD;;EAIA,MAAMuC,SAAS,GAAGrC,GAAG,IAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBsC,QAAzC;;EAEA,MAAMC,oBAAoB,GAAG,CAACzB,OAAD,EAAU0B,QAAV,KAAuB;EAClD,MAAIC,MAAM,GAAG,KAAb;EACA,QAAMC,eAAe,GAAG,CAAxB;EACA,QAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;EAEA,WAASE,QAAT,GAAoB;EAClBH,IAAAA,MAAM,GAAG,IAAT;EACA3B,IAAAA,OAAO,CAAC+B,mBAAR,CAA4B/C,cAA5B,EAA4C8C,QAA5C;EACD;;EAED9B,EAAAA,OAAO,CAACgC,gBAAR,CAAyBhD,cAAzB,EAAyC8C,QAAzC;EACAG,EAAAA,UAAU,CAAC,MAAM;EACf,QAAI,CAACN,MAAL,EAAa;EACXP,MAAAA,oBAAoB,CAACpB,OAAD,CAApB;EACD;EACF,GAJS,EAIP6B,gBAJO,CAAV;EAKD,CAhBD;;EAkBA,MAAMK,eAAe,GAAG,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,KAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiCC,QAAQ,IAAI;EAC3C,UAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,UAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,UAAMG,SAAS,GAAGD,KAAK,IAAIpB,SAAS,CAACoB,KAAD,CAAlB,GAA4B,SAA5B,GAAwC1D,MAAM,CAAC0D,KAAD,CAAhE;;EAEA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,SAAJ,CACH,GAAEZ,aAAa,CAACa,WAAd,EAA4B,IAA/B,GACC,WAAUP,QAAS,oBAAmBG,SAAU,IADjD,GAEC,sBAAqBF,aAAc,IAHhC,CAAN;EAKD;EACF,GAZD;EAaD,CAdD;;EAgBA,MAAMO,SAAS,GAAGjD,OAAO,IAAI;EAC3B,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,KAAP;EACD;;EAED,MAAIA,OAAO,CAACkD,KAAR,IAAiBlD,OAAO,CAACmD,UAAzB,IAAuCnD,OAAO,CAACmD,UAAR,CAAmBD,KAA9D,EAAqE;EACnE,UAAME,YAAY,GAAGrC,gBAAgB,CAACf,OAAD,CAArC;EACA,UAAMqD,eAAe,GAAGtC,gBAAgB,CAACf,OAAO,CAACmD,UAAT,CAAxC;EAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;EAGD;;EAED,SAAO,KAAP;EACD,CAfD;;EAiBA,MAAMC,UAAU,GAAGxD,OAAO,IAAI;EAC5B,MAAI,CAACA,OAAD,IAAYA,OAAO,CAACwB,QAAR,KAAqBiC,IAAI,CAACC,YAA1C,EAAwD;EACtD,WAAO,IAAP;EACD;;EAED,MAAI1D,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;EAC1C,WAAO,IAAP;EACD;;EAED,MAAI,OAAO5D,OAAO,CAAC6D,QAAf,KAA4B,WAAhC,EAA6C;EAC3C,WAAO7D,OAAO,CAAC6D,QAAf;EACD;;EAED,SAAO7D,OAAO,CAAC8D,YAAR,CAAqB,UAArB,KAAoC9D,OAAO,CAACE,YAAR,CAAqB,UAArB,MAAqC,OAAhF;EACD,CAdD;;EAgBA,MAAM6D,cAAc,GAAG/D,OAAO,IAAI;EAChC,MAAI,CAACH,QAAQ,CAACmE,eAAT,CAAyBC,YAA9B,EAA4C;EAC1C,WAAO,IAAP;EACD,GAH+B;;;EAMhC,MAAI,OAAOjE,OAAO,CAACkE,WAAf,KAA+B,UAAnC,EAA+C;EAC7C,UAAMC,IAAI,GAAGnE,OAAO,CAACkE,WAAR,EAAb;EACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;EACD;;EAED,MAAInE,OAAO,YAAYoE,UAAvB,EAAmC;EACjC,WAAOpE,OAAP;EACD,GAb+B;;;EAgBhC,MAAI,CAACA,OAAO,CAACmD,UAAb,EAAyB;EACvB,WAAO,IAAP;EACD;;EAED,SAAOY,cAAc,CAAC/D,OAAO,CAACmD,UAAT,CAArB;EACD,CArBD;;EAuBA,MAAMkB,IAAI,GAAG,MAAM,YAAY,EAA/B;;EAEA,MAAMC,MAAM,GAAGtE,OAAO,IAAIA,OAAO,CAACuE,YAAlC;;EAEA,MAAMC,SAAS,GAAG,MAAM;EACtB,QAAM;EAAEC,IAAAA;EAAF,MAAa3D,MAAnB;;EAEA,MAAI2D,MAAM,IAAI,CAAC5E,QAAQ,CAAC6E,IAAT,CAAcZ,YAAd,CAA2B,mBAA3B,CAAf,EAAgE;EAC9D,WAAOW,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,MAAME,kBAAkB,GAAGC,QAAQ,IAAI;EACrC,MAAI/E,QAAQ,CAACgF,UAAT,KAAwB,SAA5B,EAAuC;EACrChF,IAAAA,QAAQ,CAACmC,gBAAT,CAA0B,kBAA1B,EAA8C4C,QAA9C;EACD,GAFD,MAEO;EACLA,IAAAA,QAAQ;EACT;EACF,CAND;;EAQA,MAAME,KAAK,GAAG,MAAMjF,QAAQ,CAACmE,eAAT,CAAyBe,GAAzB,KAAiC,KAArD;;EAEA,MAAMC,kBAAkB,GAAG,CAACC,IAAD,EAAOC,MAAP,KAAkB;EAC3CP,EAAAA,kBAAkB,CAAC,MAAM;EACvB,UAAMQ,CAAC,GAAGX,SAAS,EAAnB;EACA;;EACA,QAAIW,CAAJ,EAAO;EACL,YAAMC,kBAAkB,GAAGD,CAAC,CAACE,EAAF,CAAKJ,IAAL,CAA3B;EACAE,MAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,IAAaC,MAAM,CAACI,eAApB;EACAH,MAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,EAAWM,WAAX,GAAyBL,MAAzB;;EACAC,MAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,EAAWO,UAAX,GAAwB,MAAM;EAC5BL,QAAAA,CAAC,CAACE,EAAF,CAAKJ,IAAL,IAAaG,kBAAb;EACA,eAAOF,MAAM,CAACI,eAAd;EACD,OAHD;EAID;EACF,GAZiB,CAAlB;EAaD,CAdD;;EC1NA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,MAAMG,UAAU,GAAG,IAAIC,GAAJ,EAAnB;AAEA,aAAe;EACbC,EAAAA,GAAG,CAAC3F,OAAD,EAAU4F,GAAV,EAAeC,QAAf,EAAyB;EAC1B,QAAI,CAACJ,UAAU,CAACK,GAAX,CAAe9F,OAAf,CAAL,EAA8B;EAC5ByF,MAAAA,UAAU,CAACE,GAAX,CAAe3F,OAAf,EAAwB,IAAI0F,GAAJ,EAAxB;EACD;;EAED,UAAMK,WAAW,GAAGN,UAAU,CAACO,GAAX,CAAehG,OAAf,CAApB,CAL0B;EAQ1B;;EACA,QAAI,CAAC+F,WAAW,CAACD,GAAZ,CAAgBF,GAAhB,CAAD,IAAyBG,WAAW,CAACE,IAAZ,KAAqB,CAAlD,EAAqD;EACnD;EACAC,MAAAA,OAAO,CAACC,KAAR,CAAe,+EAA8EC,KAAK,CAACC,IAAN,CAAWN,WAAW,CAACxD,IAAZ,EAAX,EAA+B,CAA/B,CAAkC,GAA/H;EACA;EACD;;EAEDwD,IAAAA,WAAW,CAACJ,GAAZ,CAAgBC,GAAhB,EAAqBC,QAArB;EACD,GAjBY;;EAmBbG,EAAAA,GAAG,CAAChG,OAAD,EAAU4F,GAAV,EAAe;EAChB,QAAIH,UAAU,CAACK,GAAX,CAAe9F,OAAf,CAAJ,EAA6B;EAC3B,aAAOyF,UAAU,CAACO,GAAX,CAAehG,OAAf,EAAwBgG,GAAxB,CAA4BJ,GAA5B,KAAoC,IAA3C;EACD;;EAED,WAAO,IAAP;EACD,GAzBY;;EA2BbU,EAAAA,MAAM,CAACtG,OAAD,EAAU4F,GAAV,EAAe;EACnB,QAAI,CAACH,UAAU,CAACK,GAAX,CAAe9F,OAAf,CAAL,EAA8B;EAC5B;EACD;;EAED,UAAM+F,WAAW,GAAGN,UAAU,CAACO,GAAX,CAAehG,OAAf,CAApB;EAEA+F,IAAAA,WAAW,CAACQ,MAAZ,CAAmBX,GAAnB,EAPmB;;EAUnB,QAAIG,WAAW,CAACE,IAAZ,KAAqB,CAAzB,EAA4B;EAC1BR,MAAAA,UAAU,CAACc,MAAX,CAAkBvG,OAAlB;EACD;EACF;;EAxCY,CAAf;;ECfA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;;EAEA,MAAMwG,cAAc,GAAG,oBAAvB;EACA,MAAMC,cAAc,GAAG,MAAvB;EACA,MAAMC,aAAa,GAAG,QAAtB;EACA,MAAMC,aAAa,GAAG,EAAtB;;EACA,IAAIC,QAAQ,GAAG,CAAf;EACA,MAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WADO;EAEnBC,EAAAA,UAAU,EAAE;EAFO,CAArB;EAIA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB;EAiDA;EACA;EACA;EACA;EACA;;EAEA,SAASC,WAAT,CAAqBlH,OAArB,EAA8BmH,GAA9B,EAAmC;EACjC,SAAQA,GAAG,IAAK,GAAEA,GAAI,KAAIP,QAAQ,EAAG,EAA9B,IAAoC5G,OAAO,CAAC4G,QAA5C,IAAwDA,QAAQ,EAAvE;EACD;;EAED,SAASQ,QAAT,CAAkBpH,OAAlB,EAA2B;EACzB,QAAMmH,GAAG,GAAGD,WAAW,CAAClH,OAAD,CAAvB;EAEAA,EAAAA,OAAO,CAAC4G,QAAR,GAAmBO,GAAnB;EACAR,EAAAA,aAAa,CAACQ,GAAD,CAAb,GAAqBR,aAAa,CAACQ,GAAD,CAAb,IAAsB,EAA3C;EAEA,SAAOR,aAAa,CAACQ,GAAD,CAApB;EACD;;EAED,SAASE,gBAAT,CAA0BrH,OAA1B,EAAmCqF,EAAnC,EAAuC;EACrC,SAAO,SAASiC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBxH,OAAvB;;EAEA,QAAIsH,OAAO,CAACG,MAAZ,EAAoB;EAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiB3H,OAAjB,EAA0BuH,KAAK,CAACK,IAAhC,EAAsCvC,EAAtC;EACD;;EAED,WAAOA,EAAE,CAACwC,KAAH,CAAS7H,OAAT,EAAkB,CAACuH,KAAD,CAAlB,CAAP;EACD,GARD;EASD;;EAED,SAASO,0BAAT,CAAoC9H,OAApC,EAA6CC,QAA7C,EAAuDoF,EAAvD,EAA2D;EACzD,SAAO,SAASiC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,UAAMQ,WAAW,GAAG/H,OAAO,CAACgI,gBAAR,CAAyB/H,QAAzB,CAApB;;EAEA,SAAK,IAAI;EAAEgI,MAAAA;EAAF,QAAaV,KAAtB,EAA6BU,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC9E,UAAxE,EAAoF;EAClF,WAAK,IAAI+E,CAAC,GAAGH,WAAW,CAACI,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;EACrC,YAAIH,WAAW,CAACG,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;EAC7BV,UAAAA,KAAK,CAACC,cAAN,GAAuBS,MAAvB;;EAEA,cAAIX,OAAO,CAACG,MAAZ,EAAoB;EAClB;EACAC,YAAAA,YAAY,CAACC,GAAb,CAAiB3H,OAAjB,EAA0BuH,KAAK,CAACK,IAAhC,EAAsCvC,EAAtC;EACD;;EAED,iBAAOA,EAAE,CAACwC,KAAH,CAASI,MAAT,EAAiB,CAACV,KAAD,CAAjB,CAAP;EACD;EACF;EACF,KAhB4B;;;EAmB7B,WAAO,IAAP;EACD,GApBD;EAqBD;;EAED,SAASa,WAAT,CAAqBC,MAArB,EAA6Bf,OAA7B,EAAsCgB,kBAAkB,GAAG,IAA3D,EAAiE;EAC/D,QAAMC,YAAY,GAAGjG,MAAM,CAACC,IAAP,CAAY8F,MAAZ,CAArB;;EAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;EACvD,UAAMX,KAAK,GAAGc,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;EAEA,QAAIX,KAAK,CAACkB,eAAN,KAA0BnB,OAA1B,IAAqCC,KAAK,CAACe,kBAAN,KAA6BA,kBAAtE,EAA0F;EACxF,aAAOf,KAAP;EACD;EACF;;EAED,SAAO,IAAP;EACD;;EAED,SAASmB,eAAT,CAAyBC,iBAAzB,EAA4CrB,OAA5C,EAAqDsB,YAArD,EAAmE;EACjE,QAAMC,UAAU,GAAG,OAAOvB,OAAP,KAAmB,QAAtC;EACA,QAAMmB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBtB,OAApD,CAFiE;;EAKjE,MAAIwB,SAAS,GAAGH,iBAAiB,CAACI,OAAlB,CAA0BtC,cAA1B,EAA0C,EAA1C,CAAhB;EACA,QAAMuC,MAAM,GAAGnC,YAAY,CAACiC,SAAD,CAA3B;;EAEA,MAAIE,MAAJ,EAAY;EACVF,IAAAA,SAAS,GAAGE,MAAZ;EACD;;EAED,QAAMC,QAAQ,GAAGjC,YAAY,CAAClB,GAAb,CAAiBgD,SAAjB,CAAjB;;EAEA,MAAI,CAACG,QAAL,EAAe;EACbH,IAAAA,SAAS,GAAGH,iBAAZ;EACD;;EAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;EACD;;EAED,SAASI,UAAT,CAAoBlJ,OAApB,EAA6B2I,iBAA7B,EAAgDrB,OAAhD,EAAyDsB,YAAzD,EAAuEnB,MAAvE,EAA+E;EAC7E,MAAI,OAAOkB,iBAAP,KAA6B,QAA7B,IAAyC,CAAC3I,OAA9C,EAAuD;EACrD;EACD;;EAED,MAAI,CAACsH,OAAL,EAAc;EACZA,IAAAA,OAAO,GAAGsB,YAAV;EACAA,IAAAA,YAAY,GAAG,IAAf;EACD;;EAED,QAAM,CAACC,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAAhE;EACA,QAAMP,MAAM,GAAGjB,QAAQ,CAACpH,OAAD,CAAvB;EACA,QAAMmJ,QAAQ,GAAGd,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;EACA,QAAMM,UAAU,GAAGhB,WAAW,CAACe,QAAD,EAAWV,eAAX,EAA4BI,UAAU,GAAGvB,OAAH,GAAa,IAAnD,CAA9B;;EAEA,MAAI8B,UAAJ,EAAgB;EACdA,IAAAA,UAAU,CAAC3B,MAAX,GAAoB2B,UAAU,CAAC3B,MAAX,IAAqBA,MAAzC;EAEA;EACD;;EAED,QAAMN,GAAG,GAAGD,WAAW,CAACuB,eAAD,EAAkBE,iBAAiB,CAACI,OAAlB,CAA0BvC,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;EACA,QAAMnB,EAAE,GAAGwD,UAAU,GACnBf,0BAA0B,CAAC9H,OAAD,EAAUsH,OAAV,EAAmBsB,YAAnB,CADP,GAEnBvB,gBAAgB,CAACrH,OAAD,EAAUsH,OAAV,CAFlB;EAIAjC,EAAAA,EAAE,CAACiD,kBAAH,GAAwBO,UAAU,GAAGvB,OAAH,GAAa,IAA/C;EACAjC,EAAAA,EAAE,CAACoD,eAAH,GAAqBA,eAArB;EACApD,EAAAA,EAAE,CAACoC,MAAH,GAAYA,MAAZ;EACApC,EAAAA,EAAE,CAACuB,QAAH,GAAcO,GAAd;EACAgC,EAAAA,QAAQ,CAAChC,GAAD,CAAR,GAAgB9B,EAAhB;EAEArF,EAAAA,OAAO,CAACgC,gBAAR,CAAyB8G,SAAzB,EAAoCzD,EAApC,EAAwCwD,UAAxC;EACD;;EAED,SAASQ,aAAT,CAAuBrJ,OAAvB,EAAgCqI,MAAhC,EAAwCS,SAAxC,EAAmDxB,OAAnD,EAA4DgB,kBAA5D,EAAgF;EAC9E,QAAMjD,EAAE,GAAG+C,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBxB,OAApB,EAA6BgB,kBAA7B,CAAtB;;EAEA,MAAI,CAACjD,EAAL,EAAS;EACP;EACD;;EAEDrF,EAAAA,OAAO,CAAC+B,mBAAR,CAA4B+G,SAA5B,EAAuCzD,EAAvC,EAA2CiE,OAAO,CAAChB,kBAAD,CAAlD;EACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBzD,EAAE,CAACuB,QAArB,CAAP;EACD;;EAED,SAAS2C,wBAAT,CAAkCvJ,OAAlC,EAA2CqI,MAA3C,EAAmDS,SAAnD,EAA8DU,SAA9D,EAAyE;EACvE,QAAMC,iBAAiB,GAAGpB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EAEAxG,EAAAA,MAAM,CAACC,IAAP,CAAYkH,iBAAZ,EAA+BjH,OAA/B,CAAuCkH,UAAU,IAAI;EACnD,QAAIA,UAAU,CAACtJ,QAAX,CAAoBoJ,SAApB,CAAJ,EAAoC;EAClC,YAAMjC,KAAK,GAAGkC,iBAAiB,CAACC,UAAD,CAA/B;EAEAL,MAAAA,aAAa,CAACrJ,OAAD,EAAUqI,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,GAND;EAOD;;EAED,MAAMZ,YAAY,GAAG;EACnBiC,EAAAA,EAAE,CAAC3J,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC;EACxCM,IAAAA,UAAU,CAAClJ,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,KAAxC,CAAV;EACD,GAHkB;;EAKnBgB,EAAAA,GAAG,CAAC5J,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC;EACzCM,IAAAA,UAAU,CAAClJ,OAAD,EAAUuH,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,IAAxC,CAAV;EACD,GAPkB;;EASnBjB,EAAAA,GAAG,CAAC3H,OAAD,EAAU2I,iBAAV,EAA6BrB,OAA7B,EAAsCsB,YAAtC,EAAoD;EACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAAC3I,OAA9C,EAAuD;EACrD;EACD;;EAED,UAAM,CAAC6I,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,IAA2CJ,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAAhE;EACA,UAAMiB,WAAW,GAAGf,SAAS,KAAKH,iBAAlC;EACA,UAAMN,MAAM,GAAGjB,QAAQ,CAACpH,OAAD,CAAvB;EACA,UAAM8J,WAAW,GAAGnB,iBAAiB,CAACtI,UAAlB,CAA6B,GAA7B,CAApB;;EAEA,QAAI,OAAOoI,eAAP,KAA2B,WAA/B,EAA4C;EAC1C;EACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;EACjC;EACD;;EAEDO,MAAAA,aAAa,CAACrJ,OAAD,EAAUqI,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGvB,OAAH,GAAa,IAArE,CAAb;EACA;EACD;;EAED,QAAIwC,WAAJ,EAAiB;EACfxH,MAAAA,MAAM,CAACC,IAAP,CAAY8F,MAAZ,EAAoB7F,OAApB,CAA4BuH,YAAY,IAAI;EAC1CR,QAAAA,wBAAwB,CAACvJ,OAAD,EAAUqI,MAAV,EAAkB0B,YAAlB,EAAgCpB,iBAAiB,CAACqB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;EACD,OAFD;EAGD;;EAED,UAAMP,iBAAiB,GAAGpB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EACAxG,IAAAA,MAAM,CAACC,IAAP,CAAYkH,iBAAZ,EAA+BjH,OAA/B,CAAuCyH,WAAW,IAAI;EACpD,YAAMP,UAAU,GAAGO,WAAW,CAAClB,OAAZ,CAAoBrC,aAApB,EAAmC,EAAnC,CAAnB;;EAEA,UAAI,CAACmD,WAAD,IAAgBlB,iBAAiB,CAACvI,QAAlB,CAA2BsJ,UAA3B,CAApB,EAA4D;EAC1D,cAAMnC,KAAK,GAAGkC,iBAAiB,CAACQ,WAAD,CAA/B;EAEAZ,QAAAA,aAAa,CAACrJ,OAAD,EAAUqI,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,KARD;EASD,GA7CkB;;EA+CnB4B,EAAAA,OAAO,CAAClK,OAAD,EAAUuH,KAAV,EAAiB4C,IAAjB,EAAuB;EAC5B,QAAI,OAAO5C,KAAP,KAAiB,QAAjB,IAA6B,CAACvH,OAAlC,EAA2C;EACzC,aAAO,IAAP;EACD;;EAED,UAAMmF,CAAC,GAAGX,SAAS,EAAnB;EACA,UAAMsE,SAAS,GAAGvB,KAAK,CAACwB,OAAN,CAActC,cAAd,EAA8B,EAA9B,CAAlB;EACA,UAAMoD,WAAW,GAAGtC,KAAK,KAAKuB,SAA9B;EACA,UAAMG,QAAQ,GAAGjC,YAAY,CAAClB,GAAb,CAAiBgD,SAAjB,CAAjB;EAEA,QAAIsB,WAAJ;EACA,QAAIC,OAAO,GAAG,IAAd;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAIC,gBAAgB,GAAG,KAAvB;EACA,QAAIC,GAAG,GAAG,IAAV;;EAEA,QAAIX,WAAW,IAAI1E,CAAnB,EAAsB;EACpBiF,MAAAA,WAAW,GAAGjF,CAAC,CAAC7D,KAAF,CAAQiG,KAAR,EAAe4C,IAAf,CAAd;EAEAhF,MAAAA,CAAC,CAACnF,OAAD,CAAD,CAAWkK,OAAX,CAAmBE,WAAnB;EACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;EACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;EACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;EACD;;EAED,QAAI1B,QAAJ,EAAc;EACZuB,MAAAA,GAAG,GAAG3K,QAAQ,CAAC+K,WAAT,CAAqB,YAArB,CAAN;EACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAc/B,SAAd,EAAyBuB,OAAzB,EAAkC,IAAlC;EACD,KAHD,MAGO;EACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgBvD,KAAhB,EAAuB;EAC3B8C,QAAAA,OAD2B;EAE3BU,QAAAA,UAAU,EAAE;EAFe,OAAvB,CAAN;EAID,KAjC2B;;;EAoC5B,QAAI,OAAOZ,IAAP,KAAgB,WAApB,EAAiC;EAC/B7H,MAAAA,MAAM,CAACC,IAAP,CAAY4H,IAAZ,EAAkB3H,OAAlB,CAA0BoD,GAAG,IAAI;EAC/BtD,QAAAA,MAAM,CAAC0I,cAAP,CAAsBR,GAAtB,EAA2B5E,GAA3B,EAAgC;EAC9BI,UAAAA,GAAG,GAAG;EACJ,mBAAOmE,IAAI,CAACvE,GAAD,CAAX;EACD;;EAH6B,SAAhC;EAKD,OAND;EAOD;;EAED,QAAI2E,gBAAJ,EAAsB;EACpBC,MAAAA,GAAG,CAACS,cAAJ;EACD;;EAED,QAAIX,cAAJ,EAAoB;EAClBtK,MAAAA,OAAO,CAACqB,aAAR,CAAsBmJ,GAAtB;EACD;;EAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;EAC9DA,MAAAA,WAAW,CAACa,cAAZ;EACD;;EAED,WAAOT,GAAP;EACD;;EA1GkB,CAArB;;EC7NA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;;EAEA,MAAMU,OAAO,GAAG,aAAhB;;EAEA,MAAMC,aAAN,CAAoB;EAClBC,EAAAA,WAAW,CAACpL,OAAD,EAAU;EACnBA,IAAAA,OAAO,GAAG,OAAOA,OAAP,KAAmB,QAAnB,GAA8BH,QAAQ,CAACY,aAAT,CAAuBT,OAAvB,CAA9B,GAAgEA,OAA1E;;EAEA,QAAI,CAACA,OAAL,EAAc;EACZ;EACD;;EAED,SAAKqL,QAAL,GAAgBrL,OAAhB;EACAsL,IAAAA,IAAI,CAAC3F,GAAL,CAAS,KAAK0F,QAAd,EAAwB,KAAKD,WAAL,CAAiBG,QAAzC,EAAmD,IAAnD;EACD;;EAEDC,EAAAA,OAAO,GAAG;EACRF,IAAAA,IAAI,CAAChF,MAAL,CAAY,KAAK+E,QAAjB,EAA2B,KAAKD,WAAL,CAAiBG,QAA5C;EACA,SAAKF,QAAL,GAAgB,IAAhB;EACD;EAED;;;EAEkB,SAAXI,WAAW,CAACzL,OAAD,EAAU;EAC1B,WAAOsL,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkB,KAAKuL,QAAvB,CAAP;EACD;;EAEiB,aAAPL,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAzBiB;;ECjBpB;EACA;EACA;EACA;EACA;EACA;EAYA;EACA;EACA;EACA;EACA;;EAEA,MAAMQ,MAAI,GAAG,OAAb;EACA,MAAMH,UAAQ,GAAG,UAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMC,gBAAgB,GAAG,2BAAzB;EAEA,MAAMC,WAAW,GAAI,QAAOH,WAAU,EAAtC;EACA,MAAMI,YAAY,GAAI,SAAQJ,WAAU,EAAxC;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMK,gBAAgB,GAAG,OAAzB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBjB,aAApB,CAAkC;EAChC;EAEmB,aAARI,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAL+B;;;EAShCc,EAAAA,KAAK,CAACrM,OAAD,EAAU;EACb,UAAMsM,WAAW,GAAGtM,OAAO,GAAG,KAAKuM,eAAL,CAAqBvM,OAArB,CAAH,GAAmC,KAAKqL,QAAnE;;EACA,UAAMmB,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;EAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAACjC,gBAAxC,EAA0D;EACxD;EACD;;EAED,SAAKmC,cAAL,CAAoBJ,WAApB;EACD,GAlB+B;;;EAsBhCC,EAAAA,eAAe,CAACvM,OAAD,EAAU;EACvB,WAAOU,sBAAsB,CAACV,OAAD,CAAtB,IAAmCA,OAAO,CAAC2M,OAAR,CAAiB,IAAGV,gBAAiB,EAArC,CAA1C;EACD;;EAEDQ,EAAAA,kBAAkB,CAACzM,OAAD,EAAU;EAC1B,WAAO0H,YAAY,CAACwC,OAAb,CAAqBlK,OAArB,EAA8B8L,WAA9B,CAAP;EACD;;EAEDY,EAAAA,cAAc,CAAC1M,OAAD,EAAU;EACtBA,IAAAA,OAAO,CAAC2D,SAAR,CAAkB2C,MAAlB,CAAyB6F,iBAAzB;;EAEA,QAAI,CAACnM,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2BsI,iBAA3B,CAAL,EAAkD;EAChD,WAAKU,eAAL,CAAqB5M,OAArB;;EACA;EACD;;EAED,UAAMY,kBAAkB,GAAGD,gCAAgC,CAACX,OAAD,CAA3D;EAEA0H,IAAAA,YAAY,CAACkC,GAAb,CAAiB5J,OAAjB,EAA0B,eAA1B,EAA2C,MAAM,KAAK4M,eAAL,CAAqB5M,OAArB,CAAjD;EACAyB,IAAAA,oBAAoB,CAACzB,OAAD,EAAUY,kBAAV,CAApB;EACD;;EAEDgM,EAAAA,eAAe,CAAC5M,OAAD,EAAU;EACvB,QAAIA,OAAO,CAACmD,UAAZ,EAAwB;EACtBnD,MAAAA,OAAO,CAACmD,UAAR,CAAmB0J,WAAnB,CAA+B7M,OAA/B;EACD;;EAED0H,IAAAA,YAAY,CAACwC,OAAb,CAAqBlK,OAArB,EAA8B+L,YAA9B;EACD,GAlD+B;;;EAsDV,SAAfzG,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EAEA,UAAI,CAACwB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIX,KAAJ,CAAU,IAAV,CAAP;EACD;;EAED,UAAIhK,MAAM,KAAK,OAAf,EAAwB;EACtB2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAVM,CAAP;EAWD;;EAEmB,SAAb4K,aAAa,CAACC,aAAD,EAAgB;EAClC,WAAO,UAAU1F,KAAV,EAAiB;EACtB,UAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAAC0D,cAAN;EACD;;EAEDgC,MAAAA,aAAa,CAACZ,KAAd,CAAoB,IAApB;EACD,KAND;EAOD;;EA5E+B;EA+ElC;EACA;EACA;EACA;EACA;;;EAEA3E,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACY,aAAN,CAAoB,IAAIZ,KAAJ,EAApB,CAAlE;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEApH,kBAAkB,CAAC0G,MAAD,EAAOU,KAAP,CAAlB;;EC1IA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;EACA;EACA;;EAEA,MAAMV,MAAI,GAAG,QAAb;EACA,MAAMH,UAAQ,GAAG,WAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMsB,mBAAiB,GAAG,QAA1B;EAEA,MAAMC,sBAAoB,GAAG,2BAA7B;EAEA,MAAMnB,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMwB,MAAN,SAAqBjC,aAArB,CAAmC;EACjC;EAEmB,aAARI,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GALgC;;;EASjC8B,EAAAA,MAAM,GAAG;EACP;EACA,SAAKhC,QAAL,CAAciC,YAAd,CAA2B,cAA3B,EAA2C,KAAKjC,QAAL,CAAc1H,SAAd,CAAwB0J,MAAxB,CAA+BH,mBAA/B,CAA3C;EACD,GAZgC;;;EAgBX,SAAf5H,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EAEA,UAAI,CAACwB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIK,MAAJ,CAAW,IAAX,CAAP;EACD;;EAED,UAAIhL,MAAM,KAAK,QAAf,EAAyB;EACvB2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EA5BgC;EA+BnC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE5F,KAAK,IAAI;EAC7EA,EAAAA,KAAK,CAAC0D,cAAN;EAEA,QAAMsC,MAAM,GAAGhG,KAAK,CAACU,MAAN,CAAa0E,OAAb,CAAqBQ,sBAArB,CAAf;EAEA,MAAIJ,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAASuH,MAAT,EAAiBhC,UAAjB,CAAX;;EACA,MAAI,CAACwB,IAAL,EAAW;EACTA,IAAAA,IAAI,GAAG,IAAIK,MAAJ,CAAWG,MAAX,CAAP;EACD;;EAEDR,EAAAA,IAAI,CAACM,MAAL;EACD,CAXD;EAaA;EACA;EACA;EACA;EACA;EACA;;EAEArI,kBAAkB,CAAC0G,MAAD,EAAO0B,MAAP,CAAlB;;EC5FA;EACA;EACA;EACA;EACA;EACA;EAEA,SAASI,aAAT,CAAuBC,GAAvB,EAA4B;EAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;EAClB,WAAO,IAAP;EACD;;EAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;EACnB,WAAO,KAAP;EACD;;EAED,MAAIA,GAAG,KAAKxM,MAAM,CAACwM,GAAD,CAAN,CAAYrO,QAAZ,EAAZ,EAAoC;EAClC,WAAO6B,MAAM,CAACwM,GAAD,CAAb;EACD;;EAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;EAChC,WAAO,IAAP;EACD;;EAED,SAAOA,GAAP;EACD;;EAED,SAASC,gBAAT,CAA0B9H,GAA1B,EAA+B;EAC7B,SAAOA,GAAG,CAACmD,OAAJ,CAAY,QAAZ,EAAsB4E,GAAG,IAAK,IAAGA,GAAG,CAACpO,WAAJ,EAAkB,EAAnD,CAAP;EACD;;EAED,MAAMqO,WAAW,GAAG;EAClBC,EAAAA,gBAAgB,CAAC7N,OAAD,EAAU4F,GAAV,EAAejD,KAAf,EAAsB;EACpC3C,IAAAA,OAAO,CAACsN,YAAR,CAAsB,WAAUI,gBAAgB,CAAC9H,GAAD,CAAM,EAAtD,EAAyDjD,KAAzD;EACD,GAHiB;;EAKlBmL,EAAAA,mBAAmB,CAAC9N,OAAD,EAAU4F,GAAV,EAAe;EAChC5F,IAAAA,OAAO,CAAC+N,eAAR,CAAyB,WAAUL,gBAAgB,CAAC9H,GAAD,CAAM,EAAzD;EACD,GAPiB;;EASlBoI,EAAAA,iBAAiB,CAAChO,OAAD,EAAU;EACzB,QAAI,CAACA,OAAL,EAAc;EACZ,aAAO,EAAP;EACD;;EAED,UAAMiO,UAAU,GAAG,EAAnB;EAEA3L,IAAAA,MAAM,CAACC,IAAP,CAAYvC,OAAO,CAACkO,OAApB,EACGC,MADH,CACUvI,GAAG,IAAIA,GAAG,CAACvF,UAAJ,CAAe,IAAf,CADjB,EAEGmC,OAFH,CAEWoD,GAAG,IAAI;EACd,UAAIwI,OAAO,GAAGxI,GAAG,CAACmD,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAd;EACAqF,MAAAA,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkB9O,WAAlB,KAAkC6O,OAAO,CAACpE,KAAR,CAAc,CAAd,EAAiBoE,OAAO,CAACjG,MAAzB,CAA5C;EACA8F,MAAAA,UAAU,CAACG,OAAD,CAAV,GAAsBZ,aAAa,CAACxN,OAAO,CAACkO,OAAR,CAAgBtI,GAAhB,CAAD,CAAnC;EACD,KANH;EAQA,WAAOqI,UAAP;EACD,GAzBiB;;EA2BlBK,EAAAA,gBAAgB,CAACtO,OAAD,EAAU4F,GAAV,EAAe;EAC7B,WAAO4H,aAAa,CAACxN,OAAO,CAACE,YAAR,CAAsB,WAAUwN,gBAAgB,CAAC9H,GAAD,CAAM,EAAtD,CAAD,CAApB;EACD,GA7BiB;;EA+BlB2I,EAAAA,MAAM,CAACvO,OAAD,EAAU;EACd,UAAMwO,IAAI,GAAGxO,OAAO,CAACyO,qBAAR,EAAb;EAEA,WAAO;EACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAW7O,QAAQ,CAAC6E,IAAT,CAAciK,SADzB;EAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAY/O,QAAQ,CAAC6E,IAAT,CAAcmK;EAF3B,KAAP;EAID,GAtCiB;;EAwClBC,EAAAA,QAAQ,CAAC9O,OAAD,EAAU;EAChB,WAAO;EACL0O,MAAAA,GAAG,EAAE1O,OAAO,CAAC+O,SADR;EAELH,MAAAA,IAAI,EAAE5O,OAAO,CAACgP;EAFT,KAAP;EAID;;EA7CiB,CAApB;;EC/BA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,MAAMC,SAAS,GAAG,CAAlB;EAEA,MAAMC,cAAc,GAAG;EACrBC,EAAAA,IAAI,CAAClP,QAAD,EAAWD,OAAO,GAAGH,QAAQ,CAACmE,eAA9B,EAA+C;EACjD,WAAO,GAAGoL,MAAH,CAAU,GAAGC,OAAO,CAACC,SAAR,CAAkBtH,gBAAlB,CAAmC3I,IAAnC,CAAwCW,OAAxC,EAAiDC,QAAjD,CAAb,CAAP;EACD,GAHoB;;EAKrBsP,EAAAA,OAAO,CAACtP,QAAD,EAAWD,OAAO,GAAGH,QAAQ,CAACmE,eAA9B,EAA+C;EACpD,WAAOqL,OAAO,CAACC,SAAR,CAAkB7O,aAAlB,CAAgCpB,IAAhC,CAAqCW,OAArC,EAA8CC,QAA9C,CAAP;EACD,GAPoB;;EASrBuP,EAAAA,QAAQ,CAACxP,OAAD,EAAUC,QAAV,EAAoB;EAC1B,WAAO,GAAGmP,MAAH,CAAU,GAAGpP,OAAO,CAACwP,QAArB,EACJrB,MADI,CACGsB,KAAK,IAAIA,KAAK,CAACC,OAAN,CAAczP,QAAd,CADZ,CAAP;EAED,GAZoB;;EAcrB0P,EAAAA,OAAO,CAAC3P,OAAD,EAAUC,QAAV,EAAoB;EACzB,UAAM0P,OAAO,GAAG,EAAhB;EAEA,QAAIC,QAAQ,GAAG5P,OAAO,CAACmD,UAAvB;;EAEA,WAAOyM,QAAQ,IAAIA,QAAQ,CAACpO,QAAT,KAAsBiC,IAAI,CAACC,YAAvC,IAAuDkM,QAAQ,CAACpO,QAAT,KAAsByN,SAApF,EAA+F;EAC7F,UAAIW,QAAQ,CAACF,OAAT,CAAiBzP,QAAjB,CAAJ,EAAgC;EAC9B0P,QAAAA,OAAO,CAACE,IAAR,CAAaD,QAAb;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACzM,UAApB;EACD;;EAED,WAAOwM,OAAP;EACD,GA5BoB;;EA8BrBG,EAAAA,IAAI,CAAC9P,OAAD,EAAUC,QAAV,EAAoB;EACtB,QAAI8P,QAAQ,GAAG/P,OAAO,CAACgQ,sBAAvB;;EAEA,WAAOD,QAAP,EAAiB;EACf,UAAIA,QAAQ,CAACL,OAAT,CAAiBzP,QAAjB,CAAJ,EAAgC;EAC9B,eAAO,CAAC8P,QAAD,CAAP;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;EACD;;EAED,WAAO,EAAP;EACD,GA1CoB;;EA4CrBC,EAAAA,IAAI,CAACjQ,OAAD,EAAUC,QAAV,EAAoB;EACtB,QAAIgQ,IAAI,GAAGjQ,OAAO,CAACkQ,kBAAnB;;EAEA,WAAOD,IAAP,EAAa;EACX,UAAIA,IAAI,CAACP,OAAL,CAAazP,QAAb,CAAJ,EAA4B;EAC1B,eAAO,CAACgQ,IAAD,CAAP;EACD;;EAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;EACD;;EAED,WAAO,EAAP;EACD;;EAxDoB,CAAvB;;ECfA;EACA;EACA;EACA;EACA;EACA;EAmBA;EACA;EACA;EACA;EACA;;EAEA,MAAMxE,MAAI,GAAG,UAAb;EACA,MAAMH,UAAQ,GAAG,aAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMuE,cAAc,GAAG,WAAvB;EACA,MAAMC,eAAe,GAAG,YAAxB;EACA,MAAMC,sBAAsB,GAAG,GAA/B;;EACA,MAAMC,eAAe,GAAG,EAAxB;EAEA,MAAMC,SAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,KAHO;EAIdC,EAAAA,KAAK,EAAE,OAJO;EAKdC,EAAAA,IAAI,EAAE,IALQ;EAMdC,EAAAA,KAAK,EAAE;EANO,CAAhB;EASA,MAAMC,aAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,KAAK,EAAE,kBAJW;EAKlBC,EAAAA,IAAI,EAAE,SALY;EAMlBC,EAAAA,KAAK,EAAE;EANW,CAApB;EASA,MAAME,UAAU,GAAG,MAAnB;EACA,MAAMC,UAAU,GAAG,MAAnB;EACA,MAAMC,cAAc,GAAG,MAAvB;EACA,MAAMC,eAAe,GAAG,OAAxB;EAEA,MAAMC,WAAW,GAAI,QAAOxF,WAAU,EAAtC;EACA,MAAMyF,UAAU,GAAI,OAAMzF,WAAU,EAApC;EACA,MAAM0F,aAAa,GAAI,UAAS1F,WAAU,EAA1C;EACA,MAAM2F,gBAAgB,GAAI,aAAY3F,WAAU,EAAhD;EACA,MAAM4F,gBAAgB,GAAI,aAAY5F,WAAU,EAAhD;EACA,MAAM6F,gBAAgB,GAAI,aAAY7F,WAAU,EAAhD;EACA,MAAM8F,eAAe,GAAI,YAAW9F,WAAU,EAA9C;EACA,MAAM+F,cAAc,GAAI,WAAU/F,WAAU,EAA5C;EACA,MAAMgG,iBAAiB,GAAI,cAAahG,WAAU,EAAlD;EACA,MAAMiG,eAAe,GAAI,YAAWjG,WAAU,EAA9C;EACA,MAAMkG,gBAAgB,GAAI,YAAWlG,WAAU,EAA/C;EACA,MAAMmG,qBAAmB,GAAI,OAAMnG,WAAU,GAAEC,cAAa,EAA5D;EACA,MAAMI,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMmG,mBAAmB,GAAG,UAA5B;EACA,MAAM7E,mBAAiB,GAAG,QAA1B;EACA,MAAM8E,gBAAgB,GAAG,OAAzB;EACA,MAAMC,cAAc,GAAG,mBAAvB;EACA,MAAMC,gBAAgB,GAAG,qBAAzB;EACA,MAAMC,eAAe,GAAG,oBAAxB;EACA,MAAMC,eAAe,GAAG,oBAAxB;EACA,MAAMC,wBAAwB,GAAG,eAAjC;EAEA,MAAMC,iBAAe,GAAG,SAAxB;EACA,MAAMC,oBAAoB,GAAG,uBAA7B;EACA,MAAMC,aAAa,GAAG,gBAAtB;EACA,MAAMC,iBAAiB,GAAG,oBAA1B;EACA,MAAMC,kBAAkB,GAAG,0CAA3B;EACA,MAAMC,mBAAmB,GAAG,sBAA5B;EACA,MAAMC,kBAAkB,GAAG,kBAA3B;EACA,MAAMC,mBAAmB,GAAG,qCAA5B;EACA,MAAMC,kBAAkB,GAAG,2BAA3B;EAEA,MAAMC,kBAAkB,GAAG,OAA3B;EACA,MAAMC,gBAAgB,GAAG,KAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,QAAN,SAAuB9H,aAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAKkT,MAAL,GAAc,IAAd;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAiB,KAAjB;EACA,SAAKC,UAAL,GAAkB,KAAlB;EACA,SAAKC,YAAL,GAAoB,IAApB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKwR,kBAAL,GAA0B1E,cAAc,CAACK,OAAf,CAAuBoD,mBAAvB,EAA4C,KAAKtH,QAAjD,CAA1B;EACA,SAAKwI,eAAL,GAAuB,kBAAkBhU,QAAQ,CAACmE,eAA3B,IAA8C8P,SAAS,CAACC,cAAV,GAA2B,CAAhG;EACA,SAAKC,aAAL,GAAqB1K,OAAO,CAACxI,MAAM,CAACmT,YAAR,CAA5B;;EAEA,SAAKC,kBAAL;EACD,GAnBkC;;;EAuBjB,aAAP3D,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GA7BkC;;;EAiCnC0E,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYpD,UAAZ;EACD;EACF;;EAEDqD,EAAAA,eAAe,GAAG;EAChB;EACA;EACA,QAAI,CAACvU,QAAQ,CAACwU,MAAV,IAAoBpR,SAAS,CAAC,KAAKoI,QAAN,CAAjC,EAAkD;EAChD,WAAK4E,IAAL;EACD;EACF;;EAEDH,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYnD,UAAZ;EACD;EACF;;EAEDL,EAAAA,KAAK,CAACpJ,KAAD,EAAQ;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK8L,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAInE,cAAc,CAACK,OAAf,CAAuBmD,kBAAvB,EAA2C,KAAKrH,QAAhD,CAAJ,EAA+D;EAC7DjK,MAAAA,oBAAoB,CAAC,KAAKiK,QAAN,CAApB;EACA,WAAKiJ,KAAL,CAAW,IAAX;EACD;;EAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;EAEDmB,EAAAA,KAAK,CAAC/M,KAAD,EAAQ;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK8L,SAAL,GAAiB,KAAjB;EACD;;EAED,QAAI,KAAKF,SAAT,EAAoB;EAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,WAAKA,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;EAC5D,WAAKmB,eAAL;;EAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAAC5U,QAAQ,CAAC6U,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8D0E,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAalD,QAFa,CAA5B;EAID;EACF;;EAEDoE,EAAAA,EAAE,CAACC,KAAD,EAAQ;EACR,SAAKzB,cAAL,GAAsBlE,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAtB;;EACA,UAAMyJ,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK3B,cAAxB,CAApB;;EAEA,QAAIyB,KAAK,GAAG,KAAK3B,MAAL,CAAY/K,MAAZ,GAAqB,CAA7B,IAAkC0M,KAAK,GAAG,CAA9C,EAAiD;EAC/C;EACD;;EAED,QAAI,KAAKvB,UAAT,EAAqB;EACnB5L,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC+F,UAAhC,EAA4C,MAAM,KAAKwD,EAAL,CAAQC,KAAR,CAAlD;EACA;EACD;;EAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,WAAKlE,KAAL;EACA,WAAK2D,KAAL;EACA;EACD;;EAED,UAAMU,KAAK,GAAGH,KAAK,GAAGC,WAAR,GACZ/D,UADY,GAEZC,UAFF;;EAIA,SAAKmD,MAAL,CAAYa,KAAZ,EAAmB,KAAK9B,MAAL,CAAY2B,KAAZ,CAAnB;EACD;;EAEDrJ,EAAAA,OAAO,GAAG;EACR9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCM,WAAhC;EAEA,SAAKuH,MAAL,GAAc,IAAd;EACA,SAAKQ,OAAL,GAAe,IAAf;EACA,SAAKP,SAAL,GAAiB,IAAjB;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,UAAL,GAAkB,IAAlB;EACA,SAAKF,cAAL,GAAsB,IAAtB;EACA,SAAKQ,kBAAL,GAA0B,IAA1B;EAEA,UAAMpI,OAAN;EACD,GA7HkC;;;EAiInCmI,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAGnO;EAFI,KAAT;EAIAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAED6S,EAAAA,YAAY,GAAG;EACb,UAAMC,SAAS,GAAGxV,IAAI,CAACyV,GAAL,CAAS,KAAK1B,WAAd,CAAlB;;EAEA,QAAIyB,SAAS,IAAI5E,eAAjB,EAAkC;EAChC;EACD;;EAED,UAAM8E,SAAS,GAAGF,SAAS,GAAG,KAAKzB,WAAnC;EAEA,SAAKA,WAAL,GAAmB,CAAnB;;EAEA,QAAI,CAAC2B,SAAL,EAAgB;EACd;EACD;;EAED,SAAKjB,MAAL,CAAYiB,SAAS,GAAG,CAAZ,GAAgBlE,eAAhB,GAAkCD,cAA9C;EACD;;EAEDiD,EAAAA,kBAAkB,GAAG;EACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;EACzB/I,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BgG,aAA/B,EAA8C9J,KAAK,IAAI,KAAK8N,QAAL,CAAc9N,KAAd,CAAvD;EACD;;EAED,QAAI,KAAKmM,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClCjJ,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BiG,gBAA/B,EAAiD/J,KAAK,IAAI,KAAKoJ,KAAL,CAAWpJ,KAAX,CAA1D;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BkG,gBAA/B,EAAiDhK,KAAK,IAAI,KAAK+M,KAAL,CAAW/M,KAAX,CAA1D;EACD;;EAED,QAAI,KAAKmM,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;EAC9C,WAAKyB,uBAAL;EACD;EACF;;EAEDA,EAAAA,uBAAuB,GAAG;EACxB,UAAMC,KAAK,GAAGhO,KAAK,IAAI;EACrB,UAAI,KAAKyM,aAAL,KAAuBzM,KAAK,CAACiO,WAAN,KAAsBxC,gBAAtB,IAA0CzL,KAAK,CAACiO,WAAN,KAAsBzC,kBAAvF,CAAJ,EAAgH;EAC9G,aAAKS,WAAL,GAAmBjM,KAAK,CAACkO,OAAzB;EACD,OAFD,MAEO,IAAI,CAAC,KAAKzB,aAAV,EAAyB;EAC9B,aAAKR,WAAL,GAAmBjM,KAAK,CAACmO,OAAN,CAAc,CAAd,EAAiBD,OAApC;EACD;EACF,KAND;;EAQA,UAAME,IAAI,GAAGpO,KAAK,IAAI;EACpB;EACA,WAAKkM,WAAL,GAAmBlM,KAAK,CAACmO,OAAN,IAAiBnO,KAAK,CAACmO,OAAN,CAAcvN,MAAd,GAAuB,CAAxC,GACjB,CADiB,GAEjBZ,KAAK,CAACmO,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,KAAKjC,WAFlC;EAGD,KALD;;EAOA,UAAMoC,GAAG,GAAGrO,KAAK,IAAI;EACnB,UAAI,KAAKyM,aAAL,KAAuBzM,KAAK,CAACiO,WAAN,KAAsBxC,gBAAtB,IAA0CzL,KAAK,CAACiO,WAAN,KAAsBzC,kBAAvF,CAAJ,EAAgH;EAC9G,aAAKU,WAAL,GAAmBlM,KAAK,CAACkO,OAAN,GAAgB,KAAKjC,WAAxC;EACD;;EAED,WAAKyB,YAAL;;EACA,UAAI,KAAKvB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,aAAKA,KAAL;;EACA,YAAI,KAAK4C,YAAT,EAAuB;EACrBsC,UAAAA,YAAY,CAAC,KAAKtC,YAAN,CAAZ;EACD;;EAED,aAAKA,YAAL,GAAoBtR,UAAU,CAACsF,KAAK,IAAI,KAAK+M,KAAL,CAAW/M,KAAX,CAAV,EAA6B8I,sBAAsB,GAAG,KAAKqD,OAAL,CAAalD,QAAnE,CAA9B;EACD;EACF,KAtBD;;EAwBAtB,IAAAA,cAAc,CAACC,IAAf,CAAoBsD,iBAApB,EAAuC,KAAKpH,QAA5C,EAAsD7I,OAAtD,CAA8DsT,OAAO,IAAI;EACvEpO,MAAAA,YAAY,CAACiC,EAAb,CAAgBmM,OAAhB,EAAyBjE,gBAAzB,EAA2CkE,CAAC,IAAIA,CAAC,CAAC9K,cAAF,EAAhD;EACD,KAFD;;EAIA,QAAI,KAAK+I,aAAT,EAAwB;EACtBtM,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BsG,iBAA/B,EAAkDpK,KAAK,IAAIgO,KAAK,CAAChO,KAAD,CAAhE;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BuG,eAA/B,EAAgDrK,KAAK,IAAIqO,GAAG,CAACrO,KAAD,CAA5D;;EAEA,WAAK8D,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B3D,wBAA5B;EACD,KALD,MAKO;EACL3K,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BmG,gBAA/B,EAAiDjK,KAAK,IAAIgO,KAAK,CAAChO,KAAD,CAA/D;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BoG,eAA/B,EAAgDlK,KAAK,IAAIoO,IAAI,CAACpO,KAAD,CAA7D;EACAG,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BqG,cAA/B,EAA+CnK,KAAK,IAAIqO,GAAG,CAACrO,KAAD,CAA3D;EACD;EACF;;EAED8N,EAAAA,QAAQ,CAAC9N,KAAD,EAAQ;EACd,QAAI,kBAAkBzE,IAAlB,CAAuByE,KAAK,CAACU,MAAN,CAAagO,OAApC,CAAJ,EAAkD;EAChD;EACD;;EAED,QAAI1O,KAAK,CAAC3B,GAAN,KAAcuK,cAAlB,EAAkC;EAChC5I,MAAAA,KAAK,CAAC0D,cAAN;;EACA,WAAKkJ,MAAL,CAAYlD,cAAZ;EACD,KAHD,MAGO,IAAI1J,KAAK,CAAC3B,GAAN,KAAcwK,eAAlB,EAAmC;EACxC7I,MAAAA,KAAK,CAAC0D,cAAN;;EACA,WAAKkJ,MAAL,CAAYjD,eAAZ;EACD;EACF;;EAED6D,EAAAA,aAAa,CAAC/U,OAAD,EAAU;EACrB,SAAKkT,MAAL,GAAclT,OAAO,IAAIA,OAAO,CAACmD,UAAnB,GACZ+L,cAAc,CAACC,IAAf,CAAoBqD,aAApB,EAAmCxS,OAAO,CAACmD,UAA3C,CADY,GAEZ,EAFF;EAIA,WAAO,KAAK+P,MAAL,CAAYgD,OAAZ,CAAoBlW,OAApB,CAAP;EACD;;EAEDmW,EAAAA,eAAe,CAACnB,KAAD,EAAQoB,aAAR,EAAuB;EACpC,UAAMC,MAAM,GAAGrB,KAAK,KAAKjE,UAAzB;EACA,UAAMuF,MAAM,GAAGtB,KAAK,KAAKhE,UAAzB;;EACA,UAAM8D,WAAW,GAAG,KAAKC,aAAL,CAAmBqB,aAAnB,CAApB;;EACA,UAAMG,aAAa,GAAG,KAAKrD,MAAL,CAAY/K,MAAZ,GAAqB,CAA3C;EACA,UAAMqO,aAAa,GAAIF,MAAM,IAAIxB,WAAW,KAAK,CAA3B,IAAkCuB,MAAM,IAAIvB,WAAW,KAAKyB,aAAlF;;EAEA,QAAIC,aAAa,IAAI,CAAC,KAAK9C,OAAL,CAAa9C,IAAnC,EAAyC;EACvC,aAAOwF,aAAP;EACD;;EAED,UAAMK,KAAK,GAAGH,MAAM,GAAG,CAAC,CAAJ,GAAQ,CAA5B;EACA,UAAMI,SAAS,GAAG,CAAC5B,WAAW,GAAG2B,KAAf,IAAwB,KAAKvD,MAAL,CAAY/K,MAAtD;EAEA,WAAOuO,SAAS,KAAK,CAAC,CAAf,GACL,KAAKxD,MAAL,CAAY,KAAKA,MAAL,CAAY/K,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAK+K,MAAL,CAAYwD,SAAZ,CAFF;EAGD;;EAEDC,EAAAA,kBAAkB,CAACC,aAAD,EAAgBC,kBAAhB,EAAoC;EACpD,UAAMC,WAAW,GAAG,KAAK/B,aAAL,CAAmB6B,aAAnB,CAApB;;EACA,UAAMG,SAAS,GAAG,KAAKhC,aAAL,CAAmB7F,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAnB,CAAlB;;EAEA,WAAO3D,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC8F,WAApC,EAAiD;EACtDyF,MAAAA,aADsD;EAEtDxB,MAAAA,SAAS,EAAEyB,kBAF2C;EAGtDxQ,MAAAA,IAAI,EAAE0Q,SAHgD;EAItDnC,MAAAA,EAAE,EAAEkC;EAJkD,KAAjD,CAAP;EAMD;;EAEDE,EAAAA,0BAA0B,CAAChX,OAAD,EAAU;EAClC,QAAI,KAAK4T,kBAAT,EAA6B;EAC3B,YAAMqD,eAAe,GAAG/H,cAAc,CAACK,OAAf,CAAuB+C,iBAAvB,EAAwC,KAAKsB,kBAA7C,CAAxB;EAEAqD,MAAAA,eAAe,CAACtT,SAAhB,CAA0B2C,MAA1B,CAAiC4G,mBAAjC;EACA+J,MAAAA,eAAe,CAAClJ,eAAhB,CAAgC,cAAhC;EAEA,YAAMmJ,UAAU,GAAGhI,cAAc,CAACC,IAAf,CAAoByD,kBAApB,EAAwC,KAAKgB,kBAA7C,CAAnB;;EAEA,WAAK,IAAI1L,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgP,UAAU,CAAC/O,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;EAC1C,YAAIjH,MAAM,CAACkW,QAAP,CAAgBD,UAAU,CAAChP,CAAD,CAAV,CAAchI,YAAd,CAA2B,kBAA3B,CAAhB,EAAgE,EAAhE,MAAwE,KAAK6U,aAAL,CAAmB/U,OAAnB,CAA5E,EAAyG;EACvGkX,UAAAA,UAAU,CAAChP,CAAD,CAAV,CAAcvE,SAAd,CAAwBqS,GAAxB,CAA4B9I,mBAA5B;EACAgK,UAAAA,UAAU,CAAChP,CAAD,CAAV,CAAcoF,YAAd,CAA2B,cAA3B,EAA2C,MAA3C;EACA;EACD;EACF;EACF;EACF;;EAEDkH,EAAAA,eAAe,GAAG;EAChB,UAAMxU,OAAO,GAAG,KAAKoT,cAAL,IAAuBlE,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAvC;;EAEA,QAAI,CAACrL,OAAL,EAAc;EACZ;EACD;;EAED,UAAMoX,eAAe,GAAGnW,MAAM,CAACkW,QAAP,CAAgBnX,OAAO,CAACE,YAAR,CAAqB,kBAArB,CAAhB,EAA0D,EAA1D,CAAxB;;EAEA,QAAIkX,eAAJ,EAAqB;EACnB,WAAK1D,OAAL,CAAa2D,eAAb,GAA+B,KAAK3D,OAAL,CAAa2D,eAAb,IAAgC,KAAK3D,OAAL,CAAalD,QAA5E;EACA,WAAKkD,OAAL,CAAalD,QAAb,GAAwB4G,eAAxB;EACD,KAHD,MAGO;EACL,WAAK1D,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa2D,eAAb,IAAgC,KAAK3D,OAAL,CAAalD,QAArE;EACD;EACF;;EAED2D,EAAAA,MAAM,CAACmD,gBAAD,EAAmBtX,OAAnB,EAA4B;EAChC,UAAMgV,KAAK,GAAG,KAAKuC,iBAAL,CAAuBD,gBAAvB,CAAd;;EACA,UAAMlB,aAAa,GAAGlH,cAAc,CAACK,OAAf,CAAuBgD,oBAAvB,EAA6C,KAAKlH,QAAlD,CAAtB;;EACA,UAAMmM,kBAAkB,GAAG,KAAKzC,aAAL,CAAmBqB,aAAnB,CAA3B;;EACA,UAAMqB,WAAW,GAAGzX,OAAO,IAAI,KAAKmW,eAAL,CAAqBnB,KAArB,EAA4BoB,aAA5B,CAA/B;;EAEA,UAAMsB,gBAAgB,GAAG,KAAK3C,aAAL,CAAmB0C,WAAnB,CAAzB;;EACA,UAAME,SAAS,GAAGrO,OAAO,CAAC,KAAK6J,SAAN,CAAzB;EAEA,UAAMkD,MAAM,GAAGrB,KAAK,KAAKjE,UAAzB;EACA,UAAM6G,oBAAoB,GAAGvB,MAAM,GAAGnE,gBAAH,GAAsBD,cAAzD;EACA,UAAM4F,cAAc,GAAGxB,MAAM,GAAGlE,eAAH,GAAqBC,eAAlD;;EACA,UAAMyE,kBAAkB,GAAG,KAAKiB,iBAAL,CAAuB9C,KAAvB,CAA3B;;EAEA,QAAIyC,WAAW,IAAIA,WAAW,CAAC9T,SAAZ,CAAsBC,QAAtB,CAA+BsJ,mBAA/B,CAAnB,EAAsE;EACpE,WAAKoG,UAAL,GAAkB,KAAlB;EACA;EACD;;EAED,UAAMyE,UAAU,GAAG,KAAKpB,kBAAL,CAAwBc,WAAxB,EAAqCZ,kBAArC,CAAnB;;EACA,QAAIkB,UAAU,CAACxN,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAI,CAAC6L,aAAD,IAAkB,CAACqB,WAAvB,EAAoC;EAClC;EACA;EACD;;EAED,SAAKnE,UAAL,GAAkB,IAAlB;;EAEA,QAAIqE,SAAJ,EAAe;EACb,WAAKhH,KAAL;EACD;;EAED,SAAKqG,0BAAL,CAAgCS,WAAhC;;EACA,SAAKrE,cAAL,GAAsBqE,WAAtB;;EAEA,QAAI,KAAKpM,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCoO,gBAAjC,CAAJ,EAAwD;EACtDyF,MAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B6B,cAA1B;EAEAvT,MAAAA,MAAM,CAACmT,WAAD,CAAN;EAEArB,MAAAA,aAAa,CAACzS,SAAd,CAAwBqS,GAAxB,CAA4B4B,oBAA5B;EACAH,MAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B4B,oBAA1B;EAEA,YAAMhX,kBAAkB,GAAGD,gCAAgC,CAACyV,aAAD,CAA3D;EAEA1O,MAAAA,YAAY,CAACkC,GAAb,CAAiBwM,aAAjB,EAAgC,eAAhC,EAAiD,MAAM;EACrDqB,QAAAA,WAAW,CAAC9T,SAAZ,CAAsB2C,MAAtB,CAA6BsR,oBAA7B,EAAmDC,cAAnD;EACAJ,QAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B9I,mBAA1B;EAEAkJ,QAAAA,aAAa,CAACzS,SAAd,CAAwB2C,MAAxB,CAA+B4G,mBAA/B,EAAkD2K,cAAlD,EAAkED,oBAAlE;EAEA,aAAKtE,UAAL,GAAkB,KAAlB;EAEArR,QAAAA,UAAU,CAAC,MAAM;EACfyF,UAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC+F,UAApC,EAAgD;EAC9CwF,YAAAA,aAAa,EAAEa,WAD+B;EAE9CrC,YAAAA,SAAS,EAAEyB,kBAFmC;EAG9CxQ,YAAAA,IAAI,EAAEmR,kBAHwC;EAI9C5C,YAAAA,EAAE,EAAE8C;EAJ0C,WAAhD;EAMD,SAPS,EAOP,CAPO,CAAV;EAQD,OAhBD;EAkBAjW,MAAAA,oBAAoB,CAAC2U,aAAD,EAAgBxV,kBAAhB,CAApB;EACD,KA7BD,MA6BO;EACLwV,MAAAA,aAAa,CAACzS,SAAd,CAAwB2C,MAAxB,CAA+B4G,mBAA/B;EACAuK,MAAAA,WAAW,CAAC9T,SAAZ,CAAsBqS,GAAtB,CAA0B9I,mBAA1B;EAEA,WAAKoG,UAAL,GAAkB,KAAlB;EACA5L,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC+F,UAApC,EAAgD;EAC9CwF,QAAAA,aAAa,EAAEa,WAD+B;EAE9CrC,QAAAA,SAAS,EAAEyB,kBAFmC;EAG9CxQ,QAAAA,IAAI,EAAEmR,kBAHwC;EAI9C5C,QAAAA,EAAE,EAAE8C;EAJ0C,OAAhD;EAMD;;EAED,QAAIC,SAAJ,EAAe;EACb,WAAKrD,KAAL;EACD;EACF;;EAEDiD,EAAAA,iBAAiB,CAACnC,SAAD,EAAY;EAC3B,QAAI,CAAC,CAAClE,eAAD,EAAkBD,cAAlB,EAAkC7Q,QAAlC,CAA2CgV,SAA3C,CAAL,EAA4D;EAC1D,aAAOA,SAAP;EACD;;EAED,QAAItQ,KAAK,EAAT,EAAa;EACX,aAAOsQ,SAAS,KAAKlE,eAAd,GAAgCF,UAAhC,GAA6CD,UAApD;EACD;;EAED,WAAOqE,SAAS,KAAKlE,eAAd,GAAgCH,UAAhC,GAA6CC,UAApD;EACD;;EAED8G,EAAAA,iBAAiB,CAAC9C,KAAD,EAAQ;EACvB,QAAI,CAAC,CAACjE,UAAD,EAAaC,UAAb,EAAyB5Q,QAAzB,CAAkC4U,KAAlC,CAAL,EAA+C;EAC7C,aAAOA,KAAP;EACD;;EAED,QAAIlQ,KAAK,EAAT,EAAa;EACX,aAAOkQ,KAAK,KAAKjE,UAAV,GAAuBE,cAAvB,GAAwCC,eAA/C;EACD;;EAED,WAAO8D,KAAK,KAAKjE,UAAV,GAAuBG,eAAvB,GAAyCD,cAAhD;EACD,GAvakC;;;EA2aX,SAAjB+G,iBAAiB,CAAChY,OAAD,EAAUoC,MAAV,EAAkB;EACxC,QAAI2K,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAX;EACA,QAAImI,OAAO,GAAG,EACZ,GAAGnD,SADS;EAEZ,SAAG3C,WAAW,CAACI,iBAAZ,CAA8BhO,OAA9B;EAFS,KAAd;;EAKA,QAAI,OAAOoC,MAAP,KAAkB,QAAtB,EAAgC;EAC9BsR,MAAAA,OAAO,GAAG,EACR,GAAGA,OADK;EAER,WAAGtR;EAFK,OAAV;EAID;;EAED,UAAM6V,MAAM,GAAG,OAAO7V,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCsR,OAAO,CAAChD,KAA7D;;EAEA,QAAI,CAAC3D,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIkG,QAAJ,CAAajT,OAAb,EAAsB0T,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B2K,MAAAA,IAAI,CAAC6H,EAAL,CAAQxS,MAAR;EACD,KAFD,MAEO,IAAI,OAAO6V,MAAP,KAAkB,QAAtB,EAAgC;EACrC,UAAI,OAAOlL,IAAI,CAACkL,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIlV,SAAJ,CAAe,oBAAmBkV,MAAO,GAAzC,CAAN;EACD;;EAEDlL,MAAAA,IAAI,CAACkL,MAAD,CAAJ;EACD,KANM,MAMA,IAAIvE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACwE,IAAhC,EAAsC;EAC3CnL,MAAAA,IAAI,CAAC4D,KAAL;EACA5D,MAAAA,IAAI,CAACuH,KAAL;EACD;EACF;;EAEqB,SAAfhP,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3BmG,MAAAA,QAAQ,CAAC+E,iBAAT,CAA2B,IAA3B,EAAiC5V,MAAjC;EACD,KAFM,CAAP;EAGD;;EAEyB,SAAnB+V,mBAAmB,CAAC5Q,KAAD,EAAQ;EAChC,UAAMU,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,QAAI,CAACuH,MAAD,IAAW,CAACA,MAAM,CAACtE,SAAP,CAAiBC,QAAjB,CAA0BmO,mBAA1B,CAAhB,EAAgE;EAC9D;EACD;;EAED,UAAM3P,MAAM,GAAG,EACb,GAAGwL,WAAW,CAACI,iBAAZ,CAA8B/F,MAA9B,CADU;EAEb,SAAG2F,WAAW,CAACI,iBAAZ,CAA8B,IAA9B;EAFU,KAAf;EAIA,UAAMoK,UAAU,GAAG,KAAKlY,YAAL,CAAkB,kBAAlB,CAAnB;;EAEA,QAAIkY,UAAJ,EAAgB;EACdhW,MAAAA,MAAM,CAACoO,QAAP,GAAkB,KAAlB;EACD;;EAEDyC,IAAAA,QAAQ,CAAC+E,iBAAT,CAA2B/P,MAA3B,EAAmC7F,MAAnC;;EAEA,QAAIgW,UAAJ,EAAgB;EACd9M,MAAAA,IAAI,CAACtF,GAAL,CAASiC,MAAT,EAAiBsD,UAAjB,EAA2BqJ,EAA3B,CAA8BwD,UAA9B;EACD;;EAED7Q,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EA3ekC;EA8erC;EACA;EACA;EACA;EACA;;;EAEAvD,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgD6G,mBAAhD,EAAqEI,QAAQ,CAACkF,mBAA9E;EAEAzQ,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwBgR,qBAAxB,EAA6C,MAAM;EACjD,QAAMuG,SAAS,GAAGnJ,cAAc,CAACC,IAAf,CAAoB2D,kBAApB,CAAlB;;EAEA,OAAK,IAAI5K,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG6P,SAAS,CAAClQ,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;EACpD+K,IAAAA,QAAQ,CAAC+E,iBAAT,CAA2BK,SAAS,CAACnQ,CAAD,CAApC,EAAyCoD,IAAI,CAACtF,GAAL,CAASqS,SAAS,CAACnQ,CAAD,CAAlB,EAAuBqD,UAAvB,CAAzC;EACD;EACF,CAND;EAQA;EACA;EACA;EACA;EACA;EACA;;EAEAvG,kBAAkB,CAAC0G,MAAD,EAAOuH,QAAP,CAAlB;;EC7mBA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;EACA;EACA;;EAEA,MAAMvH,MAAI,GAAG,UAAb;EACA,MAAMH,UAAQ,GAAG,aAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAM2E,SAAO,GAAG;EACdlD,EAAAA,MAAM,EAAE,IADM;EAEdiL,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,MAAMxH,aAAW,GAAG;EAClBzD,EAAAA,MAAM,EAAE,SADU;EAElBiL,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,MAAMC,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAM8M,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMO,iBAAe,GAAG,MAAxB;EACA,MAAMwM,mBAAmB,GAAG,UAA5B;EACA,MAAMC,qBAAqB,GAAG,YAA9B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EAEA,MAAMC,KAAK,GAAG,OAAd;EACA,MAAMC,MAAM,GAAG,QAAf;EAEA,MAAMC,gBAAgB,GAAG,oBAAzB;EACA,MAAM7L,sBAAoB,GAAG,6BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAM8L,QAAN,SAAuB9N,aAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAKkZ,gBAAL,GAAwB,KAAxB;EACA,SAAKxF,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAK+W,aAAL,GAAqBjK,cAAc,CAACC,IAAf,CAClB,GAAEhC,sBAAqB,WAAU,KAAK9B,QAAL,CAAc+N,EAAG,KAAnD,GACC,GAAEjM,sBAAqB,qBAAoB,KAAK9B,QAAL,CAAc+N,EAAG,IAF1C,CAArB;EAKA,UAAMC,UAAU,GAAGnK,cAAc,CAACC,IAAf,CAAoBhC,sBAApB,CAAnB;;EAEA,SAAK,IAAIjF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG6Q,UAAU,CAAClR,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;EACrD,YAAMoR,IAAI,GAAGD,UAAU,CAACnR,CAAD,CAAvB;EACA,YAAMjI,QAAQ,GAAGO,sBAAsB,CAAC8Y,IAAD,CAAvC;EACA,YAAMC,aAAa,GAAGrK,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EACnBkO,MADmB,CACZqL,SAAS,IAAIA,SAAS,KAAK,KAAKnO,QADpB,CAAtB;;EAGA,UAAIpL,QAAQ,KAAK,IAAb,IAAqBsZ,aAAa,CAACpR,MAAvC,EAA+C;EAC7C,aAAKsR,SAAL,GAAiBxZ,QAAjB;;EACA,aAAKkZ,aAAL,CAAmBtJ,IAAnB,CAAwByJ,IAAxB;EACD;EACF;;EAED,SAAKI,OAAL,GAAe,KAAKhG,OAAL,CAAa4E,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAKjG,OAAL,CAAa4E,MAAlB,EAA0B;EACxB,WAAKsB,yBAAL,CAA+B,KAAKvO,QAApC,EAA8C,KAAK8N,aAAnD;EACD;;EAED,QAAI,KAAKzF,OAAL,CAAarG,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;EACF,GAlCkC;;;EAsCjB,aAAPkD,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GA5CkC;;;EAgDnC8B,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKhC,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAAJ,EAAuD;EACrD,WAAK0N,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKZ,gBAAL,IAAyB,KAAK7N,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAA7B,EAAgF;EAC9E;EACD;;EAED,QAAI4N,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKN,OAAT,EAAkB;EAChBK,MAAAA,OAAO,GAAG7K,cAAc,CAACC,IAAf,CAAoB6J,gBAApB,EAAsC,KAAKU,OAA3C,EACPvL,MADO,CACAmL,IAAI,IAAI;EACd,YAAI,OAAO,KAAK5F,OAAL,CAAa4E,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAOgB,IAAI,CAACpZ,YAAL,CAAkB,gBAAlB,MAAwC,KAAKwT,OAAL,CAAa4E,MAA5D;EACD;;EAED,eAAOgB,IAAI,CAAC3V,SAAL,CAAeC,QAAf,CAAwB+U,mBAAxB,CAAP;EACD,OAPO,CAAV;;EASA,UAAIoB,OAAO,CAAC5R,MAAR,KAAmB,CAAvB,EAA0B;EACxB4R,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,UAAME,SAAS,GAAG/K,cAAc,CAACK,OAAf,CAAuB,KAAKkK,SAA5B,CAAlB;;EACA,QAAIM,OAAJ,EAAa;EACX,YAAMG,cAAc,GAAGH,OAAO,CAAC5K,IAAR,CAAamK,IAAI,IAAIW,SAAS,KAAKX,IAAnC,CAAvB;EACAU,MAAAA,WAAW,GAAGE,cAAc,GAAG5O,IAAI,CAACtF,GAAL,CAASkU,cAAT,EAAyB3O,UAAzB,CAAH,GAAwC,IAApE;;EAEA,UAAIyO,WAAW,IAAIA,WAAW,CAACd,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,UAAMiB,UAAU,GAAGzS,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,CAAnB;;EACA,QAAI4B,UAAU,CAAC5P,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAIwP,OAAJ,EAAa;EACXA,MAAAA,OAAO,CAACvX,OAAR,CAAgB4X,UAAU,IAAI;EAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;EAC5BnB,UAAAA,QAAQ,CAACoB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;EACD;;EAED,YAAI,CAACJ,WAAL,EAAkB;EAChB1O,UAAAA,IAAI,CAAC3F,GAAL,CAASyU,UAAT,EAAqB7O,UAArB,EAA+B,IAA/B;EACD;EACF,OARD;EASD;;EAED,UAAM+O,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKlP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BqS,mBAA/B;;EACA,SAAKtN,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B4C,qBAA5B;;EAEA,SAAKvN,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKnB,aAAL,CAAmBhR,MAAvB,EAA+B;EAC7B,WAAKgR,aAAL,CAAmB3W,OAAnB,CAA2BxC,OAAO,IAAI;EACpCA,QAAAA,OAAO,CAAC2D,SAAR,CAAkB2C,MAAlB,CAAyBuS,oBAAzB;EACA7Y,QAAAA,OAAO,CAACsN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD,OAHD;EAID;;EAED,SAAKkN,gBAAL,CAAsB,IAAtB;;EAEA,UAAMC,QAAQ,GAAG,MAAM;EACrB,WAAKpP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BsS,qBAA/B;;EACA,WAAKvN,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B2C,mBAA5B,EAAiDxM,iBAAjD;;EAEA,WAAKd,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAiC,EAAjC;EAEA,WAAKE,gBAAL,CAAsB,KAAtB;EAEA9S,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC;EACD,KATD;;EAWA,UAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAatX,WAAb,KAA6BsX,SAAS,CAACtQ,KAAV,CAAgB,CAAhB,CAA1D;EACA,UAAM2Q,UAAU,GAAI,SAAQD,oBAAqB,EAAjD;EACA,UAAM9Z,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EAEAhZ,IAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACA,SAAKyK,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAkC,GAAE,KAAKjP,QAAL,CAAcsP,UAAd,CAA0B,IAA9D;EACD;;EAEDd,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKX,gBAAL,IAAyB,CAAC,KAAK7N,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAA9B,EAAiF;EAC/E;EACD;;EAED,UAAMgO,UAAU,GAAGzS,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,CAAnB;;EACA,QAAI0B,UAAU,CAAC5P,gBAAf,EAAiC;EAC/B;EACD;;EAED,UAAM+P,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKlP,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAkC,GAAE,KAAKjP,QAAL,CAAcoD,qBAAd,GAAsC6L,SAAtC,CAAiD,IAArF;EAEAhW,IAAAA,MAAM,CAAC,KAAK+G,QAAN,CAAN;;EAEA,SAAKA,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B4C,qBAA5B;;EACA,SAAKvN,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BqS,mBAA/B,EAAoDxM,iBAApD;;EAEA,UAAMyO,kBAAkB,GAAG,KAAKzB,aAAL,CAAmBhR,MAA9C;;EACA,QAAIyS,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAI1S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0S,kBAApB,EAAwC1S,CAAC,EAAzC,EAA6C;EAC3C,cAAMgC,OAAO,GAAG,KAAKiP,aAAL,CAAmBjR,CAAnB,CAAhB;EACA,cAAMoR,IAAI,GAAG5Y,sBAAsB,CAACwJ,OAAD,CAAnC;;EAEA,YAAIoP,IAAI,IAAI,CAACA,IAAI,CAAC3V,SAAL,CAAeC,QAAf,CAAwBuI,iBAAxB,CAAb,EAAuD;EACrDjC,UAAAA,OAAO,CAACvG,SAAR,CAAkBqS,GAAlB,CAAsB6C,oBAAtB;EACA3O,UAAAA,OAAO,CAACoD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;EACD;EACF;EACF;;EAED,SAAKkN,gBAAL,CAAsB,IAAtB;;EAEA,UAAMC,QAAQ,GAAG,MAAM;EACrB,WAAKD,gBAAL,CAAsB,KAAtB;;EACA,WAAKnP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+BsS,qBAA/B;;EACA,WAAKvN,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B2C,mBAA5B;;EACAjR,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC;EACD,KALD;;EAOA,SAAKrN,QAAL,CAAcnI,KAAd,CAAoBoX,SAApB,IAAiC,EAAjC;EACA,UAAM1Z,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EACAhZ,IAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD;;EAED4Z,EAAAA,gBAAgB,CAACK,eAAD,EAAkB;EAChC,SAAK3B,gBAAL,GAAwB2B,eAAxB;EACD;;EAEDrP,EAAAA,OAAO,GAAG;EACR,UAAMA,OAAN;EACA,SAAKkI,OAAL,GAAe,IAAf;EACA,SAAKgG,OAAL,GAAe,IAAf;EACA,SAAKP,aAAL,GAAqB,IAArB;EACA,SAAKD,gBAAL,GAAwB,IAAxB;EACD,GAzMkC;;;EA6MnCvF,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAGnO;EAFI,KAAT;EAIAA,IAAAA,MAAM,CAACiL,MAAP,GAAgB/D,OAAO,CAAClH,MAAM,CAACiL,MAAR,CAAvB,CALiB;;EAMjBnL,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAEDmY,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKlP,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkV,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;EACD;;EAEDY,EAAAA,UAAU,GAAG;EACX,QAAI;EAAErB,MAAAA;EAAF,QAAa,KAAK5E,OAAtB;;EAEA,QAAInS,SAAS,CAAC+W,MAAD,CAAb,EAAuB;EACrB;EACA,UAAI,OAAOA,MAAM,CAACwC,MAAd,KAAyB,WAAzB,IAAwC,OAAOxC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;EAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;EACD;EACF,KALD,MAKO;EACLA,MAAAA,MAAM,GAAGpJ,cAAc,CAACK,OAAf,CAAuB+I,MAAvB,CAAT;EACD;;EAED,UAAMrY,QAAQ,GAAI,GAAEkN,sBAAqB,oBAAmBmL,MAAO,IAAnE;EAEApJ,IAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EAA8BqY,MAA9B,EACG9V,OADH,CACWxC,OAAO,IAAI;EAClB,YAAM+a,QAAQ,GAAGra,sBAAsB,CAACV,OAAD,CAAvC;;EAEA,WAAK4Z,yBAAL,CACEmB,QADF,EAEE,CAAC/a,OAAD,CAFF;EAID,KARH;EAUA,WAAOsY,MAAP;EACD;;EAEDsB,EAAAA,yBAAyB,CAAC5Z,OAAD,EAAUgb,YAAV,EAAwB;EAC/C,QAAI,CAAChb,OAAD,IAAY,CAACgb,YAAY,CAAC7S,MAA9B,EAAsC;EACpC;EACD;;EAED,UAAM8S,MAAM,GAAGjb,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2BuI,iBAA3B,CAAf;EAEA6O,IAAAA,YAAY,CAACxY,OAAb,CAAqB8W,IAAI,IAAI;EAC3B,UAAI2B,MAAJ,EAAY;EACV3B,QAAAA,IAAI,CAAC3V,SAAL,CAAe2C,MAAf,CAAsBuS,oBAAtB;EACD,OAFD,MAEO;EACLS,QAAAA,IAAI,CAAC3V,SAAL,CAAeqS,GAAf,CAAmB6C,oBAAnB;EACD;;EAEDS,MAAAA,IAAI,CAAChM,YAAL,CAAkB,eAAlB,EAAmC2N,MAAnC;EACD,KARD;EASD,GAtQkC;;;EA0QX,SAAjBZ,iBAAiB,CAACra,OAAD,EAAUoC,MAAV,EAAkB;EACxC,QAAI2K,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAX;EACA,UAAMmI,OAAO,GAAG,EACd,GAAGnD,SADW;EAEd,SAAG3C,WAAW,CAACI,iBAAZ,CAA8BhO,OAA9B,CAFW;EAGd,UAAI,OAAOoC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHc,KAAhB;;EAMA,QAAI,CAAC2K,IAAD,IAAS2G,OAAO,CAACrG,MAAjB,IAA2B,OAAOjL,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;EACrFsR,MAAAA,OAAO,CAACrG,MAAR,GAAiB,KAAjB;EACD;;EAED,QAAI,CAACN,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIkM,QAAJ,CAAajZ,OAAb,EAAsB0T,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,MAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF;;EAEqB,SAAfkD,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3BmM,MAAAA,QAAQ,CAACoB,iBAAT,CAA2B,IAA3B,EAAiCjY,MAAjC;EACD,KAFM,CAAP;EAGD;;EAvSkC;EA0SrC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrF;EACA,MAAIA,KAAK,CAACU,MAAN,CAAagO,OAAb,KAAyB,GAAzB,IAAiC1O,KAAK,CAACC,cAAN,IAAwBD,KAAK,CAACC,cAAN,CAAqByO,OAArB,KAAiC,GAA9F,EAAoG;EAClG1O,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EAED,QAAMiQ,WAAW,GAAGtN,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;EACA,QAAM/N,QAAQ,GAAGO,sBAAsB,CAAC,IAAD,CAAvC;EACA,QAAM2a,gBAAgB,GAAGjM,cAAc,CAACC,IAAf,CAAoBlP,QAApB,CAAzB;EAEAkb,EAAAA,gBAAgB,CAAC3Y,OAAjB,CAAyBxC,OAAO,IAAI;EAClC,UAAM+M,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAb;EACA,QAAInJ,MAAJ;;EACA,QAAI2K,IAAJ,EAAU;EACR;EACA,UAAIA,IAAI,CAAC2M,OAAL,KAAiB,IAAjB,IAAyB,OAAOwB,WAAW,CAAC5C,MAAnB,KAA8B,QAA3D,EAAqE;EACnEvL,QAAAA,IAAI,CAAC2G,OAAL,CAAa4E,MAAb,GAAsB4C,WAAW,CAAC5C,MAAlC;EACAvL,QAAAA,IAAI,CAAC2M,OAAL,GAAe3M,IAAI,CAAC4M,UAAL,EAAf;EACD;;EAEDvX,MAAAA,MAAM,GAAG,QAAT;EACD,KARD,MAQO;EACLA,MAAAA,MAAM,GAAG8Y,WAAT;EACD;;EAEDjC,IAAAA,QAAQ,CAACoB,iBAAT,CAA2Bra,OAA3B,EAAoCoC,MAApC;EACD,GAhBD;EAiBD,CA3BD;EA6BA;EACA;EACA;EACA;EACA;EACA;;EAEA4C,kBAAkB,CAAC0G,MAAD,EAAOuN,QAAP,CAAlB;;ECvZA;EACA;EACA;EACA;EACA;EACA;EAmBA;EACA;EACA;EACA;EACA;;EAEA,MAAMvN,MAAI,GAAG,UAAb;EACA,MAAMH,UAAQ,GAAG,aAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAMwP,YAAU,GAAG,QAAnB;EACA,MAAMC,SAAS,GAAG,OAAlB;EACA,MAAMC,OAAO,GAAG,KAAhB;EACA,MAAMC,YAAY,GAAG,SAArB;EACA,MAAMC,cAAc,GAAG,WAAvB;EACA,MAAMC,kBAAkB,GAAG,CAA3B;;EAEA,MAAMC,cAAc,GAAG,IAAI7Y,MAAJ,CAAY,GAAE0Y,YAAa,IAAGC,cAAe,IAAGJ,YAAW,EAA3D,CAAvB;EAEA,MAAM3C,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAM4M,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAMgQ,WAAW,GAAI,QAAOhQ,WAAU,EAAtC;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EACA,MAAMgQ,sBAAsB,GAAI,UAASjQ,WAAU,GAAEC,cAAa,EAAlE;EACA,MAAMiQ,oBAAoB,GAAI,QAAOlQ,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMkQ,mBAAmB,GAAG,UAA5B;EACA,MAAM3P,iBAAe,GAAG,MAAxB;EACA,MAAM4P,iBAAiB,GAAG,QAA1B;EACA,MAAMC,kBAAkB,GAAG,SAA3B;EACA,MAAMC,oBAAoB,GAAG,WAA7B;EACA,MAAMC,iBAAiB,GAAG,QAA1B;EAEA,MAAM/O,sBAAoB,GAAG,6BAA7B;EACA,MAAMgP,aAAa,GAAG,gBAAtB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMC,sBAAsB,GAAG,6DAA/B;EAEA,MAAMC,aAAa,GAAGxX,KAAK,KAAK,SAAL,GAAiB,WAA5C;EACA,MAAMyX,gBAAgB,GAAGzX,KAAK,KAAK,WAAL,GAAmB,SAAjD;EACA,MAAM0X,gBAAgB,GAAG1X,KAAK,KAAK,YAAL,GAAoB,cAAlD;EACA,MAAM2X,mBAAmB,GAAG3X,KAAK,KAAK,cAAL,GAAsB,YAAvD;EACA,MAAM4X,eAAe,GAAG5X,KAAK,KAAK,YAAL,GAAoB,aAAjD;EACA,MAAM6X,cAAc,GAAG7X,KAAK,KAAK,aAAL,GAAqB,YAAjD;EAEA,MAAMyL,SAAO,GAAG;EACdhC,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CADM;EAEdqO,EAAAA,QAAQ,EAAE,iBAFI;EAGdC,EAAAA,SAAS,EAAE,QAHG;EAIdvZ,EAAAA,OAAO,EAAE,SAJK;EAKdwZ,EAAAA,YAAY,EAAE;EALA,CAAhB;EAQA,MAAMhM,aAAW,GAAG;EAClBvC,EAAAA,MAAM,EAAE,yBADU;EAElBqO,EAAAA,QAAQ,EAAE,kBAFQ;EAGlBC,EAAAA,SAAS,EAAE,yBAHO;EAIlBvZ,EAAAA,OAAO,EAAE,QAJS;EAKlBwZ,EAAAA,YAAY,EAAE;EALI,CAApB;EAQA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,QAAN,SAAuB5R,aAAvB,CAAqC;EACnCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAKgd,OAAL,GAAe,IAAf;EACA,SAAKtJ,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAK6a,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKlJ,kBAAL;EACD,GAVkC;;;EAcjB,aAAP3D,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEqB,aAAXO,WAAW,GAAG;EACvB,WAAOA,aAAP;EACD;;EAEkB,aAARvF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAxBkC;;;EA4BnC8B,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKhC,QAAL,CAAcxH,QAAd,IAA0B,KAAKwH,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkY,mBAAjC,CAA9B,EAAqF;EACnF;EACD;;EAED,UAAMuB,QAAQ,GAAG,KAAKhS,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,iBAAjC,CAAjB;;EAEA4Q,IAAAA,QAAQ,CAACO,UAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,SAAKvD,IAAL;EACD;;EAEDA,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKzO,QAAL,CAAcxH,QAAd,IAA0B,KAAKwH,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkY,mBAAjC,CAA1B,IAAmF,KAAKmB,KAAL,CAAWtZ,SAAX,CAAqBC,QAArB,CAA8BuI,iBAA9B,CAAvF,EAAuI;EACrI;EACD;;EAED,UAAMmM,MAAM,GAAGyE,QAAQ,CAACQ,oBAAT,CAA8B,KAAKlS,QAAnC,CAAf;EACA,UAAMuL,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKvL;EADA,KAAtB;EAIA,UAAMmS,SAAS,GAAG9V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD3B,aAAhD,CAAlB;;EAEA,QAAI4G,SAAS,CAACjT,gBAAd,EAAgC;EAC9B;EACD,KAdI;;;EAiBL,QAAI,KAAK4S,SAAT,EAAoB;EAClBvP,MAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAKoP,KAAlC,EAAyC,QAAzC,EAAmD,MAAnD;EACD,KAFD,MAEO;EACL,UAAI,OAAOQ,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAI1a,SAAJ,CAAc,+DAAd,CAAN;EACD;;EAED,UAAI2a,gBAAgB,GAAG,KAAKrS,QAA5B;;EAEA,UAAI,KAAKqI,OAAL,CAAamJ,SAAb,KAA2B,QAA/B,EAAyC;EACvCa,QAAAA,gBAAgB,GAAGpF,MAAnB;EACD,OAFD,MAEO,IAAI/W,SAAS,CAAC,KAAKmS,OAAL,CAAamJ,SAAd,CAAb,EAAuC;EAC5Ca,QAAAA,gBAAgB,GAAG,KAAKhK,OAAL,CAAamJ,SAAhC,CAD4C;;EAI5C,YAAI,OAAO,KAAKnJ,OAAL,CAAamJ,SAAb,CAAuB/B,MAA9B,KAAyC,WAA7C,EAA0D;EACxD4C,UAAAA,gBAAgB,GAAG,KAAKhK,OAAL,CAAamJ,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OAPM,MAOA,IAAI,OAAO,KAAKnJ,OAAL,CAAamJ,SAApB,KAAkC,QAAtC,EAAgD;EACrDa,QAAAA,gBAAgB,GAAG,KAAKhK,OAAL,CAAamJ,SAAhC;EACD;;EAED,YAAMC,YAAY,GAAG,KAAKa,gBAAL,EAArB;;EACA,YAAMC,eAAe,GAAGd,YAAY,CAACe,SAAb,CAAuB1O,IAAvB,CAA4B2O,QAAQ,IAAIA,QAAQ,CAAC7Y,IAAT,KAAkB,aAAlB,IAAmC6Y,QAAQ,CAACC,OAAT,KAAqB,KAAhG,CAAxB;EAEA,WAAKf,OAAL,GAAeS,iBAAM,CAACO,YAAP,CAAoBN,gBAApB,EAAsC,KAAKT,KAA3C,EAAkDH,YAAlD,CAAf;;EAEA,UAAIc,eAAJ,EAAqB;EACnBhQ,QAAAA,WAAW,CAACC,gBAAZ,CAA6B,KAAKoP,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD;EACD;EACF,KA/CI;EAkDL;EACA;EACA;;;EACA,QAAI,kBAAkBpd,QAAQ,CAACmE,eAA3B,IACF,CAACsU,MAAM,CAAC3L,OAAP,CAAeyP,mBAAf,CADH,EACwC;EACtC,SAAGhN,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EACGhN,OADH,CACW8W,IAAI,IAAI5R,YAAY,CAACiC,EAAb,CAAgB2P,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyCjV,IAAI,EAA7C,CADnB;EAED;;EAED,SAAKgH,QAAL,CAAc4S,KAAd;;EACA,SAAK5S,QAAL,CAAciC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEA,SAAK2P,KAAL,CAAWtZ,SAAX,CAAqB0J,MAArB,CAA4BlB,iBAA5B;;EACA,SAAKd,QAAL,CAAc1H,SAAd,CAAwB0J,MAAxB,CAA+BlB,iBAA/B;;EACAzE,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD5B,aAAjD;EACD;;EAEDiD,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKxO,QAAL,CAAcxH,QAAd,IAA0B,KAAKwH,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCkY,mBAAjC,CAA1B,IAAmF,CAAC,KAAKmB,KAAL,CAAWtZ,SAAX,CAAqBC,QAArB,CAA8BuI,iBAA9B,CAAxF,EAAwI;EACtI;EACD;;EAED,UAAMyK,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKvL;EADA,KAAtB;EAIA,UAAM6S,SAAS,GAAGxW,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,EAAgD7B,aAAhD,CAAlB;;EAEA,QAAIsH,SAAS,CAAC3T,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAI,KAAKyS,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAamB,OAAb;EACD;;EAED,SAAKlB,KAAL,CAAWtZ,SAAX,CAAqB0J,MAArB,CAA4BlB,iBAA5B;;EACA,SAAKd,QAAL,CAAc1H,SAAd,CAAwB0J,MAAxB,CAA+BlB,iBAA/B;;EACAyB,IAAAA,WAAW,CAACE,mBAAZ,CAAgC,KAAKmP,KAArC,EAA4C,QAA5C;EACAvV,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC,EAAkD9B,aAAlD;EACD;;EAEDpL,EAAAA,OAAO,GAAG;EACR9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCM,WAAhC;EACA,SAAKsR,KAAL,GAAa,IAAb;;EAEA,QAAI,KAAKD,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAamB,OAAb;;EACA,WAAKnB,OAAL,GAAe,IAAf;EACD;;EAED,UAAMxR,OAAN;EACD;;EAED4S,EAAAA,MAAM,GAAG;EACP,SAAKjB,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKJ,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAaoB,MAAb;EACD;EACF,GAzJkC;;;EA6JnClK,EAAAA,kBAAkB,GAAG;EACnBxM,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BsQ,WAA/B,EAA4CpU,KAAK,IAAI;EACnDA,MAAAA,KAAK,CAAC0D,cAAN;EACA,WAAKoC,MAAL;EACD,KAHD;EAID;;EAEDsG,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKgJ,WAAL,CAAiBmF,OADb;EAEP,SAAG3C,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAFI;EAGP,SAAGjJ;EAHI,KAAT;EAMAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe,KAAKgJ,WAAL,CAAiB0F,WAAhC,CAAf;;EAEA,QAAI,OAAO1O,MAAM,CAACya,SAAd,KAA4B,QAA5B,IAAwC,CAACtb,SAAS,CAACa,MAAM,CAACya,SAAR,CAAlD,IACF,OAAOza,MAAM,CAACya,SAAP,CAAiBpO,qBAAxB,KAAkD,UADpD,EAEE;EACA;EACA,YAAM,IAAI1L,SAAJ,CAAe,GAAE2I,MAAI,CAAC1I,WAAL,EAAmB,gGAApC,CAAN;EACD;;EAED,WAAOZ,MAAP;EACD;;EAED8a,EAAAA,eAAe,GAAG;EAChB,WAAOhO,cAAc,CAACe,IAAf,CAAoB,KAAK5E,QAAzB,EAAmC8Q,aAAnC,EAAkD,CAAlD,CAAP;EACD;;EAEDkC,EAAAA,aAAa,GAAG;EACd,UAAMC,cAAc,GAAG,KAAKjT,QAAL,CAAclI,UAArC;;EAEA,QAAImb,cAAc,CAAC3a,SAAf,CAAyBC,QAAzB,CAAkCoY,kBAAlC,CAAJ,EAA2D;EACzD,aAAOU,eAAP;EACD;;EAED,QAAI4B,cAAc,CAAC3a,SAAf,CAAyBC,QAAzB,CAAkCqY,oBAAlC,CAAJ,EAA6D;EAC3D,aAAOU,cAAP;EACD,KATa;;;EAYd,UAAM4B,KAAK,GAAGxd,gBAAgB,CAAC,KAAKkc,KAAN,CAAhB,CAA6BuB,gBAA7B,CAA8C,eAA9C,EAA+Dje,IAA/D,OAA0E,KAAxF;;EAEA,QAAI+d,cAAc,CAAC3a,SAAf,CAAyBC,QAAzB,CAAkCmY,iBAAlC,CAAJ,EAA0D;EACxD,aAAOwC,KAAK,GAAGhC,gBAAH,GAAsBD,aAAlC;EACD;;EAED,WAAOiC,KAAK,GAAG9B,mBAAH,GAAyBD,gBAArC;EACD;;EAEDY,EAAAA,aAAa,GAAG;EACd,WAAO,KAAK/R,QAAL,CAAcsB,OAAd,CAAuB,IAAGuP,iBAAkB,EAA5C,MAAmD,IAA1D;EACD;;EAEDuC,EAAAA,UAAU,GAAG;EACX,UAAM;EAAElQ,MAAAA;EAAF,QAAa,KAAKmF,OAAxB;;EAEA,QAAI,OAAOnF,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAACjO,KAAP,CAAa,GAAb,EAAkBoe,GAAlB,CAAsBjR,GAAG,IAAIxM,MAAM,CAACkW,QAAP,CAAgB1J,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAOc,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAOoQ,UAAU,IAAIpQ,MAAM,CAACoQ,UAAD,EAAa,KAAKtT,QAAlB,CAA3B;EACD;;EAED,WAAOkD,MAAP;EACD;;EAEDoP,EAAAA,gBAAgB,GAAG;EACjB,UAAMiB,qBAAqB,GAAG;EAC5BC,MAAAA,SAAS,EAAE,KAAKR,aAAL,EADiB;EAE5BR,MAAAA,SAAS,EAAE,CAAC;EACV5Y,QAAAA,IAAI,EAAE,iBADI;EAEV6Z,QAAAA,OAAO,EAAE;EACPlC,UAAAA,QAAQ,EAAE,KAAKlJ,OAAL,CAAakJ;EADhB;EAFC,OAAD,EAMX;EACE3X,QAAAA,IAAI,EAAE,QADR;EAEE6Z,QAAAA,OAAO,EAAE;EACPvQ,UAAAA,MAAM,EAAE,KAAKkQ,UAAL;EADD;EAFX,OANW;EAFiB,KAA9B,CADiB;;EAkBjB,QAAI,KAAK/K,OAAL,CAAapQ,OAAb,KAAyB,QAA7B,EAAuC;EACrCsb,MAAAA,qBAAqB,CAACf,SAAtB,GAAkC,CAAC;EACjC5Y,QAAAA,IAAI,EAAE,aAD2B;EAEjC8Y,QAAAA,OAAO,EAAE;EAFwB,OAAD,CAAlC;EAID;;EAED,WAAO,EACL,GAAGa,qBADE;EAEL,UAAI,OAAO,KAAKlL,OAAL,CAAaoJ,YAApB,KAAqC,UAArC,GAAkD,KAAKpJ,OAAL,CAAaoJ,YAAb,CAA0B8B,qBAA1B,CAAlD,GAAqG,KAAKlL,OAAL,CAAaoJ,YAAtH;EAFK,KAAP;EAID,GA/PkC;;;EAmQX,SAAjBiC,iBAAiB,CAAC/e,OAAD,EAAUoC,MAAV,EAAkB;EACxC,QAAI2K,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAShG,OAAT,EAAkBuL,UAAlB,CAAX;;EACA,UAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,QAAI,CAAC2K,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIgQ,QAAJ,CAAa/c,OAAb,EAAsB0T,OAAtB,CAAP;EACD;;EAED,QAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,MAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF;;EAEqB,SAAfkD,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3BiQ,MAAAA,QAAQ,CAACgC,iBAAT,CAA2B,IAA3B,EAAiC3c,MAAjC;EACD,KAFM,CAAP;EAGD;;EAEgB,SAAVkb,UAAU,CAAC/V,KAAD,EAAQ;EACvB,QAAIA,KAAJ,EAAW;EACT,UAAIA,KAAK,CAACgG,MAAN,KAAiBkO,kBAAjB,IAAwClU,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC3B,GAAN,KAAc0V,OAApF,EAA8F;EAC5F;EACD;;EAED,UAAI,8BAA8BxY,IAA9B,CAAmCyE,KAAK,CAACU,MAAN,CAAagO,OAAhD,CAAJ,EAA8D;EAC5D;EACD;EACF;;EAED,UAAM+I,OAAO,GAAG9P,cAAc,CAACC,IAAf,CAAoBhC,sBAApB,CAAhB;;EAEA,SAAK,IAAIjF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGwW,OAAO,CAAC7W,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;EAClD,YAAM+W,OAAO,GAAG3T,IAAI,CAACtF,GAAL,CAASgZ,OAAO,CAAC9W,CAAD,CAAhB,EAAqBqD,UAArB,CAAhB;EACA,YAAMqL,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEoI,OAAO,CAAC9W,CAAD;EADF,OAAtB;;EAIA,UAAIX,KAAK,IAAIA,KAAK,CAACK,IAAN,KAAe,OAA5B,EAAqC;EACnCgP,QAAAA,aAAa,CAACsI,UAAd,GAA2B3X,KAA3B;EACD;;EAED,UAAI,CAAC0X,OAAL,EAAc;EACZ;EACD;;EAED,YAAME,YAAY,GAAGF,OAAO,CAAChC,KAA7B;;EACA,UAAI,CAAC+B,OAAO,CAAC9W,CAAD,CAAP,CAAWvE,SAAX,CAAqBC,QAArB,CAA8BuI,iBAA9B,CAAL,EAAqD;EACnD;EACD;;EAED,UAAI5E,KAAJ,EAAW;EACT;EACA,YAAI,CAAC0X,OAAO,CAAC5T,QAAT,EAAmB+T,IAAnB,CAAwBpf,OAAO,IAAIuH,KAAK,CAAC8X,YAAN,GAAqBjf,QAArB,CAA8BJ,OAA9B,CAAnC,CAAJ,EAAgF;EAC9E;EACD,SAJQ;;;EAOT,YAAIuH,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC3B,GAAN,KAAc0V,OAAxC,IAAmD6D,YAAY,CAACvb,QAAb,CAAsB2D,KAAK,CAACU,MAA5B,CAAvD,EAA4F;EAC1F;EACD;EACF;;EAED,YAAMiW,SAAS,GAAGxW,YAAY,CAACwC,OAAb,CAAqB8U,OAAO,CAAC9W,CAAD,CAA5B,EAAiCuQ,YAAjC,EAA6C7B,aAA7C,CAAlB;;EACA,UAAIsH,SAAS,CAAC3T,gBAAd,EAAgC;EAC9B;EACD,OAlCiD;EAqClD;;;EACA,UAAI,kBAAkB1K,QAAQ,CAACmE,eAA/B,EAAgD;EAC9C,WAAGoL,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EACGhN,OADH,CACW8W,IAAI,IAAI5R,YAAY,CAACC,GAAb,CAAiB2R,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0CjV,IAAI,EAA9C,CADnB;EAED;;EAED2a,MAAAA,OAAO,CAAC9W,CAAD,CAAP,CAAWoF,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;EAEA,UAAI2R,OAAO,CAACjC,OAAZ,EAAqB;EACnBiC,QAAAA,OAAO,CAACjC,OAAR,CAAgBmB,OAAhB;EACD;;EAEDgB,MAAAA,YAAY,CAACxb,SAAb,CAAuB2C,MAAvB,CAA8B6F,iBAA9B;EACA6S,MAAAA,OAAO,CAAC9W,CAAD,CAAP,CAAWvE,SAAX,CAAqB2C,MAArB,CAA4B6F,iBAA5B;EACAyB,MAAAA,WAAW,CAACE,mBAAZ,CAAgCqR,YAAhC,EAA8C,QAA9C;EACAzX,MAAAA,YAAY,CAACwC,OAAb,CAAqB8U,OAAO,CAAC9W,CAAD,CAA5B,EAAiCwQ,cAAjC,EAA+C9B,aAA/C;EACD;EACF;;EAE0B,SAApB2G,oBAAoB,CAACvd,OAAD,EAAU;EACnC,WAAOU,sBAAsB,CAACV,OAAD,CAAtB,IAAmCA,OAAO,CAACmD,UAAlD;EACD;;EAE2B,SAArBmc,qBAAqB,CAAC/X,KAAD,EAAQ;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkBzE,IAAlB,CAAuByE,KAAK,CAACU,MAAN,CAAagO,OAApC,IACF1O,KAAK,CAAC3B,GAAN,KAAcyV,SAAd,IAA4B9T,KAAK,CAAC3B,GAAN,KAAcwV,YAAd,KAC1B7T,KAAK,CAAC3B,GAAN,KAAc4V,cAAd,IAAgCjU,KAAK,CAAC3B,GAAN,KAAc2V,YAA/C,IACChU,KAAK,CAACU,MAAN,CAAa0E,OAAb,CAAqBwP,aAArB,CAF0B,CAD1B,GAIF,CAACT,cAAc,CAAC5Y,IAAf,CAAoByE,KAAK,CAAC3B,GAA1B,CAJH,EAImC;EACjC;EACD;;EAED2B,IAAAA,KAAK,CAAC0D,cAAN;EACA1D,IAAAA,KAAK,CAACgY,eAAN;;EAEA,QAAI,KAAK1b,QAAL,IAAiB,KAAKF,SAAL,CAAeC,QAAf,CAAwBkY,mBAAxB,CAArB,EAAmE;EACjE;EACD;;EAED,UAAMxD,MAAM,GAAGyE,QAAQ,CAACQ,oBAAT,CAA8B,IAA9B,CAAf;EACA,UAAMF,QAAQ,GAAG,KAAK1Z,SAAL,CAAeC,QAAf,CAAwBuI,iBAAxB,CAAjB;;EAEA,QAAI5E,KAAK,CAAC3B,GAAN,KAAcwV,YAAlB,EAA8B;EAC5B,YAAM7N,MAAM,GAAG,KAAKmC,OAAL,CAAavC,sBAAb,IAAqC,IAArC,GAA4C+B,cAAc,CAACY,IAAf,CAAoB,IAApB,EAA0B3C,sBAA1B,EAAgD,CAAhD,CAA3D;EACAI,MAAAA,MAAM,CAAC0Q,KAAP;EACAlB,MAAAA,QAAQ,CAACO,UAAT;EACA;EACD;;EAED,QAAI,CAACD,QAAD,KAAc9V,KAAK,CAAC3B,GAAN,KAAc2V,YAAd,IAA8BhU,KAAK,CAAC3B,GAAN,KAAc4V,cAA1D,CAAJ,EAA+E;EAC7E,YAAMjO,MAAM,GAAG,KAAKmC,OAAL,CAAavC,sBAAb,IAAqC,IAArC,GAA4C+B,cAAc,CAACY,IAAf,CAAoB,IAApB,EAA0B3C,sBAA1B,EAAgD,CAAhD,CAA3D;EACAI,MAAAA,MAAM,CAACiS,KAAP;EACA;EACD;;EAED,QAAI,CAACnC,QAAD,IAAa9V,KAAK,CAAC3B,GAAN,KAAcyV,SAA/B,EAA0C;EACxC0B,MAAAA,QAAQ,CAACO,UAAT;EACA;EACD;;EAED,UAAMmC,KAAK,GAAGvQ,cAAc,CAACC,IAAf,CAAoBkN,sBAApB,EAA4C/D,MAA5C,EAAoDnK,MAApD,CAA2DlL,SAA3D,CAAd;;EAEA,QAAI,CAACwc,KAAK,CAACtX,MAAX,EAAmB;EACjB;EACD;;EAED,QAAI0M,KAAK,GAAG4K,KAAK,CAACvJ,OAAN,CAAc3O,KAAK,CAACU,MAApB,CAAZ,CAlDkC;;EAqDlC,QAAIV,KAAK,CAAC3B,GAAN,KAAc2V,YAAd,IAA8B1G,KAAK,GAAG,CAA1C,EAA6C;EAC3CA,MAAAA,KAAK;EACN,KAvDiC;;;EA0DlC,QAAItN,KAAK,CAAC3B,GAAN,KAAc4V,cAAd,IAAgC3G,KAAK,GAAG4K,KAAK,CAACtX,MAAN,GAAe,CAA3D,EAA8D;EAC5D0M,MAAAA,KAAK;EACN,KA5DiC;;;EA+DlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;EAEA4K,IAAAA,KAAK,CAAC5K,KAAD,CAAL,CAAaoJ,KAAb;EACD;;EArakC;EAwarC;EACA;EACA;EACA;EACA;;;EAEAvW,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B+b,sBAA1B,EAAkDzO,sBAAlD,EAAwE4P,QAAQ,CAACuC,qBAAjF;EACA5X,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B+b,sBAA1B,EAAkDO,aAAlD,EAAiEY,QAAQ,CAACuC,qBAA1E;EACA5X,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgD+Q,QAAQ,CAACO,UAAzD;EACA5V,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0Bgc,oBAA1B,EAAgDkB,QAAQ,CAACO,UAAzD;EACA5V,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC0D,cAAN;EACA8R,EAAAA,QAAQ,CAACgC,iBAAT,CAA2B,IAA3B;EACD,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEA/Z,kBAAkB,CAAC0G,MAAD,EAAOqR,QAAP,CAAlB;;EC5hBA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;EACA;EACA;;EAEA,MAAMrR,MAAI,GAAG,OAAb;EACA,MAAMH,UAAQ,GAAG,UAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EACA,MAAMwP,YAAU,GAAG,QAAnB;EAEA,MAAM7K,SAAO,GAAG;EACdmP,EAAAA,QAAQ,EAAE,IADI;EAEdjP,EAAAA,QAAQ,EAAE,IAFI;EAGdwN,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,MAAMnN,aAAW,GAAG;EAClB4O,EAAAA,QAAQ,EAAE,kBADQ;EAElBjP,EAAAA,QAAQ,EAAE,SAFQ;EAGlBwN,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMxF,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAMgU,oBAAoB,GAAI,gBAAehU,WAAU,EAAvD;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAM4M,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAMiU,eAAa,GAAI,UAASjU,WAAU,EAA1C;EACA,MAAMkU,YAAY,GAAI,SAAQlU,WAAU,EAAxC;EACA,MAAMmU,qBAAmB,GAAI,gBAAenU,WAAU,EAAtD;EACA,MAAMoU,qBAAqB,GAAI,kBAAiBpU,WAAU,EAA1D;EACA,MAAMqU,qBAAqB,GAAI,kBAAiBrU,WAAU,EAA1D;EACA,MAAMsU,uBAAuB,GAAI,oBAAmBtU,WAAU,EAA9D;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EAEA,MAAMsU,6BAA6B,GAAG,yBAAtC;EACA,MAAMC,mBAAmB,GAAG,gBAA5B;EACA,MAAMC,eAAe,GAAG,YAAxB;EACA,MAAMlU,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EACA,MAAMkU,iBAAiB,GAAG,cAA1B;EAEA,MAAMC,eAAe,GAAG,eAAxB;EACA,MAAMC,mBAAmB,GAAG,aAA5B;EACA,MAAMpT,sBAAoB,GAAG,0BAA7B;EACA,MAAMqT,uBAAqB,GAAG,2BAA9B;EACA,MAAMC,wBAAsB,GAAG,mDAA/B;EACA,MAAMC,yBAAuB,GAAG,aAAhC;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,KAAN,SAAoBxV,aAApB,CAAkC;EAChCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAK0T,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKwe,OAAL,GAAe1R,cAAc,CAACK,OAAf,CAAuB+Q,eAAvB,EAAwC,KAAKjV,QAA7C,CAAf;EACA,SAAKwV,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,kBAAL,GAA0B,KAA1B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAK9H,gBAAL,GAAwB,KAAxB;EACA,SAAK+H,eAAL,GAAuB,CAAvB;EACD,GAZ+B;;;EAgBd,aAAP1Q,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAtB+B;;;EA0BhC8B,EAAAA,MAAM,CAACuJ,aAAD,EAAgB;EACpB,WAAO,KAAKkK,QAAL,GAAgB,KAAKjH,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUlD,aAAV,CAArC;EACD;;EAEDkD,EAAAA,IAAI,CAAClD,aAAD,EAAgB;EAClB,QAAI,KAAKkK,QAAL,IAAiB,KAAK5H,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAI,KAAKgI,WAAL,EAAJ,EAAwB;EACtB,WAAKhI,gBAAL,GAAwB,IAAxB;EACD;;EAED,UAAMsE,SAAS,GAAG9V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD;EAChE3B,MAAAA;EADgE,KAAhD,CAAlB;;EAIA,QAAI,KAAKkK,QAAL,IAAiBtD,SAAS,CAACjT,gBAA/B,EAAiD;EAC/C;EACD;;EAED,SAAKuW,QAAL,GAAgB,IAAhB;;EAEA,SAAKK,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEA7Z,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByU,qBAA/B,EAAoDU,uBAApD,EAA2EjZ,KAAK,IAAI,KAAKsS,IAAL,CAAUtS,KAAV,CAApF;EAEAG,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKiX,OAArB,EAA8BX,uBAA9B,EAAuD,MAAM;EAC3DvY,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC2U,qBAAhC,EAAuDzY,KAAK,IAAI;EAC9D,YAAIA,KAAK,CAACU,MAAN,KAAiB,KAAKoD,QAA1B,EAAoC;EAClC,eAAK2V,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKQ,aAAL,CAAmB,MAAM,KAAKC,YAAL,CAAkB7K,aAAlB,CAAzB;EACD;;EAEDiD,EAAAA,IAAI,CAACtS,KAAD,EAAQ;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAAC0D,cAAN;EACD;;EAED,QAAI,CAAC,KAAK6V,QAAN,IAAkB,KAAK5H,gBAA3B,EAA6C;EAC3C;EACD;;EAED,UAAMgF,SAAS,GAAGxW,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,CAAlB;;EAEA,QAAIyF,SAAS,CAAC3T,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKuW,QAAL,GAAgB,KAAhB;;EACA,UAAMY,UAAU,GAAG,KAAKR,WAAL,EAAnB;;EAEA,QAAIQ,UAAJ,EAAgB;EACd,WAAKxI,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKoI,eAAL;;EACA,SAAKC,eAAL;;EAEA7Z,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B+f,eAA3B;;EAEA,SAAKvU,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,iBAA/B;;EAEAzE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCyU,qBAAhC;EACApY,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKiZ,OAAtB,EAA+BX,uBAA/B;;EAEA,QAAIyB,UAAJ,EAAgB;EACd,YAAM9gB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD9D,KAAK,IAAI,KAAKoa,UAAL,CAAgBpa,KAAhB,CAA1D;EACA9F,MAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL,WAAK+gB,UAAL;EACD;EACF;;EAEDnW,EAAAA,OAAO,GAAG;EACR,KAAC1K,MAAD,EAAS,KAAKuK,QAAd,EAAwB,KAAKuV,OAA7B,EACGpe,OADH,CACWof,WAAW,IAAIla,YAAY,CAACC,GAAb,CAAiBia,WAAjB,EAA8BjW,WAA9B,CAD1B;EAGA,UAAMH,OAAN;EAEA;EACJ;EACA;EACA;EACA;;EACI9D,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B+f,eAA3B;EAEA,SAAKlM,OAAL,GAAe,IAAf;EACA,SAAKkN,OAAL,GAAe,IAAf;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,kBAAL,GAA0B,IAA1B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAK9H,gBAAL,GAAwB,IAAxB;EACA,SAAK+H,eAAL,GAAuB,IAAvB;EACD;;EAEDY,EAAAA,YAAY,GAAG;EACb,SAAKR,aAAL;EACD,GAzI+B;;;EA6IhC1N,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAGnO;EAFI,KAAT;EAIAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAEDqf,EAAAA,YAAY,CAAC7K,aAAD,EAAgB;EAC1B,UAAM8K,UAAU,GAAG,KAAKR,WAAL,EAAnB;;EACA,UAAMY,SAAS,GAAG5S,cAAc,CAACK,OAAf,CAAuBgR,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;EAEA,QAAI,CAAC,KAAKvV,QAAL,CAAclI,UAAf,IAA6B,KAAKkI,QAAL,CAAclI,UAAd,CAAyB3B,QAAzB,KAAsCiC,IAAI,CAACC,YAA5E,EAA0F;EACxF;EACA7D,MAAAA,QAAQ,CAAC6E,IAAT,CAAcqd,WAAd,CAA0B,KAAK1W,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAcnI,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;EACA,SAAK+H,QAAL,CAAc0C,eAAd,CAA8B,aAA9B;;EACA,SAAK1C,QAAL,CAAciC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAKjC,QAAL,CAAciC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAKjC,QAAL,CAAcsD,SAAd,GAA0B,CAA1B;;EAEA,QAAImT,SAAJ,EAAe;EACbA,MAAAA,SAAS,CAACnT,SAAV,GAAsB,CAAtB;EACD;;EAED,QAAI+S,UAAJ,EAAgB;EACdpd,MAAAA,MAAM,CAAC,KAAK+G,QAAN,CAAN;EACD;;EAED,SAAKA,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B7J,iBAA5B;;EAEA,QAAI,KAAKuH,OAAL,CAAauK,KAAjB,EAAwB;EACtB,WAAK+D,aAAL;EACD;;EAED,UAAMC,kBAAkB,GAAG,MAAM;EAC/B,UAAI,KAAKvO,OAAL,CAAauK,KAAjB,EAAwB;EACtB,aAAK5S,QAAL,CAAc4S,KAAd;EACD;;EAED,WAAK/E,gBAAL,GAAwB,KAAxB;EACAxR,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD;EAC/C5B,QAAAA;EAD+C,OAAjD;EAGD,KATD;;EAWA,QAAI8K,UAAJ,EAAgB;EACd,YAAM9gB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAKigB,OAAN,CAA3D;EAEAlZ,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKgX,OAAtB,EAA+B,eAA/B,EAAgDqB,kBAAhD;EACAxgB,MAAAA,oBAAoB,CAAC,KAAKmf,OAAN,EAAehgB,kBAAf,CAApB;EACD,KALD,MAKO;EACLqhB,MAAAA,kBAAkB;EACnB;EACF;;EAEDD,EAAAA,aAAa,GAAG;EACdta,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B+f,eAA3B,EADc;;EAEdlY,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B+f,eAA1B,EAAyCrY,KAAK,IAAI;EAChD,UAAI1H,QAAQ,KAAK0H,KAAK,CAACU,MAAnB,IACA,KAAKoD,QAAL,KAAkB9D,KAAK,CAACU,MADxB,IAEA,CAAC,KAAKoD,QAAL,CAAczH,QAAd,CAAuB2D,KAAK,CAACU,MAA7B,CAFL,EAE2C;EACzC,aAAKoD,QAAL,CAAc4S,KAAd;EACD;EACF,KAND;EAOD;;EAEDqD,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKR,QAAT,EAAmB;EACjBpZ,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B0U,qBAA/B,EAAsDxY,KAAK,IAAI;EAC7D,YAAI,KAAKmM,OAAL,CAAajD,QAAb,IAAyBlJ,KAAK,CAAC3B,GAAN,KAAcwV,YAA3C,EAAuD;EACrD7T,UAAAA,KAAK,CAAC0D,cAAN;EACA,eAAK4O,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,KAAKnG,OAAL,CAAajD,QAAd,IAA0BlJ,KAAK,CAAC3B,GAAN,KAAcwV,YAA5C,EAAwD;EAC7D,eAAK8G,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO;EACLxa,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC0U,qBAAhC;EACD;EACF;;EAEDwB,EAAAA,eAAe,GAAG;EAChB,QAAI,KAAKT,QAAT,EAAmB;EACjBpZ,MAAAA,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwB+e,YAAxB,EAAsC,MAAM,KAAKwB,aAAL,EAA5C;EACD,KAFD,MAEO;EACL3Z,MAAAA,YAAY,CAACC,GAAb,CAAiB7G,MAAjB,EAAyB+e,YAAzB;EACD;EACF;;EAED8B,EAAAA,UAAU,GAAG;EACX,SAAKtW,QAAL,CAAcnI,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;EACA,SAAK+H,QAAL,CAAciC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAKjC,QAAL,CAAc0C,eAAd,CAA8B,YAA9B;;EACA,SAAK1C,QAAL,CAAc0C,eAAd,CAA8B,MAA9B;;EACA,SAAKmL,gBAAL,GAAwB,KAAxB;;EACA,SAAKsI,aAAL,CAAmB,MAAM;EACvB3hB,MAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwB2C,MAAxB,CAA+B8Z,eAA/B;;EACA,WAAK+B,iBAAL;;EACA,WAAKC,eAAL;;EACA1a,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC;EACD,KALD;EAMD;;EAED2J,EAAAA,eAAe,GAAG;EAChB,SAAKxB,SAAL,CAAe1d,UAAf,CAA0B0J,WAA1B,CAAsC,KAAKgU,SAA3C;;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;EAEDW,EAAAA,aAAa,CAAC5c,QAAD,EAAW;EACtB,UAAM8c,UAAU,GAAG,KAAKR,WAAL,EAAnB;;EACA,QAAI,KAAKJ,QAAL,IAAiB,KAAKpN,OAAL,CAAagM,QAAlC,EAA4C;EAC1C,WAAKmB,SAAL,GAAiBhhB,QAAQ,CAACyiB,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAKzB,SAAL,CAAe0B,SAAf,GAA2BpC,mBAA3B;;EAEA,UAAIuB,UAAJ,EAAgB;EACd,aAAKb,SAAL,CAAeld,SAAf,CAAyBqS,GAAzB,CAA6B9J,iBAA7B;EACD;;EAEDrM,MAAAA,QAAQ,CAAC6E,IAAT,CAAcqd,WAAd,CAA0B,KAAKlB,SAA/B;EAEAnZ,MAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByU,qBAA/B,EAAoDvY,KAAK,IAAI;EAC3D,YAAI,KAAKyZ,oBAAT,EAA+B;EAC7B,eAAKA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,YAAIzZ,KAAK,CAACU,MAAN,KAAiBV,KAAK,CAACib,aAA3B,EAA0C;EACxC;EACD;;EAED,YAAI,KAAK9O,OAAL,CAAagM,QAAb,KAA0B,QAA9B,EAAwC;EACtC,eAAKwC,0BAAL;EACD,SAFD,MAEO;EACL,eAAKrI,IAAL;EACD;EACF,OAfD;;EAiBA,UAAI6H,UAAJ,EAAgB;EACdpd,QAAAA,MAAM,CAAC,KAAKuc,SAAN,CAAN;EACD;;EAED,WAAKA,SAAL,CAAeld,SAAf,CAAyBqS,GAAzB,CAA6B7J,iBAA7B;;EAEA,UAAI,CAACuV,UAAL,EAAiB;EACf9c,QAAAA,QAAQ;EACR;EACD;;EAED,YAAM6d,0BAA0B,GAAG9hB,gCAAgC,CAAC,KAAKkgB,SAAN,CAAnE;EAEAnZ,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKiX,SAAtB,EAAiC,eAAjC,EAAkDjc,QAAlD;EACAnD,MAAAA,oBAAoB,CAAC,KAAKof,SAAN,EAAiB4B,0BAAjB,CAApB;EACD,KA1CD,MA0CO,IAAI,CAAC,KAAK3B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C,WAAKA,SAAL,CAAeld,SAAf,CAAyB2C,MAAzB,CAAgC6F,iBAAhC;;EAEA,YAAMuW,cAAc,GAAG,MAAM;EAC3B,aAAKL,eAAL;;EACAzd,QAAAA,QAAQ;EACT,OAHD;;EAKA,UAAI8c,UAAJ,EAAgB;EACd,cAAMe,0BAA0B,GAAG9hB,gCAAgC,CAAC,KAAKkgB,SAAN,CAAnE;EACAnZ,QAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKiX,SAAtB,EAAiC,eAAjC,EAAkD6B,cAAlD;EACAjhB,QAAAA,oBAAoB,CAAC,KAAKof,SAAN,EAAiB4B,0BAAjB,CAApB;EACD,OAJD,MAIO;EACLC,QAAAA,cAAc;EACf;EACF,KAfM,MAeA;EACL9d,MAAAA,QAAQ;EACT;EACF;;EAEDsc,EAAAA,WAAW,GAAG;EACZ,WAAO,KAAK7V,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCsI,iBAAjC,CAAP;EACD;;EAEDgW,EAAAA,0BAA0B,GAAG;EAC3B,UAAMhE,SAAS,GAAGxW,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCsU,oBAApC,CAAlB;;EACA,QAAIzB,SAAS,CAAC3T,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMoY,kBAAkB,GAAG,KAAKtX,QAAL,CAAcuX,YAAd,GAA6B/iB,QAAQ,CAACmE,eAAT,CAAyB6e,YAAjF;;EAEA,QAAI,CAACF,kBAAL,EAAyB;EACvB,WAAKtX,QAAL,CAAcnI,KAAd,CAAoB4f,SAApB,GAAgC,QAAhC;EACD;;EAED,SAAKzX,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4BqK,iBAA5B;;EACA,UAAM0C,uBAAuB,GAAGpiB,gCAAgC,CAAC,KAAKigB,OAAN,CAAhE;EACAlZ,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC,eAAhC;EACA3D,IAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD,MAAM;EACrD,WAAKA,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B+Z,iBAA/B;;EACA,UAAI,CAACsC,kBAAL,EAAyB;EACvBjb,QAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiD,MAAM;EACrD,eAAKA,QAAL,CAAcnI,KAAd,CAAoB4f,SAApB,GAAgC,EAAhC;EACD,SAFD;EAGArhB,QAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgB0X,uBAAhB,CAApB;EACD;EACF,KARD;EASAthB,IAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgB0X,uBAAhB,CAApB;;EACA,SAAK1X,QAAL,CAAc4S,KAAd;EACD,GA3V+B;EA8VhC;EACA;;;EAEAoD,EAAAA,aAAa,GAAG;EACd,UAAMsB,kBAAkB,GAAG,KAAKtX,QAAL,CAAcuX,YAAd,GAA6B/iB,QAAQ,CAACmE,eAAT,CAAyB6e,YAAjF;;EAEA,QAAK,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAA5B,IAAkD,CAAC7d,KAAK,EAAzD,IAAiE,KAAKic,kBAAL,IAA2B,CAAC4B,kBAA5B,IAAkD7d,KAAK,EAA5H,EAAiI;EAC/H,WAAKuG,QAAL,CAAcnI,KAAd,CAAoB8f,WAApB,GAAmC,GAAE,KAAK/B,eAAgB,IAA1D;EACD;;EAED,QAAK,KAAKF,kBAAL,IAA2B,CAAC4B,kBAA5B,IAAkD,CAAC7d,KAAK,EAAzD,IAAiE,CAAC,KAAKic,kBAAN,IAA4B4B,kBAA5B,IAAkD7d,KAAK,EAA5H,EAAiI;EAC/H,WAAKuG,QAAL,CAAcnI,KAAd,CAAoB+f,YAApB,GAAoC,GAAE,KAAKhC,eAAgB,IAA3D;EACD;EACF;;EAEDkB,EAAAA,iBAAiB,GAAG;EAClB,SAAK9W,QAAL,CAAcnI,KAAd,CAAoB8f,WAApB,GAAkC,EAAlC;EACA,SAAK3X,QAAL,CAAcnI,KAAd,CAAoB+f,YAApB,GAAmC,EAAnC;EACD;;EAED9B,EAAAA,eAAe,GAAG;EAChB,UAAM3S,IAAI,GAAG3O,QAAQ,CAAC6E,IAAT,CAAc+J,qBAAd,EAAb;EACA,SAAKsS,kBAAL,GAA0BrhB,IAAI,CAACwjB,KAAL,CAAW1U,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAAC2U,KAA5B,IAAqCriB,MAAM,CAACsiB,UAAtE;EACA,SAAKnC,eAAL,GAAuB,KAAKoC,kBAAL,EAAvB;EACD;;EAEDjC,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKL,kBAAT,EAA6B;EAC3B,WAAKuC,qBAAL,CAA2B7C,wBAA3B,EAAmD,cAAnD,EAAmE8C,eAAe,IAAIA,eAAe,GAAG,KAAKtC,eAA7G;;EACA,WAAKqC,qBAAL,CAA2B5C,yBAA3B,EAAoD,aAApD,EAAmE6C,eAAe,IAAIA,eAAe,GAAG,KAAKtC,eAA7G;;EACA,WAAKqC,qBAAL,CAA2B,MAA3B,EAAmC,cAAnC,EAAmDC,eAAe,IAAIA,eAAe,GAAG,KAAKtC,eAA7F;EACD;;EAEDphB,IAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwBqS,GAAxB,CAA4BoK,eAA5B;EACD;;EAEDkD,EAAAA,qBAAqB,CAACrjB,QAAD,EAAWujB,SAAX,EAAsB5e,QAAtB,EAAgC;EACnDsK,IAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EACGuC,OADH,CACWxC,OAAO,IAAI;EAClB,UAAIA,OAAO,KAAKH,QAAQ,CAAC6E,IAArB,IAA6B5D,MAAM,CAACsiB,UAAP,GAAoBpjB,OAAO,CAACyjB,WAAR,GAAsB,KAAKxC,eAAhF,EAAiG;EAC/F;EACD;;EAED,YAAMyC,WAAW,GAAG1jB,OAAO,CAACkD,KAAR,CAAcsgB,SAAd,CAApB;EACA,YAAMD,eAAe,GAAGziB,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiCwjB,SAAjC,CAAxB;EACA5V,MAAAA,WAAW,CAACC,gBAAZ,CAA6B7N,OAA7B,EAAsCwjB,SAAtC,EAAiDE,WAAjD;EACA1jB,MAAAA,OAAO,CAACkD,KAAR,CAAcsgB,SAAd,IAA2B5e,QAAQ,CAAC3D,MAAM,CAACC,UAAP,CAAkBqiB,eAAlB,CAAD,CAAR,GAA+C,IAA1E;EACD,KAVH;EAWD;;EAEDnB,EAAAA,eAAe,GAAG;EAChB,SAAKuB,uBAAL,CAA6BlD,wBAA7B,EAAqD,cAArD;;EACA,SAAKkD,uBAAL,CAA6BjD,yBAA7B,EAAsD,aAAtD;;EACA,SAAKiD,uBAAL,CAA6B,MAA7B,EAAqC,cAArC;EACD;;EAEDA,EAAAA,uBAAuB,CAAC1jB,QAAD,EAAWujB,SAAX,EAAsB;EAC3CtU,IAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EAA8BuC,OAA9B,CAAsCxC,OAAO,IAAI;EAC/C,YAAM2C,KAAK,GAAGiL,WAAW,CAACU,gBAAZ,CAA6BtO,OAA7B,EAAsCwjB,SAAtC,CAAd;;EACA,UAAI,OAAO7gB,KAAP,KAAiB,WAAjB,IAAgC3C,OAAO,KAAKH,QAAQ,CAAC6E,IAAzD,EAA+D;EAC7D1E,QAAAA,OAAO,CAACkD,KAAR,CAAcsgB,SAAd,IAA2B,EAA3B;EACD,OAFD,MAEO;EACL5V,QAAAA,WAAW,CAACE,mBAAZ,CAAgC9N,OAAhC,EAAyCwjB,SAAzC;EACAxjB,QAAAA,OAAO,CAACkD,KAAR,CAAcsgB,SAAd,IAA2B7gB,KAA3B;EACD;EACF,KARD;EASD;;EAED0gB,EAAAA,kBAAkB,GAAG;EAAE;EACrB,UAAMO,SAAS,GAAG/jB,QAAQ,CAACyiB,aAAT,CAAuB,KAAvB,CAAlB;EACAsB,IAAAA,SAAS,CAACrB,SAAV,GAAsBrC,6BAAtB;EACArgB,IAAAA,QAAQ,CAAC6E,IAAT,CAAcqd,WAAd,CAA0B6B,SAA1B;EACA,UAAMC,cAAc,GAAGD,SAAS,CAACnV,qBAAV,GAAkCqV,KAAlC,GAA0CF,SAAS,CAACH,WAA3E;EACA5jB,IAAAA,QAAQ,CAAC6E,IAAT,CAAcmI,WAAd,CAA0B+W,SAA1B;EACA,WAAOC,cAAP;EACD,GAza+B;;;EA6aV,SAAfve,eAAe,CAAClD,MAAD,EAASwU,aAAT,EAAwB;EAC5C,WAAO,KAAK9J,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;EACA,YAAMmI,OAAO,GAAG,EACd,GAAGnD,SADW;EAEd,WAAG3C,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFW;EAGd,YAAI,OAAO5L,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHc,OAAhB;;EAMA,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI4T,KAAJ,CAAU,IAAV,EAAgBjN,OAAhB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAawU,aAAb;EACD;EACF,KAnBM,CAAP;EAoBD;;EAlc+B;EAqclC;EACA;EACA;EACA;EACA;;;EAEAlP,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrF,QAAMU,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,KAAKuV,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnD1O,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EAEDvD,EAAAA,YAAY,CAACkC,GAAb,CAAiB3B,MAAjB,EAAyBsQ,YAAzB,EAAqCiF,SAAS,IAAI;EAChD,QAAIA,SAAS,CAACjT,gBAAd,EAAgC;EAC9B;EACA;EACD;;EAED7C,IAAAA,YAAY,CAACkC,GAAb,CAAiB3B,MAAjB,EAAyByQ,cAAzB,EAAuC,MAAM;EAC3C,UAAIzV,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,aAAKgb,KAAL;EACD;EACF,KAJD;EAKD,GAXD;EAaA,MAAIlR,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAASiC,MAAT,EAAiBsD,UAAjB,CAAX;;EACA,MAAI,CAACwB,IAAL,EAAW;EACT,UAAM3K,MAAM,GAAG,EACb,GAAGwL,WAAW,CAACI,iBAAZ,CAA8B/F,MAA9B,CADU;EAEb,SAAG2F,WAAW,CAACI,iBAAZ,CAA8B,IAA9B;EAFU,KAAf;EAKAjB,IAAAA,IAAI,GAAG,IAAI4T,KAAJ,CAAU1Y,MAAV,EAAkB7F,MAAlB,CAAP;EACD;;EAED2K,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;EACD,CA/BD;EAiCA;EACA;EACA;EACA;EACA;EACA;;EAEArI,kBAAkB,CAAC0G,MAAD,EAAOiV,KAAP,CAAlB;;ECnkBA;EACA;EACA;EACA;EACA;EACA;EAKA,MAAMF,sBAAsB,GAAG,sCAA/B;EACA,MAAMC,uBAAuB,GAAG,aAAhC;;EAEA,MAAMqD,QAAQ,GAAG,MAAM;EACrB;EACA,QAAMC,aAAa,GAAGnkB,QAAQ,CAACmE,eAAT,CAAyByf,WAA/C;EACA,SAAO/jB,IAAI,CAACyV,GAAL,CAASrU,MAAM,CAACsiB,UAAP,GAAoBY,aAA7B,CAAP;EACD,CAJD;;EAMA,MAAMnK,IAAI,GAAG,CAACiK,KAAK,GAAGC,QAAQ,EAAjB,KAAwB;EACnClkB,EAAAA,QAAQ,CAAC6E,IAAT,CAAcxB,KAAd,CAAoB+gB,QAApB,GAA+B,QAA/B;;EACAX,EAAAA,qBAAqB,CAAC7C,sBAAD,EAAyB,cAAzB,EAAyC8C,eAAe,IAAIA,eAAe,GAAGO,KAA9E,CAArB;;EACAR,EAAAA,qBAAqB,CAAC5C,uBAAD,EAA0B,aAA1B,EAAyC6C,eAAe,IAAIA,eAAe,GAAGO,KAA9E,CAArB;;EACAR,EAAAA,qBAAqB,CAAC,MAAD,EAAS,cAAT,EAAyBC,eAAe,IAAIA,eAAe,GAAGO,KAA9D,CAArB;EACD,CALD;;EAOA,MAAMR,qBAAqB,GAAG,CAACrjB,QAAD,EAAWujB,SAAX,EAAsB5e,QAAtB,KAAmC;EAC/D,QAAMif,cAAc,GAAGE,QAAQ,EAA/B;EACA7U,EAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EACGuC,OADH,CACWxC,OAAO,IAAI;EAClB,QAAIA,OAAO,KAAKH,QAAQ,CAAC6E,IAArB,IAA6B5D,MAAM,CAACsiB,UAAP,GAAoBpjB,OAAO,CAACyjB,WAAR,GAAsBI,cAA3E,EAA2F;EACzF;EACD;;EAED,UAAMH,WAAW,GAAG1jB,OAAO,CAACkD,KAAR,CAAcsgB,SAAd,CAApB;EACA,UAAMD,eAAe,GAAGziB,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,EAAiCwjB,SAAjC,CAAxB;EACA5V,IAAAA,WAAW,CAACC,gBAAZ,CAA6B7N,OAA7B,EAAsCwjB,SAAtC,EAAiDE,WAAjD;EACA1jB,IAAAA,OAAO,CAACkD,KAAR,CAAcsgB,SAAd,IAA2B5e,QAAQ,CAAC3D,MAAM,CAACC,UAAP,CAAkBqiB,eAAlB,CAAD,CAAR,GAA+C,IAA1E;EACD,GAVH;EAWD,CAbD;;EAeA,MAAMW,KAAK,GAAG,MAAM;EAClBrkB,EAAAA,QAAQ,CAAC6E,IAAT,CAAcxB,KAAd,CAAoB+gB,QAApB,GAA+B,MAA/B;;EACAN,EAAAA,uBAAuB,CAAClD,sBAAD,EAAyB,cAAzB,CAAvB;;EACAkD,EAAAA,uBAAuB,CAACjD,uBAAD,EAA0B,aAA1B,CAAvB;;EACAiD,EAAAA,uBAAuB,CAAC,MAAD,EAAS,cAAT,CAAvB;EACD,CALD;;EAOA,MAAMA,uBAAuB,GAAG,CAAC1jB,QAAD,EAAWujB,SAAX,KAAyB;EACvDtU,EAAAA,cAAc,CAACC,IAAf,CAAoBlP,QAApB,EAA8BuC,OAA9B,CAAsCxC,OAAO,IAAI;EAC/C,UAAM2C,KAAK,GAAGiL,WAAW,CAACU,gBAAZ,CAA6BtO,OAA7B,EAAsCwjB,SAAtC,CAAd;;EACA,QAAI,OAAO7gB,KAAP,KAAiB,WAAjB,IAAgC3C,OAAO,KAAKH,QAAQ,CAAC6E,IAAzD,EAA+D;EAC7D1E,MAAAA,OAAO,CAACkD,KAAR,CAAcihB,cAAd,CAA6BX,SAA7B;EACD,KAFD,MAEO;EACL5V,MAAAA,WAAW,CAACE,mBAAZ,CAAgC9N,OAAhC,EAAyCwjB,SAAzC;EACAxjB,MAAAA,OAAO,CAACkD,KAAR,CAAcsgB,SAAd,IAA2B7gB,KAA3B;EACD;EACF,GARD;EASD,CAVD;;EChDA;EACA;EACA;EACA;EACA;EACA;EAkBA;EACA;EACA;EACA;EACA;;EAEA,MAAM+I,MAAI,GAAG,WAAb;EACA,MAAMH,UAAQ,GAAG,cAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EACA,MAAMkG,qBAAmB,GAAI,OAAMnG,WAAU,GAAEC,cAAa,EAA5D;EACA,MAAMwP,UAAU,GAAG,QAAnB;EAEA,MAAM7K,SAAO,GAAG;EACdmP,EAAAA,QAAQ,EAAE,IADI;EAEdjP,EAAAA,QAAQ,EAAE,IAFI;EAGd2T,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,MAAMtT,aAAW,GAAG;EAClB4O,EAAAA,QAAQ,EAAE,SADQ;EAElBjP,EAAAA,QAAQ,EAAE,SAFQ;EAGlB2T,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,MAAMC,wBAAwB,GAAG,oBAAjC;EACA,MAAMlY,iBAAe,GAAG,MAAxB;EACA,MAAMmY,mBAAmB,GAAG,oBAA5B;EACA,MAAMC,aAAa,GAAG,iBAAtB;EACA,MAAMC,eAAe,GAAI,GAAED,aAAc,MAAKD,mBAAoB,EAAlE;EAEA,MAAM/L,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAM8M,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAMiU,aAAa,GAAI,UAASjU,WAAU,EAA1C;EACA,MAAMK,sBAAoB,GAAI,QAAOL,WAAU,GAAEC,cAAa,EAA9D;EACA,MAAMkU,qBAAmB,GAAI,gBAAenU,WAAU,EAAtD;EAEA,MAAM6U,uBAAqB,GAAG,+BAA9B;EACA,MAAMrT,sBAAoB,GAAG,8BAA7B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMsX,SAAN,SAAwBtZ,aAAxB,CAAsC;EACpCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAK0T,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAK0e,QAAL,GAAgB,KAAhB;;EACA,SAAK5M,kBAAL;EACD,GAPmC;;;EAWlB,aAAP3D,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAjBmC;;;EAqBpC8B,EAAAA,MAAM,CAACuJ,aAAD,EAAgB;EACpB,WAAO,KAAKkK,QAAL,GAAgB,KAAKjH,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUlD,aAAV,CAArC;EACD;;EAEDkD,EAAAA,IAAI,CAAClD,aAAD,EAAgB;EAClB,QAAI,KAAKkK,QAAT,EAAmB;EACjB;EACD;;EAED,UAAMtD,SAAS,GAAG9V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD;EAAE3B,MAAAA;EAAF,KAAhD,CAAlB;;EAEA,QAAI4G,SAAS,CAACjT,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKuW,QAAL,GAAgB,IAAhB;EACA,SAAKzV,QAAL,CAAcnI,KAAd,CAAoBK,UAApB,GAAiC,SAAjC;;EAEA,QAAI,KAAKmQ,OAAL,CAAagM,QAAjB,EAA2B;EACzB7f,MAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwBqS,GAAxB,CAA4BqO,wBAA5B;EACD;;EAED,QAAI,CAAC,KAAK3Q,OAAL,CAAa0Q,MAAlB,EAA0B;EACxBM,MAAAA,IAAa;EACd;;EAED,SAAKrZ,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4BsO,mBAA5B;;EACA,SAAKjZ,QAAL,CAAc0C,eAAd,CAA8B,aAA9B;;EACA,SAAK1C,QAAL,CAAciC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAKjC,QAAL,CAAciC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAKjC,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B7J,iBAA5B;;EAEA,UAAMwY,gBAAgB,GAAG,MAAM;EAC7B,WAAKtZ,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+Bge,mBAA/B;;EACA5c,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD;EAAE5B,QAAAA;EAAF,OAAjD;;EACA,WAAKgO,sBAAL,CAA4B,KAAKvZ,QAAjC;EACD,KAJD;;EAMApJ,IAAAA,UAAU,CAAC0iB,gBAAD,EAAmBhkB,gCAAgC,CAAC,KAAK0K,QAAN,CAAnD,CAAV;EACD;;EAEDwO,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKiH,QAAV,EAAoB;EAClB;EACD;;EAED,UAAM5C,SAAS,GAAGxW,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,YAApC,CAAlB;;EAEA,QAAIyF,SAAS,CAAC3T,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKc,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4BsO,mBAA5B;;EACA5c,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B+f,aAA3B;;EACA,SAAKvU,QAAL,CAAcwZ,IAAd;;EACA,SAAK/D,QAAL,GAAgB,KAAhB;;EACA,SAAKzV,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,iBAA/B;;EAEA,UAAM2Y,gBAAgB,GAAG,MAAM;EAC7B,WAAKzZ,QAAL,CAAciC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,WAAKjC,QAAL,CAAc0C,eAAd,CAA8B,YAA9B;;EACA,WAAK1C,QAAL,CAAc0C,eAAd,CAA8B,MAA9B;;EACA,WAAK1C,QAAL,CAAcnI,KAAd,CAAoBK,UAApB,GAAiC,QAAjC;;EAEA,UAAI,KAAKmQ,OAAL,CAAagM,QAAjB,EAA2B;EACzB7f,QAAAA,QAAQ,CAAC6E,IAAT,CAAcf,SAAd,CAAwB2C,MAAxB,CAA+B+d,wBAA/B;EACD;;EAED,UAAI,CAAC,KAAK3Q,OAAL,CAAa0Q,MAAlB,EAA0B;EACxBW,QAAAA,KAAc;EACf;;EAEDrd,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,cAApC;;EACA,WAAKrN,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+Bge,mBAA/B;EACD,KAhBD;;EAkBAriB,IAAAA,UAAU,CAAC6iB,gBAAD,EAAmBnkB,gCAAgC,CAAC,KAAK0K,QAAN,CAAnD,CAAV;EACD,GAlGmC;;;EAsGpCsI,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,SAAG3C,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAFI;EAGP,UAAI,OAAOjJ,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C;EAHO,KAAT;EAKAF,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EACA,WAAO1O,MAAP;EACD;;EAEDwiB,EAAAA,sBAAsB,CAAC5kB,OAAD,EAAU;EAC9B0H,IAAAA,YAAY,CAACC,GAAb,CAAiB9H,QAAjB,EAA2B+f,aAA3B,EAD8B;;EAE9BlY,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B+f,aAA1B,EAAyCrY,KAAK,IAAI;EAChD,UAAI1H,QAAQ,KAAK0H,KAAK,CAACU,MAAnB,IACFjI,OAAO,KAAKuH,KAAK,CAACU,MADhB,IAEF,CAACjI,OAAO,CAAC4D,QAAR,CAAiB2D,KAAK,CAACU,MAAvB,CAFH,EAEmC;EACjCjI,QAAAA,OAAO,CAACie,KAAR;EACD;EACF,KAND;EAOAje,IAAAA,OAAO,CAACie,KAAR;EACD;;EAED/J,EAAAA,kBAAkB,GAAG;EACnBxM,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByU,qBAA/B,EAAoDU,uBAApD,EAA2E,MAAM,KAAK3G,IAAL,EAAjF;EAEAnS,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0B,SAA1B,EAAqC0H,KAAK,IAAI;EAC5C,UAAI,KAAKmM,OAAL,CAAajD,QAAb,IAAyBlJ,KAAK,CAAC3B,GAAN,KAAcwV,UAA3C,EAAuD;EACrD,aAAKvB,IAAL;EACD;EACF,KAJD;EAMAnS,IAAAA,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDzE,KAAK,IAAI;EACvD,YAAMU,MAAM,GAAGiH,cAAc,CAACK,OAAf,CAAuB/O,sBAAsB,CAAC+G,KAAK,CAACU,MAAP,CAA7C,CAAf;;EACA,UAAI,CAAC,KAAKoD,QAAL,CAAczH,QAAd,CAAuB2D,KAAK,CAACU,MAA7B,CAAD,IAAyCA,MAAM,KAAK,KAAKoD,QAA7D,EAAuE;EACrE,aAAKwO,IAAL;EACD;EACF,KALD;EAMD,GA3ImC;;;EA+Id,SAAfvU,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,KAA4B,IAAIkZ,SAAJ,CAAc,IAAd,EAAoB,OAAOriB,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1D,CAAzC;;EAEA,UAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B;EACD;;EAED,UAAI2K,IAAI,CAAC3K,MAAD,CAAJ,KAAiBjD,SAAjB,IAA8BiD,MAAM,CAAC/B,UAAP,CAAkB,GAAlB,CAA9B,IAAwD+B,MAAM,KAAK,aAAvE,EAAsF;EACpF,cAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,MAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAa,IAAb;EACD,KAZM,CAAP;EAaD;;EA7JmC;EAgKtC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,sBAA1B,EAAgDmB,sBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrF,QAAMU,MAAM,GAAGvH,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,CAAC,GAAD,EAAM,MAAN,EAAcN,QAAd,CAAuB,KAAK6V,OAA5B,CAAJ,EAA0C;EACxC1O,IAAAA,KAAK,CAAC0D,cAAN;EACD;;EAED,MAAIzH,UAAU,CAAC,IAAD,CAAd,EAAsB;EACpB;EACD;;EAEDkE,EAAAA,YAAY,CAACkC,GAAb,CAAiB3B,MAAjB,EAAyByQ,cAAzB,EAAuC,MAAM;EAC3C;EACA,QAAIzV,SAAS,CAAC,IAAD,CAAb,EAAqB;EACnB,WAAKgb,KAAL;EACD;EACF,GALD,EAXqF;;EAmBrF,QAAM+G,YAAY,GAAG9V,cAAc,CAACK,OAAf,CAAuBiV,eAAvB,CAArB;;EACA,MAAIQ,YAAY,IAAIA,YAAY,KAAK/c,MAArC,EAA6C;EAC3C;EACD;;EAED,QAAM8E,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAASiC,MAAT,EAAiBsD,UAAjB,KAA8B,IAAIkZ,SAAJ,CAAcxc,MAAd,CAA3C;EAEA8E,EAAAA,IAAI,CAACM,MAAL,CAAY,IAAZ;EACD,CA3BD;EA6BA3F,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwBgR,qBAAxB,EAA6C,MAAM;EACjD5C,EAAAA,cAAc,CAACC,IAAf,CAAoBoV,aAApB,EAAmC/hB,OAAnC,CAA2CyiB,EAAE,IAAI,CAAC3Z,IAAI,CAACtF,GAAL,CAASif,EAAT,EAAa1Z,UAAb,KAA0B,IAAIkZ,SAAJ,CAAcQ,EAAd,CAA3B,EAA8CnL,IAA9C,EAAjD;EACD,CAFD;EAIA;EACA;EACA;EACA;EACA;;EAEA9U,kBAAkB,CAAC0G,MAAD,EAAO+Y,SAAP,CAAlB;;ECpRA;EACA;EACA;EACA;EACA;EACA;EAEA,MAAMS,QAAQ,GAAG,IAAIje,GAAJ,CAAQ,CACvB,YADuB,EAEvB,MAFuB,EAGvB,MAHuB,EAIvB,UAJuB,EAKvB,UALuB,EAMvB,QANuB,EAOvB,KAPuB,EAQvB,YARuB,CAAR,CAAjB;EAWA,MAAMke,sBAAsB,GAAG,gBAA/B;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,4DAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,MAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,MAAMC,gBAAgB,GAAG,CAACC,IAAD,EAAOC,oBAAP,KAAgC;EACvD,QAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcnmB,WAAd,EAAjB;;EAEA,MAAIimB,oBAAoB,CAACplB,QAArB,CAA8BqlB,QAA9B,CAAJ,EAA6C;EAC3C,QAAIP,QAAQ,CAACpf,GAAT,CAAa2f,QAAb,CAAJ,EAA4B;EAC1B,aAAOnc,OAAO,CAAC8b,gBAAgB,CAACtiB,IAAjB,CAAsByiB,IAAI,CAACI,SAA3B,KAAyCN,gBAAgB,CAACviB,IAAjB,CAAsByiB,IAAI,CAACI,SAA3B,CAA1C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,QAAMC,MAAM,GAAGJ,oBAAoB,CAACrX,MAArB,CAA4B0X,SAAS,IAAIA,SAAS,YAAYhjB,MAA9D,CAAf,CAXuD;;EAcvD,OAAK,IAAIqF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGod,MAAM,CAACzd,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;EACjD,QAAI0d,MAAM,CAAC1d,CAAD,CAAN,CAAUpF,IAAV,CAAe2iB,QAAf,CAAJ,EAA8B;EAC5B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD,CArBD;;EAuBO,MAAMK,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;EAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9B3e,EAAAA,CAAC,EAAE,EAlB2B;EAmB9B4e,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAC9D,MAAI,CAACF,UAAU,CAACzf,MAAhB,EAAwB;EACtB,WAAOyf,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,QAAMG,SAAS,GAAG,IAAIjnB,MAAM,CAACknB,SAAX,EAAlB;EACA,QAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;EACA,QAAMO,aAAa,GAAG7lB,MAAM,CAACC,IAAP,CAAYslB,SAAZ,CAAtB;EACA,QAAMO,QAAQ,GAAG,GAAGhZ,MAAH,CAAU,GAAG6Y,eAAe,CAACvjB,IAAhB,CAAqBsD,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;EAEA,OAAK,IAAIE,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG4f,QAAQ,CAACjgB,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;EACnD,UAAM+c,EAAE,GAAGmD,QAAQ,CAAClgB,CAAD,CAAnB;EACA,UAAMmgB,MAAM,GAAGpD,EAAE,CAACS,QAAH,CAAYnmB,WAAZ,EAAf;;EAEA,QAAI,CAAC4oB,aAAa,CAAC/nB,QAAd,CAAuBioB,MAAvB,CAAL,EAAqC;EACnCpD,MAAAA,EAAE,CAAC9hB,UAAH,CAAc0J,WAAd,CAA0BoY,EAA1B;EAEA;EACD;;EAED,UAAMqD,aAAa,GAAG,GAAGlZ,MAAH,CAAU,GAAG6V,EAAE,CAAChX,UAAhB,CAAtB;EACA,UAAMsa,iBAAiB,GAAG,GAAGnZ,MAAH,CAAUyY,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACQ,MAAD,CAAT,IAAqB,EAArD,CAA1B;EAEAC,IAAAA,aAAa,CAAC9lB,OAAd,CAAsB+iB,IAAI,IAAI;EAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOgD,iBAAP,CAArB,EAAgD;EAC9CtD,QAAAA,EAAE,CAAClX,eAAH,CAAmBwX,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EAKD;;EAED,SAAOuC,eAAe,CAACvjB,IAAhB,CAAqB8jB,SAA5B;EACD;;EC9HD;EACA;EACA;EACA;EACA;EACA;EAyBA;EACA;EACA;EACA;EACA;;EAEA,MAAM9c,MAAI,GAAG,SAAb;EACA,MAAMH,UAAQ,GAAG,YAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMkd,cAAY,GAAG,YAArB;EACA,MAAMC,oBAAkB,GAAG,IAAI7lB,MAAJ,CAAY,UAAS4lB,cAAa,MAAlC,EAAyC,GAAzC,CAA3B;EACA,MAAME,qBAAqB,GAAG,IAAI1hB,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B;EAEA,MAAM6J,aAAW,GAAG;EAClB8X,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlB5e,EAAAA,OAAO,EAAE,QAJS;EAKlB6e,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlB/oB,EAAAA,QAAQ,EAAE,kBAPQ;EAQlB4e,EAAAA,SAAS,EAAE,mBARO;EASlBtQ,EAAAA,MAAM,EAAE,yBATU;EAUlB0L,EAAAA,SAAS,EAAE,0BAVO;EAWlBgP,EAAAA,kBAAkB,EAAE,OAXF;EAYlBrM,EAAAA,QAAQ,EAAE,kBAZQ;EAalBsM,EAAAA,WAAW,EAAE,mBAbK;EAclBC,EAAAA,QAAQ,EAAE,SAdQ;EAelBrB,EAAAA,UAAU,EAAE,iBAfM;EAgBlBD,EAAAA,SAAS,EAAE,QAhBO;EAiBlB/K,EAAAA,YAAY,EAAE;EAjBI,CAApB;EAoBA,MAAMsM,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAEzkB,KAAK,KAAK,MAAL,GAAc,OAHN;EAIpB0kB,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE3kB,KAAK,KAAK,OAAL,GAAe;EALN,CAAtB;EAQA,MAAMyL,SAAO,GAAG;EACdqY,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,mCAFF,GAGA,QALI;EAMd3e,EAAAA,OAAO,EAAE,aANK;EAOd4e,EAAAA,KAAK,EAAE,EAPO;EAQdC,EAAAA,KAAK,EAAE,CARO;EASdC,EAAAA,IAAI,EAAE,KATQ;EAUd/oB,EAAAA,QAAQ,EAAE,KAVI;EAWd4e,EAAAA,SAAS,EAAE,KAXG;EAYdtQ,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAZM;EAad0L,EAAAA,SAAS,EAAE,KAbG;EAcdgP,EAAAA,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAdN;EAedrM,EAAAA,QAAQ,EAAE,iBAfI;EAgBdsM,EAAAA,WAAW,EAAE,EAhBC;EAiBdC,EAAAA,QAAQ,EAAE,IAjBI;EAkBdrB,EAAAA,UAAU,EAAE,IAlBE;EAmBdD,EAAAA,SAAS,EAAE/B,gBAnBG;EAoBdhJ,EAAAA,YAAY,EAAE;EApBA,CAAhB;EAuBA,MAAMxb,OAAK,GAAG;EACZooB,EAAAA,IAAI,EAAG,OAAM/d,WAAU,EADX;EAEZge,EAAAA,MAAM,EAAG,SAAQhe,WAAU,EAFf;EAGZie,EAAAA,IAAI,EAAG,OAAMje,WAAU,EAHX;EAIZke,EAAAA,KAAK,EAAG,QAAOle,WAAU,EAJb;EAKZme,EAAAA,QAAQ,EAAG,WAAUne,WAAU,EALnB;EAMZoe,EAAAA,KAAK,EAAG,QAAOpe,WAAU,EANb;EAOZqe,EAAAA,OAAO,EAAG,UAASre,WAAU,EAPjB;EAQZse,EAAAA,QAAQ,EAAG,WAAUte,WAAU,EARnB;EASZue,EAAAA,UAAU,EAAG,aAAYve,WAAU,EATvB;EAUZwe,EAAAA,UAAU,EAAG,aAAYxe,WAAU;EAVvB,CAAd;EAaA,MAAMO,iBAAe,GAAG,MAAxB;EACA,MAAMke,gBAAgB,GAAG,OAAzB;EACA,MAAMje,iBAAe,GAAG,MAAxB;EAEA,MAAMke,gBAAgB,GAAG,MAAzB;EACA,MAAMC,eAAe,GAAG,KAAxB;EAEA,MAAMC,sBAAsB,GAAG,gBAA/B;EAEA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,aAAa,GAAG,OAAtB;EACA,MAAMC,cAAc,GAAG,QAAvB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,OAAN,SAAsBzf,aAAtB,CAAoC;EAClCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,QAAI,OAAOqb,iBAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAI1a,SAAJ,CAAc,8DAAd,CAAN;EACD;;EAED,UAAM/C,OAAN,EAL2B;;EAQ3B,SAAK6qB,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAKhO,OAAL,GAAe,IAAf,CAZ2B;;EAe3B,SAAK5a,MAAL,GAAc,KAAKuR,UAAL,CAAgBvR,MAAhB,CAAd;EACA,SAAK6oB,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;EACD,GApBiC;;;EAwBhB,aAAP3a,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEc,aAAJ7E,IAAI,GAAG;EAChB,WAAOA,MAAP;EACD;;EAEkB,aAARH,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD;;EAEe,aAALjK,KAAK,GAAG;EACjB,WAAOA,OAAP;EACD;;EAEmB,aAATqK,SAAS,GAAG;EACrB,WAAOA,WAAP;EACD;;EAEqB,aAAXmF,WAAW,GAAG;EACvB,WAAOA,aAAP;EACD,GA9CiC;;;EAkDlCqa,EAAAA,MAAM,GAAG;EACP,SAAKN,UAAL,GAAkB,IAAlB;EACD;;EAEDO,EAAAA,OAAO,GAAG;EACR,SAAKP,UAAL,GAAkB,KAAlB;EACD;;EAEDQ,EAAAA,aAAa,GAAG;EACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;EAEDxd,EAAAA,MAAM,CAAC9F,KAAD,EAAQ;EACZ,QAAI,CAAC,KAAKsjB,UAAV,EAAsB;EACpB;EACD;;EAED,QAAItjB,KAAJ,EAAW;EACT,YAAM0X,OAAO,GAAG,KAAKqM,4BAAL,CAAkC/jB,KAAlC,CAAhB;;EAEA0X,MAAAA,OAAO,CAAC+L,cAAR,CAAuBxL,KAAvB,GAA+B,CAACP,OAAO,CAAC+L,cAAR,CAAuBxL,KAAvD;;EAEA,UAAIP,OAAO,CAACsM,oBAAR,EAAJ,EAAoC;EAClCtM,QAAAA,OAAO,CAACuM,MAAR,CAAe,IAAf,EAAqBvM,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACwM,MAAR,CAAe,IAAf,EAAqBxM,OAArB;EACD;EACF,KAVD,MAUO;EACL,UAAI,KAAKyM,aAAL,GAAqB/nB,SAArB,CAA+BC,QAA/B,CAAwCuI,iBAAxC,CAAJ,EAA8D;EAC5D,aAAKsf,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;EAEDhgB,EAAAA,OAAO,GAAG;EACRqK,IAAAA,YAAY,CAAC,KAAKiV,QAAN,CAAZ;EAEApjB,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgC,KAAKD,WAAL,CAAiBO,SAAjD;EACAjE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAL,CAAcsB,OAAd,CAAuB,IAAGyd,gBAAiB,EAA3C,CAAjB,EAAgE,eAAhE,EAAiF,KAAKuB,iBAAtF;;EAEA,QAAI,KAAKV,GAAL,IAAY,KAAKA,GAAL,CAAS9nB,UAAzB,EAAqC;EACnC,WAAK8nB,GAAL,CAAS9nB,UAAT,CAAoB0J,WAApB,CAAgC,KAAKoe,GAArC;EACD;;EAED,SAAKJ,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,WAAL,GAAmB,IAAnB;EACA,SAAKC,cAAL,GAAsB,IAAtB;;EACA,QAAI,KAAKhO,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAamB,OAAb;EACD;;EAED,SAAKnB,OAAL,GAAe,IAAf;EACA,SAAK5a,MAAL,GAAc,IAAd;EACA,SAAK6oB,GAAL,GAAW,IAAX;EACA,UAAMzf,OAAN;EACD;;EAEDsO,EAAAA,IAAI,GAAG;EACL,QAAI,KAAKzO,QAAL,CAAcnI,KAAd,CAAoBI,OAApB,KAAgC,MAApC,EAA4C;EAC1C,YAAM,IAAIsoB,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAI,EAAE,KAAKC,aAAL,MAAwB,KAAKhB,UAA/B,CAAJ,EAAgD;EAC9C;EACD;;EAED,UAAMrN,SAAS,GAAG9V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBsoB,IAA3D,CAAlB;EACA,UAAMkC,UAAU,GAAG/nB,cAAc,CAAC,KAAKsH,QAAN,CAAjC;EACA,UAAM0gB,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAKzgB,QAAL,CAAc2gB,aAAd,CAA4BhoB,eAA5B,CAA4CJ,QAA5C,CAAqD,KAAKyH,QAA1D,CADiB,GAEjBygB,UAAU,CAACloB,QAAX,CAAoB,KAAKyH,QAAzB,CAFF;;EAIA,QAAImS,SAAS,CAACjT,gBAAV,IAA8B,CAACwhB,UAAnC,EAA+C;EAC7C;EACD;;EAED,UAAMd,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,UAAMO,KAAK,GAAGzsB,MAAM,CAAC,KAAK4L,WAAL,CAAiBM,IAAlB,CAApB;EAEAuf,IAAAA,GAAG,CAAC3d,YAAJ,CAAiB,IAAjB,EAAuB2e,KAAvB;;EACA,SAAK5gB,QAAL,CAAciC,YAAd,CAA2B,kBAA3B,EAA+C2e,KAA/C;;EAEA,SAAKC,UAAL;;EAEA,QAAI,KAAK9pB,MAAL,CAAYwmB,SAAhB,EAA2B;EACzBqC,MAAAA,GAAG,CAACtnB,SAAJ,CAAcqS,GAAd,CAAkB9J,iBAAlB;EACD;;EAED,UAAM2S,SAAS,GAAG,OAAO,KAAKzc,MAAL,CAAYyc,SAAnB,KAAiC,UAAjC,GAChB,KAAKzc,MAAL,CAAYyc,SAAZ,CAAsBxf,IAAtB,CAA2B,IAA3B,EAAiC4rB,GAAjC,EAAsC,KAAK5f,QAA3C,CADgB,GAEhB,KAAKjJ,MAAL,CAAYyc,SAFd;;EAIA,UAAMsN,UAAU,GAAG,KAAKC,cAAL,CAAoBvN,SAApB,CAAnB;;EACA,SAAKwN,mBAAL,CAAyBF,UAAzB;;EAEA,UAAMlS,SAAS,GAAG,KAAKqS,aAAL,EAAlB;;EACAhhB,IAAAA,IAAI,CAAC3F,GAAL,CAASslB,GAAT,EAAc,KAAK7f,WAAL,CAAiBG,QAA/B,EAAyC,IAAzC;;EAEA,QAAI,CAAC,KAAKF,QAAL,CAAc2gB,aAAd,CAA4BhoB,eAA5B,CAA4CJ,QAA5C,CAAqD,KAAKqnB,GAA1D,CAAL,EAAqE;EACnEhR,MAAAA,SAAS,CAAC8H,WAAV,CAAsBkJ,GAAtB;EACAvjB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBwoB,QAA3D;EACD;;EAED,QAAI,KAAK9M,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAaoB,MAAb;EACD,KAFD,MAEO;EACL,WAAKpB,OAAL,GAAeS,iBAAM,CAACO,YAAP,CAAoB,KAAK3S,QAAzB,EAAmC4f,GAAnC,EAAwC,KAAKtN,gBAAL,CAAsBwO,UAAtB,CAAxC,CAAf;EACD;;EAEDlB,IAAAA,GAAG,CAACtnB,SAAJ,CAAcqS,GAAd,CAAkB7J,iBAAlB;EAEA,UAAM+c,WAAW,GAAG,OAAO,KAAK9mB,MAAL,CAAY8mB,WAAnB,KAAmC,UAAnC,GAAgD,KAAK9mB,MAAL,CAAY8mB,WAAZ,EAAhD,GAA4E,KAAK9mB,MAAL,CAAY8mB,WAA5G;;EACA,QAAIA,WAAJ,EAAiB;EACf+B,MAAAA,GAAG,CAACtnB,SAAJ,CAAcqS,GAAd,CAAkB,GAAGkT,WAAW,CAAC5oB,KAAZ,CAAkB,GAAlB,CAArB;EACD,KAzDI;EA4DL;EACA;EACA;;;EACA,QAAI,kBAAkBT,QAAQ,CAACmE,eAA/B,EAAgD;EAC9C,SAAGoL,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EAAqChN,OAArC,CAA6CxC,OAAO,IAAI;EACtD0H,QAAAA,YAAY,CAACiC,EAAb,CAAgB3J,OAAhB,EAAyB,WAAzB,EAAsCqE,IAAI,EAA1C;EACD,OAFD;EAGD;;EAED,UAAMoW,QAAQ,GAAG,MAAM;EACrB,YAAM8R,cAAc,GAAG,KAAKxB,WAA5B;EAEA,WAAKA,WAAL,GAAmB,IAAnB;EACArjB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBuoB,KAA3D;;EAEA,UAAI0C,cAAc,KAAKjC,eAAvB,EAAwC;EACtC,aAAKmB,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF,KATD;;EAWA,QAAI,KAAKR,GAAL,CAAStnB,SAAT,CAAmBC,QAAnB,CAA4BsI,iBAA5B,CAAJ,EAAkD;EAChD,YAAMtL,kBAAkB,GAAGD,gCAAgC,CAAC,KAAKsqB,GAAN,CAA3D;EACAvjB,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKqhB,GAAtB,EAA2B,eAA3B,EAA4CxQ,QAA5C;EACAhZ,MAAAA,oBAAoB,CAAC,KAAKwpB,GAAN,EAAWrqB,kBAAX,CAApB;EACD,KAJD,MAIO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDZ,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKmD,OAAV,EAAmB;EACjB;EACD;;EAED,UAAMiO,GAAG,GAAG,KAAKS,aAAL,EAAZ;;EACA,UAAMjR,QAAQ,GAAG,MAAM;EACrB,UAAI,KAAK8Q,oBAAL,EAAJ,EAAiC;EAC/B;EACD;;EAED,UAAI,KAAKR,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAAC9nB,UAAjD,EAA6D;EAC3D8nB,QAAAA,GAAG,CAAC9nB,UAAJ,CAAe0J,WAAf,CAA2Boe,GAA3B;EACD;;EAED,WAAKuB,cAAL;;EACA,WAAKnhB,QAAL,CAAc0C,eAAd,CAA8B,kBAA9B;;EACArG,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBqoB,MAA3D;;EAEA,UAAI,KAAK3M,OAAT,EAAkB;EAChB,aAAKA,OAAL,CAAamB,OAAb;;EACA,aAAKnB,OAAL,GAAe,IAAf;EACD;EACF,KAjBD;;EAmBA,UAAMkB,SAAS,GAAGxW,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoC,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuBooB,IAA3D,CAAlB;;EACA,QAAIxL,SAAS,CAAC3T,gBAAd,EAAgC;EAC9B;EACD;;EAED0gB,IAAAA,GAAG,CAACtnB,SAAJ,CAAc2C,MAAd,CAAqB6F,iBAArB,EA9BK;EAiCL;;EACA,QAAI,kBAAkBtM,QAAQ,CAACmE,eAA/B,EAAgD;EAC9C,SAAGoL,MAAH,CAAU,GAAGvP,QAAQ,CAAC6E,IAAT,CAAc8K,QAA3B,EACGhN,OADH,CACWxC,OAAO,IAAI0H,YAAY,CAACC,GAAb,CAAiB3H,OAAjB,EAA0B,WAA1B,EAAuCqE,IAAvC,CADtB;EAED;;EAED,SAAK2mB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;EACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;EACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;EAEA,QAAI,KAAKS,GAAL,CAAStnB,SAAT,CAAmBC,QAAnB,CAA4BsI,iBAA5B,CAAJ,EAAkD;EAChD,YAAMtL,kBAAkB,GAAGD,gCAAgC,CAACsqB,GAAD,CAA3D;EAEAvjB,MAAAA,YAAY,CAACkC,GAAb,CAAiBqhB,GAAjB,EAAsB,eAAtB,EAAuCxQ,QAAvC;EACAhZ,MAAAA,oBAAoB,CAACwpB,GAAD,EAAMrqB,kBAAN,CAApB;EACD,KALD,MAKO;EACL6Z,MAAAA,QAAQ;EACT;;EAED,SAAKsQ,WAAL,GAAmB,EAAnB;EACD;;EAED3M,EAAAA,MAAM,GAAG;EACP,QAAI,KAAKpB,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAaoB,MAAb;EACD;EACF,GAnQiC;;;EAuQlCyN,EAAAA,aAAa,GAAG;EACd,WAAOviB,OAAO,CAAC,KAAKmjB,QAAL,EAAD,CAAd;EACD;;EAEDf,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKT,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,UAAMjrB,OAAO,GAAGH,QAAQ,CAACyiB,aAAT,CAAuB,KAAvB,CAAhB;EACAtiB,IAAAA,OAAO,CAACwoB,SAAR,GAAoB,KAAKpmB,MAAL,CAAYymB,QAAhC;EAEA,SAAKoC,GAAL,GAAWjrB,OAAO,CAACwP,QAAR,CAAiB,CAAjB,CAAX;EACA,WAAO,KAAKyb,GAAZ;EACD;;EAEDiB,EAAAA,UAAU,GAAG;EACX,UAAMjB,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,SAAKgB,iBAAL,CAAuBxd,cAAc,CAACK,OAAf,CAAuBgb,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAKwB,QAAL,EAA5E;EACAxB,IAAAA,GAAG,CAACtnB,SAAJ,CAAc2C,MAAd,CAAqB4F,iBAArB,EAAsCC,iBAAtC;EACD;;EAEDugB,EAAAA,iBAAiB,CAAC1sB,OAAD,EAAU2sB,OAAV,EAAmB;EAClC,QAAI3sB,OAAO,KAAK,IAAhB,EAAsB;EACpB;EACD;;EAED,QAAI,OAAO2sB,OAAP,KAAmB,QAAnB,IAA+BprB,SAAS,CAACorB,OAAD,CAA5C,EAAuD;EACrD,UAAIA,OAAO,CAAC7R,MAAZ,EAAoB;EAClB6R,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;EACD,OAHoD;;;EAMrD,UAAI,KAAKvqB,MAAL,CAAY4mB,IAAhB,EAAsB;EACpB,YAAI2D,OAAO,CAACxpB,UAAR,KAAuBnD,OAA3B,EAAoC;EAClCA,UAAAA,OAAO,CAACwoB,SAAR,GAAoB,EAApB;EACAxoB,UAAAA,OAAO,CAAC+hB,WAAR,CAAoB4K,OAApB;EACD;EACF,OALD,MAKO;EACL3sB,QAAAA,OAAO,CAAC4sB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;EACD;;EAED;EACD;;EAED,QAAI,KAAKxqB,MAAL,CAAY4mB,IAAhB,EAAsB;EACpB,UAAI,KAAK5mB,MAAL,CAAY+mB,QAAhB,EAA0B;EACxBwD,QAAAA,OAAO,GAAGhF,YAAY,CAACgF,OAAD,EAAU,KAAKvqB,MAAL,CAAYylB,SAAtB,EAAiC,KAAKzlB,MAAL,CAAY0lB,UAA7C,CAAtB;EACD;;EAED9nB,MAAAA,OAAO,CAACwoB,SAAR,GAAoBmE,OAApB;EACD,KAND,MAMO;EACL3sB,MAAAA,OAAO,CAAC4sB,WAAR,GAAsBD,OAAtB;EACD;EACF;;EAEDF,EAAAA,QAAQ,GAAG;EACT,QAAI3D,KAAK,GAAG,KAAKzd,QAAL,CAAcnL,YAAd,CAA2B,wBAA3B,CAAZ;;EAEA,QAAI,CAAC4oB,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAK1mB,MAAL,CAAY0mB,KAAnB,KAA6B,UAA7B,GACN,KAAK1mB,MAAL,CAAY0mB,KAAZ,CAAkBzpB,IAAlB,CAAuB,KAAKgM,QAA5B,CADM,GAEN,KAAKjJ,MAAL,CAAY0mB,KAFd;EAGD;;EAED,WAAOA,KAAP;EACD;;EAED+D,EAAAA,gBAAgB,CAACV,UAAD,EAAa;EAC3B,QAAIA,UAAU,KAAK,OAAnB,EAA4B;EAC1B,aAAO,KAAP;EACD;;EAED,QAAIA,UAAU,KAAK,MAAnB,EAA2B;EACzB,aAAO,OAAP;EACD;;EAED,WAAOA,UAAP;EACD,GArViC;;;EAyVlCb,EAAAA,4BAA4B,CAAC/jB,KAAD,EAAQ0X,OAAR,EAAiB;EAC3C,UAAM6N,OAAO,GAAG,KAAK1hB,WAAL,CAAiBG,QAAjC;EACA0T,IAAAA,OAAO,GAAGA,OAAO,IAAI3T,IAAI,CAACtF,GAAL,CAASuB,KAAK,CAACC,cAAf,EAA+BslB,OAA/B,CAArB;;EAEA,QAAI,CAAC7N,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAK7T,WAAT,CAAqB7D,KAAK,CAACC,cAA3B,EAA2C,KAAKulB,kBAAL,EAA3C,CAAV;EACAzhB,MAAAA,IAAI,CAAC3F,GAAL,CAAS4B,KAAK,CAACC,cAAf,EAA+BslB,OAA/B,EAAwC7N,OAAxC;EACD;;EAED,WAAOA,OAAP;EACD;;EAEDR,EAAAA,UAAU,GAAG;EACX,UAAM;EAAElQ,MAAAA;EAAF,QAAa,KAAKnM,MAAxB;;EAEA,QAAI,OAAOmM,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aAAOA,MAAM,CAACjO,KAAP,CAAa,GAAb,EAAkBoe,GAAlB,CAAsBjR,GAAG,IAAIxM,MAAM,CAACkW,QAAP,CAAgB1J,GAAhB,EAAqB,EAArB,CAA7B,CAAP;EACD;;EAED,QAAI,OAAOc,MAAP,KAAkB,UAAtB,EAAkC;EAChC,aAAOoQ,UAAU,IAAIpQ,MAAM,CAACoQ,UAAD,EAAa,KAAKtT,QAAlB,CAA3B;EACD;;EAED,WAAOkD,MAAP;EACD;;EAEDoP,EAAAA,gBAAgB,CAACwO,UAAD,EAAa;EAC3B,UAAMvN,qBAAqB,GAAG;EAC5BC,MAAAA,SAAS,EAAEsN,UADiB;EAE5BtO,MAAAA,SAAS,EAAE,CACT;EACE5Y,QAAAA,IAAI,EAAE,MADR;EAEE6Z,QAAAA,OAAO,EAAE;EACPkO,UAAAA,WAAW,EAAE,IADN;EAEP/D,UAAAA,kBAAkB,EAAE,KAAK7mB,MAAL,CAAY6mB;EAFzB;EAFX,OADS,EAQT;EACEhkB,QAAAA,IAAI,EAAE,QADR;EAEE6Z,QAAAA,OAAO,EAAE;EACPvQ,UAAAA,MAAM,EAAE,KAAKkQ,UAAL;EADD;EAFX,OARS,EAcT;EACExZ,QAAAA,IAAI,EAAE,iBADR;EAEE6Z,QAAAA,OAAO,EAAE;EACPlC,UAAAA,QAAQ,EAAE,KAAKxa,MAAL,CAAYwa;EADf;EAFX,OAdS,EAoBT;EACE3X,QAAAA,IAAI,EAAE,OADR;EAEE6Z,QAAAA,OAAO,EAAE;EACP9e,UAAAA,OAAO,EAAG,IAAG,KAAKoL,WAAL,CAAiBM,IAAK;EAD5B;EAFX,OApBS,EA0BT;EACEzG,QAAAA,IAAI,EAAE,UADR;EAEE8Y,QAAAA,OAAO,EAAE,IAFX;EAGEkP,QAAAA,KAAK,EAAE,YAHT;EAIE5nB,QAAAA,EAAE,EAAE0H,IAAI,IAAI,KAAKmgB,4BAAL,CAAkCngB,IAAlC;EAJd,OA1BS,CAFiB;EAmC5BogB,MAAAA,aAAa,EAAEpgB,IAAI,IAAI;EACrB,YAAIA,IAAI,CAAC+R,OAAL,CAAaD,SAAb,KAA2B9R,IAAI,CAAC8R,SAApC,EAA+C;EAC7C,eAAKqO,4BAAL,CAAkCngB,IAAlC;EACD;EACF;EAvC2B,KAA9B;EA0CA,WAAO,EACL,GAAG6R,qBADE;EAEL,UAAI,OAAO,KAAKxc,MAAL,CAAY0a,YAAnB,KAAoC,UAApC,GAAiD,KAAK1a,MAAL,CAAY0a,YAAZ,CAAyB8B,qBAAzB,CAAjD,GAAmG,KAAKxc,MAAL,CAAY0a,YAAnH;EAFK,KAAP;EAID;;EAEDuP,EAAAA,mBAAmB,CAACF,UAAD,EAAa;EAC9B,SAAKT,aAAL,GAAqB/nB,SAArB,CAA+BqS,GAA/B,CAAoC,GAAEyS,cAAa,IAAG,KAAKoE,gBAAL,CAAsBV,UAAtB,CAAkC,EAAxF;EACD;;EAEDG,EAAAA,aAAa,GAAG;EACd,QAAI,KAAKlqB,MAAL,CAAY6X,SAAZ,KAA0B,KAA9B,EAAqC;EACnC,aAAOpa,QAAQ,CAAC6E,IAAhB;EACD;;EAED,QAAInD,SAAS,CAAC,KAAKa,MAAL,CAAY6X,SAAb,CAAb,EAAsC;EACpC,aAAO,KAAK7X,MAAL,CAAY6X,SAAnB;EACD;;EAED,WAAO/K,cAAc,CAACK,OAAf,CAAuB,KAAKnN,MAAL,CAAY6X,SAAnC,CAAP;EACD;;EAEDmS,EAAAA,cAAc,CAACvN,SAAD,EAAY;EACxB,WAAOuK,aAAa,CAACvK,SAAS,CAAC7b,WAAV,EAAD,CAApB;EACD;;EAEDkoB,EAAAA,aAAa,GAAG;EACd,UAAMkC,QAAQ,GAAG,KAAKhrB,MAAL,CAAY8H,OAAZ,CAAoB5J,KAApB,CAA0B,GAA1B,CAAjB;EAEA8sB,IAAAA,QAAQ,CAAC5qB,OAAT,CAAiB0H,OAAO,IAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvBxC,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+B,KAAKD,WAAL,CAAiB9J,KAAjB,CAAuByoB,KAAtD,EAA6D,KAAK3nB,MAAL,CAAYnC,QAAzE,EAAmFsH,KAAK,IAAI,KAAK8F,MAAL,CAAY9F,KAAZ,CAA5F;EACD,OAFD,MAEO,IAAI2C,OAAO,KAAKygB,cAAhB,EAAgC;EACrC,cAAM0C,OAAO,GAAGnjB,OAAO,KAAKsgB,aAAZ,GACd,KAAKpf,WAAL,CAAiB9J,KAAjB,CAAuB4oB,UADT,GAEd,KAAK9e,WAAL,CAAiB9J,KAAjB,CAAuB0oB,OAFzB;EAGA,cAAMsD,QAAQ,GAAGpjB,OAAO,KAAKsgB,aAAZ,GACf,KAAKpf,WAAL,CAAiB9J,KAAjB,CAAuB6oB,UADR,GAEf,KAAK/e,WAAL,CAAiB9J,KAAjB,CAAuB2oB,QAFzB;EAIAviB,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BgiB,OAA/B,EAAwC,KAAKjrB,MAAL,CAAYnC,QAApD,EAA8DsH,KAAK,IAAI,KAAKikB,MAAL,CAAYjkB,KAAZ,CAAvE;EACAG,QAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+BiiB,QAA/B,EAAyC,KAAKlrB,MAAL,CAAYnC,QAArD,EAA+DsH,KAAK,IAAI,KAAKkkB,MAAL,CAAYlkB,KAAZ,CAAxE;EACD;EACF,KAdD;;EAgBA,SAAKokB,iBAAL,GAAyB,MAAM;EAC7B,UAAI,KAAKtgB,QAAT,EAAmB;EACjB,aAAKwO,IAAL;EACD;EACF,KAJD;;EAMAnS,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAAL,CAAcsB,OAAd,CAAuB,IAAGyd,gBAAiB,EAA3C,CAAhB,EAA+D,eAA/D,EAAgF,KAAKuB,iBAArF;;EAEA,QAAI,KAAKvpB,MAAL,CAAYnC,QAAhB,EAA0B;EACxB,WAAKmC,MAAL,GAAc,EACZ,GAAG,KAAKA,MADI;EAEZ8H,QAAAA,OAAO,EAAE,QAFG;EAGZjK,QAAAA,QAAQ,EAAE;EAHE,OAAd;EAKD,KAND,MAMO;EACL,WAAKstB,SAAL;EACD;EACF;;EAEDA,EAAAA,SAAS,GAAG;EACV,UAAMzE,KAAK,GAAG,KAAKzd,QAAL,CAAcnL,YAAd,CAA2B,OAA3B,CAAd;;EACA,UAAMstB,iBAAiB,GAAG,OAAO,KAAKniB,QAAL,CAAcnL,YAAd,CAA2B,wBAA3B,CAAjC;;EAEA,QAAI4oB,KAAK,IAAI0E,iBAAiB,KAAK,QAAnC,EAA6C;EAC3C,WAAKniB,QAAL,CAAciC,YAAd,CAA2B,wBAA3B,EAAqDwb,KAAK,IAAI,EAA9D;;EACA,UAAIA,KAAK,IAAI,CAAC,KAAKzd,QAAL,CAAcnL,YAAd,CAA2B,YAA3B,CAAV,IAAsD,CAAC,KAAKmL,QAAL,CAAcuhB,WAAzE,EAAsF;EACpF,aAAKvhB,QAAL,CAAciC,YAAd,CAA2B,YAA3B,EAAyCwb,KAAzC;EACD;;EAED,WAAKzd,QAAL,CAAciC,YAAd,CAA2B,OAA3B,EAAoC,EAApC;EACD;EACF;;EAEDke,EAAAA,MAAM,CAACjkB,KAAD,EAAQ0X,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKqM,4BAAL,CAAkC/jB,KAAlC,EAAyC0X,OAAzC,CAAV;;EAEA,QAAI1X,KAAJ,EAAW;EACT0X,MAAAA,OAAO,CAAC+L,cAAR,CACEzjB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2B6iB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAIvL,OAAO,CAACyM,aAAR,GAAwB/nB,SAAxB,CAAkCC,QAAlC,CAA2CuI,iBAA3C,KAA+D8S,OAAO,CAAC8L,WAAR,KAAwBV,gBAA3F,EAA6G;EAC3GpL,MAAAA,OAAO,CAAC8L,WAAR,GAAsBV,gBAAtB;EACA;EACD;;EAEDxU,IAAAA,YAAY,CAACoJ,OAAO,CAAC6L,QAAT,CAAZ;EAEA7L,IAAAA,OAAO,CAAC8L,WAAR,GAAsBV,gBAAtB;;EAEA,QAAI,CAACpL,OAAO,CAAC7c,MAAR,CAAe2mB,KAAhB,IAAyB,CAAC9J,OAAO,CAAC7c,MAAR,CAAe2mB,KAAf,CAAqBjP,IAAnD,EAAyD;EACvDmF,MAAAA,OAAO,CAACnF,IAAR;EACA;EACD;;EAEDmF,IAAAA,OAAO,CAAC6L,QAAR,GAAmB7oB,UAAU,CAAC,MAAM;EAClC,UAAIgd,OAAO,CAAC8L,WAAR,KAAwBV,gBAA5B,EAA8C;EAC5CpL,QAAAA,OAAO,CAACnF,IAAR;EACD;EACF,KAJ4B,EAI1BmF,OAAO,CAAC7c,MAAR,CAAe2mB,KAAf,CAAqBjP,IAJK,CAA7B;EAKD;;EAED2R,EAAAA,MAAM,CAAClkB,KAAD,EAAQ0X,OAAR,EAAiB;EACrBA,IAAAA,OAAO,GAAG,KAAKqM,4BAAL,CAAkC/jB,KAAlC,EAAyC0X,OAAzC,CAAV;;EAEA,QAAI1X,KAAJ,EAAW;EACT0X,MAAAA,OAAO,CAAC+L,cAAR,CACEzjB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4B6iB,aAA5B,GAA4CD,aAD9C,IAEIvL,OAAO,CAAC5T,QAAR,CAAiBzH,QAAjB,CAA0B2D,KAAK,CAACqP,aAAhC,CAFJ;EAGD;;EAED,QAAIqI,OAAO,CAACsM,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAED1V,IAAAA,YAAY,CAACoJ,OAAO,CAAC6L,QAAT,CAAZ;EAEA7L,IAAAA,OAAO,CAAC8L,WAAR,GAAsBT,eAAtB;;EAEA,QAAI,CAACrL,OAAO,CAAC7c,MAAR,CAAe2mB,KAAhB,IAAyB,CAAC9J,OAAO,CAAC7c,MAAR,CAAe2mB,KAAf,CAAqBlP,IAAnD,EAAyD;EACvDoF,MAAAA,OAAO,CAACpF,IAAR;EACA;EACD;;EAEDoF,IAAAA,OAAO,CAAC6L,QAAR,GAAmB7oB,UAAU,CAAC,MAAM;EAClC,UAAIgd,OAAO,CAAC8L,WAAR,KAAwBT,eAA5B,EAA6C;EAC3CrL,QAAAA,OAAO,CAACpF,IAAR;EACD;EACF,KAJ4B,EAI1BoF,OAAO,CAAC7c,MAAR,CAAe2mB,KAAf,CAAqBlP,IAJK,CAA7B;EAKD;;EAED0R,EAAAA,oBAAoB,GAAG;EACrB,SAAK,MAAMrhB,OAAX,IAAsB,KAAK8gB,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoB9gB,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;EAEDyJ,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjB,UAAMqrB,cAAc,GAAG7f,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAAvB;EAEA/I,IAAAA,MAAM,CAACC,IAAP,CAAYkrB,cAAZ,EAA4BjrB,OAA5B,CAAoCkrB,QAAQ,IAAI;EAC9C,UAAI/E,qBAAqB,CAAC7iB,GAAtB,CAA0B4nB,QAA1B,CAAJ,EAAyC;EACvC,eAAOD,cAAc,CAACC,QAAD,CAArB;EACD;EACF,KAJD;;EAMA,QAAItrB,MAAM,IAAI,OAAOA,MAAM,CAAC6X,SAAd,KAA4B,QAAtC,IAAkD7X,MAAM,CAAC6X,SAAP,CAAiBa,MAAvE,EAA+E;EAC7E1Y,MAAAA,MAAM,CAAC6X,SAAP,GAAmB7X,MAAM,CAAC6X,SAAP,CAAiB,CAAjB,CAAnB;EACD;;EAED7X,IAAAA,MAAM,GAAG,EACP,GAAG,KAAKgJ,WAAL,CAAiBmF,OADb;EAEP,SAAGkd,cAFI;EAGP,UAAI,OAAOrrB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;;EAMA,QAAI,OAAOA,MAAM,CAAC2mB,KAAd,KAAwB,QAA5B,EAAsC;EACpC3mB,MAAAA,MAAM,CAAC2mB,KAAP,GAAe;EACbjP,QAAAA,IAAI,EAAE1X,MAAM,CAAC2mB,KADA;EAEblP,QAAAA,IAAI,EAAEzX,MAAM,CAAC2mB;EAFA,OAAf;EAID;;EAED,QAAI,OAAO3mB,MAAM,CAAC0mB,KAAd,KAAwB,QAA5B,EAAsC;EACpC1mB,MAAAA,MAAM,CAAC0mB,KAAP,GAAe1mB,MAAM,CAAC0mB,KAAP,CAAa1pB,QAAb,EAAf;EACD;;EAED,QAAI,OAAOgD,MAAM,CAACuqB,OAAd,KAA0B,QAA9B,EAAwC;EACtCvqB,MAAAA,MAAM,CAACuqB,OAAP,GAAiBvqB,MAAM,CAACuqB,OAAP,CAAevtB,QAAf,EAAjB;EACD;;EAED8C,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe,KAAKgJ,WAAL,CAAiB0F,WAAhC,CAAf;;EAEA,QAAI1O,MAAM,CAAC+mB,QAAX,EAAqB;EACnB/mB,MAAAA,MAAM,CAACymB,QAAP,GAAkBlB,YAAY,CAACvlB,MAAM,CAACymB,QAAR,EAAkBzmB,MAAM,CAACylB,SAAzB,EAAoCzlB,MAAM,CAAC0lB,UAA3C,CAA9B;EACD;;EAED,WAAO1lB,MAAP;EACD;;EAED2qB,EAAAA,kBAAkB,GAAG;EACnB,UAAM3qB,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAKA,MAAT,EAAiB;EACf,WAAK,MAAMwD,GAAX,IAAkB,KAAKxD,MAAvB,EAA+B;EAC7B,YAAI,KAAKgJ,WAAL,CAAiBmF,OAAjB,CAAyB3K,GAAzB,MAAkC,KAAKxD,MAAL,CAAYwD,GAAZ,CAAtC,EAAwD;EACtDxD,UAAAA,MAAM,CAACwD,GAAD,CAAN,GAAc,KAAKxD,MAAL,CAAYwD,GAAZ,CAAd;EACD;EACF;EACF;;EAED,WAAOxD,MAAP;EACD;;EAEDoqB,EAAAA,cAAc,GAAG;EACf,UAAMvB,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,UAAMiC,QAAQ,GAAG1C,GAAG,CAAC/qB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCopB,oBAAhC,CAAjB;;EACA,QAAIiF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACxlB,MAAT,GAAkB,CAA3C,EAA8C;EAC5CwlB,MAAAA,QAAQ,CAACjP,GAAT,CAAakP,KAAK,IAAIA,KAAK,CAACrtB,IAAN,EAAtB,EACGiC,OADH,CACWqrB,MAAM,IAAI5C,GAAG,CAACtnB,SAAJ,CAAc2C,MAAd,CAAqBunB,MAArB,CADrB;EAED;EACF;;EAEDX,EAAAA,4BAA4B,CAACvO,UAAD,EAAa;EACvC,UAAM;EAAEmP,MAAAA;EAAF,QAAYnP,UAAlB;;EAEA,QAAI,CAACmP,KAAL,EAAY;EACV;EACD;;EAED,SAAK7C,GAAL,GAAW6C,KAAK,CAAC1F,QAAN,CAAe2F,MAA1B;;EACA,SAAKvB,cAAL;;EACA,SAAKH,mBAAL,CAAyB,KAAKD,cAAL,CAAoB0B,KAAK,CAACjP,SAA1B,CAAzB;EACD,GA7nBiC;;;EAioBZ,SAAfvZ,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAAC2K,IAAD,IAAS,eAAejK,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI6d,OAAJ,CAAY,IAAZ,EAAkBlX,OAAlB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAnBM,CAAP;EAoBD;;EAtpBiC;EAypBpC;EACA;EACA;EACA;EACA;EACA;;;EAEA4C,kBAAkB,CAAC0G,MAAD,EAAOkf,OAAP,CAAlB;;EC/xBA;EACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;EACA;EACA;;EAEA,MAAMlf,MAAI,GAAG,SAAb;EACA,MAAMH,UAAQ,GAAG,YAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMkd,YAAY,GAAG,YAArB;EACA,MAAMC,kBAAkB,GAAG,IAAI7lB,MAAJ,CAAY,UAAS4lB,YAAa,MAAlC,EAAyC,GAAzC,CAA3B;EAEA,MAAMlY,SAAO,GAAG,EACd,GAAGqa,OAAO,CAACra,OADG;EAEdsO,EAAAA,SAAS,EAAE,OAFG;EAGdtQ,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHM;EAIdrE,EAAAA,OAAO,EAAE,OAJK;EAKdyiB,EAAAA,OAAO,EAAE,EALK;EAMd9D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEI,kCAFJ,GAGE,kCAHF,GAIA;EAVI,CAAhB;EAaA,MAAM/X,aAAW,GAAG,EAClB,GAAG8Z,OAAO,CAAC9Z,WADO;EAElB6b,EAAAA,OAAO,EAAE;EAFS,CAApB;EAKA,MAAMrrB,OAAK,GAAG;EACZooB,EAAAA,IAAI,EAAG,OAAM/d,WAAU,EADX;EAEZge,EAAAA,MAAM,EAAG,SAAQhe,WAAU,EAFf;EAGZie,EAAAA,IAAI,EAAG,OAAMje,WAAU,EAHX;EAIZke,EAAAA,KAAK,EAAG,QAAOle,WAAU,EAJb;EAKZme,EAAAA,QAAQ,EAAG,WAAUne,WAAU,EALnB;EAMZoe,EAAAA,KAAK,EAAG,QAAOpe,WAAU,EANb;EAOZqe,EAAAA,OAAO,EAAG,UAASre,WAAU,EAPjB;EAQZse,EAAAA,QAAQ,EAAG,WAAUte,WAAU,EARnB;EASZue,EAAAA,UAAU,EAAG,aAAYve,WAAU,EATvB;EAUZwe,EAAAA,UAAU,EAAG,aAAYxe,WAAU;EAVvB,CAAd;EAaA,MAAMO,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAM6hB,cAAc,GAAG,iBAAvB;EACA,MAAMC,gBAAgB,GAAG,eAAzB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,OAAN,SAAsBtD,OAAtB,CAA8B;EAC5B;EAEkB,aAAPra,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEc,aAAJ7E,IAAI,GAAG;EAChB,WAAOA,MAAP;EACD;;EAEkB,aAARH,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD;;EAEe,aAALjK,KAAK,GAAG;EACjB,WAAOA,OAAP;EACD;;EAEmB,aAATqK,SAAS,GAAG;EACrB,WAAOA,WAAP;EACD;;EAEqB,aAAXmF,WAAW,GAAG;EACvB,WAAOA,aAAP;EACD,GAzB2B;;;EA6B5B+a,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKY,QAAL,MAAmB,KAAK0B,WAAL,EAA1B;EACD;;EAEDjC,EAAAA,UAAU,GAAG;EACX,UAAMjB,GAAG,GAAG,KAAKS,aAAL,EAAZ,CADW;;EAIX,SAAKgB,iBAAL,CAAuBxd,cAAc,CAACK,OAAf,CAAuBye,cAAvB,EAAuC/C,GAAvC,CAAvB,EAAoE,KAAKwB,QAAL,EAApE;;EACA,QAAIE,OAAO,GAAG,KAAKwB,WAAL,EAAd;;EACA,QAAI,OAAOxB,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACttB,IAAR,CAAa,KAAKgM,QAAlB,CAAV;EACD;;EAED,SAAKqhB,iBAAL,CAAuBxd,cAAc,CAACK,OAAf,CAAuB0e,gBAAvB,EAAyChD,GAAzC,CAAvB,EAAsE0B,OAAtE;EAEA1B,IAAAA,GAAG,CAACtnB,SAAJ,CAAc2C,MAAd,CAAqB4F,iBAArB,EAAsCC,iBAAtC;EACD,GA9C2B;;;EAkD5BkgB,EAAAA,mBAAmB,CAACF,UAAD,EAAa;EAC9B,SAAKT,aAAL,GAAqB/nB,SAArB,CAA+BqS,GAA/B,CAAoC,GAAEyS,YAAa,IAAG,KAAKoE,gBAAL,CAAsBV,UAAtB,CAAkC,EAAxF;EACD;;EAEDgC,EAAAA,WAAW,GAAG;EACZ,WAAO,KAAK9iB,QAAL,CAAcnL,YAAd,CAA2B,iBAA3B,KAAiD,KAAKkC,MAAL,CAAYuqB,OAApE;EACD;;EAEDH,EAAAA,cAAc,GAAG;EACf,UAAMvB,GAAG,GAAG,KAAKS,aAAL,EAAZ;EACA,UAAMiC,QAAQ,GAAG1C,GAAG,CAAC/qB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCopB,kBAAhC,CAAjB;;EACA,QAAIiF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACxlB,MAAT,GAAkB,CAA3C,EAA8C;EAC5CwlB,MAAAA,QAAQ,CAACjP,GAAT,CAAakP,KAAK,IAAIA,KAAK,CAACrtB,IAAN,EAAtB,EACGiC,OADH,CACWqrB,MAAM,IAAI5C,GAAG,CAACtnB,SAAJ,CAAc2C,MAAd,CAAqBunB,MAArB,CADrB;EAED;EACF,GAjE2B;;;EAqEN,SAAfvoB,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAAC2K,IAAD,IAAS,eAAejK,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAImhB,OAAJ,CAAY,IAAZ,EAAkBxa,OAAlB,CAAP;EACApI,QAAAA,IAAI,CAAC3F,GAAL,CAAS,IAAT,EAAe4F,UAAf,EAAyBwB,IAAzB;EACD;;EAED,UAAI,OAAO3K,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KApBM,CAAP;EAqBD;;EA3F2B;EA8F9B;EACA;EACA;EACA;EACA;EACA;;;EAEA4C,kBAAkB,CAAC0G,MAAD,EAAOwiB,OAAP,CAAlB;;ECxKA;EACA;EACA;EACA;EACA;EACA;EAeA;EACA;EACA;EACA;EACA;;EAEA,MAAMxiB,MAAI,GAAG,WAAb;EACA,MAAMH,UAAQ,GAAG,cAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,cAAY,GAAG,WAArB;EAEA,MAAM2E,SAAO,GAAG;EACdhC,EAAAA,MAAM,EAAE,EADM;EAEd6f,EAAAA,MAAM,EAAE,MAFM;EAGdnmB,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,MAAM6I,aAAW,GAAG;EAClBvC,EAAAA,MAAM,EAAE,QADU;EAElB6f,EAAAA,MAAM,EAAE,QAFU;EAGlBnmB,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,MAAMomB,cAAc,GAAI,WAAU1iB,WAAU,EAA5C;EACA,MAAM2iB,YAAY,GAAI,SAAQ3iB,WAAU,EAAxC;EACA,MAAMmG,mBAAmB,GAAI,OAAMnG,WAAU,GAAEC,cAAa,EAA5D;EAEA,MAAM2iB,wBAAwB,GAAG,eAAjC;EACA,MAAMrhB,mBAAiB,GAAG,QAA1B;EAEA,MAAMshB,iBAAiB,GAAG,wBAA1B;EACA,MAAMC,yBAAuB,GAAG,mBAAhC;EACA,MAAMC,kBAAkB,GAAG,WAA3B;EACA,MAAMC,kBAAkB,GAAG,WAA3B;EACA,MAAMC,mBAAmB,GAAG,kBAA5B;EACA,MAAMC,mBAAiB,GAAG,WAA1B;EACA,MAAMC,0BAAwB,GAAG,kBAAjC;EAEA,MAAMC,aAAa,GAAG,QAAtB;EACA,MAAMC,eAAe,GAAG,UAAxB;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,SAAN,SAAwB9jB,aAAxB,CAAsC;EACpCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EACA,SAAKkvB,cAAL,GAAsB,KAAK7jB,QAAL,CAAc4K,OAAd,KAA0B,MAA1B,GAAmCnV,MAAnC,GAA4C,KAAKuK,QAAvE;EACA,SAAKqI,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAKqX,SAAL,GAAkB,GAAE,KAAK/F,OAAL,CAAazL,MAAO,IAAGymB,kBAAmB,KAAI,KAAKhb,OAAL,CAAazL,MAAO,IAAG2mB,mBAAoB,KAAI,KAAKlb,OAAL,CAAazL,MAAO,KAAIsmB,wBAAyB,EAAlK;EACA,SAAKY,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,CAArB;EAEA5nB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAKulB,cAArB,EAAqCZ,YAArC,EAAmD,MAAM,KAAKiB,QAAL,EAAzD;EAEA,SAAKC,OAAL;;EACA,SAAKD,QAAL;EACD,GAfmC;;;EAmBlB,aAAPhf,OAAO,GAAG;EACnB,WAAOA,SAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAzBmC;;;EA6BpCikB,EAAAA,OAAO,GAAG;EACR,UAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoBpuB,MAA5C,GACjBiuB,aADiB,GAEjBC,eAFF;EAIA,UAAMU,YAAY,GAAG,KAAKhc,OAAL,CAAa0a,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAK/b,OAAL,CAAa0a,MAFf;EAIA,UAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;EAIA,SAAKT,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;EAEA,UAAMC,OAAO,GAAG5gB,cAAc,CAACC,IAAf,CAAoB,KAAKsK,SAAzB,CAAhB;EAEAqW,IAAAA,OAAO,CAACpR,GAAR,CAAY1e,OAAO,IAAI;EACrB,YAAM+vB,cAAc,GAAGvvB,sBAAsB,CAACR,OAAD,CAA7C;EACA,YAAMiI,MAAM,GAAG8nB,cAAc,GAAG7gB,cAAc,CAACK,OAAf,CAAuBwgB,cAAvB,CAAH,GAA4C,IAAzE;;EAEA,UAAI9nB,MAAJ,EAAY;EACV,cAAM+nB,SAAS,GAAG/nB,MAAM,CAACwG,qBAAP,EAAlB;;EACA,YAAIuhB,SAAS,CAAClM,KAAV,IAAmBkM,SAAS,CAACC,MAAjC,EAAyC;EACvC,iBAAO,CACLriB,WAAW,CAAC8hB,YAAD,CAAX,CAA0BznB,MAA1B,EAAkCyG,GAAlC,GAAwCihB,UADnC,EAELI,cAFK,CAAP;EAID;EACF;;EAED,aAAO,IAAP;EACD,KAfD,EAgBG5hB,MAhBH,CAgBU+hB,IAAI,IAAIA,IAhBlB,EAiBGC,IAjBH,CAiBQ,CAACpK,CAAD,EAAIE,CAAJ,KAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAjB1B,EAkBGzjB,OAlBH,CAkBW0tB,IAAI,IAAI;EACf,WAAKf,QAAL,CAActf,IAAd,CAAmBqgB,IAAI,CAAC,CAAD,CAAvB;;EACA,WAAKd,QAAL,CAAcvf,IAAd,CAAmBqgB,IAAI,CAAC,CAAD,CAAvB;EACD,KArBH;EAsBD;;EAED1kB,EAAAA,OAAO,GAAG;EACR,UAAMA,OAAN;EACA9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKunB,cAAtB,EAAsCvjB,WAAtC;EAEA,SAAKujB,cAAL,GAAsB,IAAtB;EACA,SAAKxb,OAAL,GAAe,IAAf;EACA,SAAK+F,SAAL,GAAiB,IAAjB;EACA,SAAK0V,QAAL,GAAgB,IAAhB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACD,GAnFmC;;;EAuFpC3b,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,SADI;EAEP,UAAI,OAAOnO,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAFO,KAAT;;EAKA,QAAI,OAAOA,MAAM,CAAC6F,MAAd,KAAyB,QAAzB,IAAqC1G,SAAS,CAACa,MAAM,CAAC6F,MAAR,CAAlD,EAAmE;EACjE,UAAI;EAAEmR,QAAAA;EAAF,UAAShX,MAAM,CAAC6F,MAApB;;EACA,UAAI,CAACmR,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAG5Z,MAAM,CAACkM,MAAD,CAAX;EACAtJ,QAAAA,MAAM,CAAC6F,MAAP,CAAcmR,EAAd,GAAmBA,EAAnB;EACD;;EAEDhX,MAAAA,MAAM,CAAC6F,MAAP,GAAiB,IAAGmR,EAAG,EAAvB;EACD;;EAEDlX,IAAAA,eAAe,CAACwJ,MAAD,EAAOtJ,MAAP,EAAe0O,aAAf,CAAf;EAEA,WAAO1O,MAAP;EACD;;EAEDwtB,EAAAA,aAAa,GAAG;EACd,WAAO,KAAKV,cAAL,KAAwBpuB,MAAxB,GACL,KAAKouB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoBvgB,SAFtB;EAGD;;EAEDkhB,EAAAA,gBAAgB,GAAG;EACjB,WAAO,KAAKX,cAAL,CAAoBtM,YAApB,IAAoCljB,IAAI,CAAC2wB,GAAL,CACzCxwB,QAAQ,CAAC6E,IAAT,CAAcke,YAD2B,EAEzC/iB,QAAQ,CAACmE,eAAT,CAAyB4e,YAFgB,CAA3C;EAID;;EAED0N,EAAAA,gBAAgB,GAAG;EACjB,WAAO,KAAKpB,cAAL,KAAwBpuB,MAAxB,GACLA,MAAM,CAACyvB,WADF,GAEL,KAAKrB,cAAL,CAAoBzgB,qBAApB,GAA4CwhB,MAF9C;EAGD;;EAEDV,EAAAA,QAAQ,GAAG;EACT,UAAM5gB,SAAS,GAAG,KAAKihB,aAAL,KAAuB,KAAKlc,OAAL,CAAanF,MAAtD;;EACA,UAAMqU,YAAY,GAAG,KAAKiN,gBAAL,EAArB;;EACA,UAAMW,SAAS,GAAG,KAAK9c,OAAL,CAAanF,MAAb,GAAsBqU,YAAtB,GAAqC,KAAK0N,gBAAL,EAAvD;;EAEA,QAAI,KAAKhB,aAAL,KAAuB1M,YAA3B,EAAyC;EACvC,WAAK4M,OAAL;EACD;;EAED,QAAI7gB,SAAS,IAAI6hB,SAAjB,EAA4B;EAC1B,YAAMvoB,MAAM,GAAG,KAAKmnB,QAAL,CAAc,KAAKA,QAAL,CAAcjnB,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAKknB,aAAL,KAAuBpnB,MAA3B,EAAmC;EACjC,aAAKwoB,SAAL,CAAexoB,MAAf;EACD;;EAED;EACD;;EAED,QAAI,KAAKonB,aAAL,IAAsB1gB,SAAS,GAAG,KAAKwgB,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKqB,MAAL;;EACA;EACD;;EAED,SAAK,IAAIxoB,CAAC,GAAG,KAAKinB,QAAL,CAAchnB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;EACvC,YAAMyoB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAclnB,CAAd,CAAvB,IACnByG,SAAS,IAAI,KAAKwgB,QAAL,CAAcjnB,CAAd,CADM,KAElB,OAAO,KAAKinB,QAAL,CAAcjnB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IAA+CyG,SAAS,GAAG,KAAKwgB,QAAL,CAAcjnB,CAAC,GAAG,CAAlB,CAFzC,CAAvB;;EAIA,UAAIyoB,cAAJ,EAAoB;EAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAclnB,CAAd,CAAf;EACD;EACF;EACF;;EAEDuoB,EAAAA,SAAS,CAACxoB,MAAD,EAAS;EAChB,SAAKonB,aAAL,GAAqBpnB,MAArB;;EAEA,SAAKyoB,MAAL;;EAEA,UAAME,OAAO,GAAG,KAAKnX,SAAL,CAAenZ,KAAf,CAAqB,GAArB,EACboe,GADa,CACTze,QAAQ,IAAK,GAAEA,QAAS,oBAAmBgI,MAAO,MAAKhI,QAAS,UAASgI,MAAO,IADvE,CAAhB;;EAGA,UAAM4oB,IAAI,GAAG3hB,cAAc,CAACK,OAAf,CAAuBqhB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;EAEA,QAAID,IAAI,CAACltB,SAAL,CAAeC,QAAf,CAAwB2qB,wBAAxB,CAAJ,EAAuD;EACrDrf,MAAAA,cAAc,CAACK,OAAf,CAAuBuf,0BAAvB,EAAiD+B,IAAI,CAAClkB,OAAL,CAAakiB,mBAAb,CAAjD,EACGlrB,SADH,CACaqS,GADb,CACiB9I,mBADjB;EAGA2jB,MAAAA,IAAI,CAACltB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB;EACD,KALD,MAKO;EACL;EACA2jB,MAAAA,IAAI,CAACltB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB;EAEAgC,MAAAA,cAAc,CAACS,OAAf,CAAuBkhB,IAAvB,EAA6BpC,yBAA7B,EACGjsB,OADH,CACWuuB,SAAS,IAAI;EACpB;EACA;EACA7hB,QAAAA,cAAc,CAACY,IAAf,CAAoBihB,SAApB,EAAgC,GAAErC,kBAAmB,KAAIE,mBAAoB,EAA7E,EACGpsB,OADH,CACW0tB,IAAI,IAAIA,IAAI,CAACvsB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB,CADnB,EAHoB;;EAOpBgC,QAAAA,cAAc,CAACY,IAAf,CAAoBihB,SAApB,EAA+BpC,kBAA/B,EACGnsB,OADH,CACWwuB,OAAO,IAAI;EAClB9hB,UAAAA,cAAc,CAACM,QAAf,CAAwBwhB,OAAxB,EAAiCtC,kBAAjC,EACGlsB,OADH,CACW0tB,IAAI,IAAIA,IAAI,CAACvsB,SAAL,CAAeqS,GAAf,CAAmB9I,mBAAnB,CADnB;EAED,SAJH;EAKD,OAbH;EAcD;;EAEDxF,IAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKglB,cAA1B,EAA0Cb,cAA1C,EAA0D;EACxDzX,MAAAA,aAAa,EAAE3O;EADyC,KAA1D;EAGD;;EAEDyoB,EAAAA,MAAM,GAAG;EACPxhB,IAAAA,cAAc,CAACC,IAAf,CAAoB,KAAKsK,SAAzB,EACGtL,MADH,CACU8iB,IAAI,IAAIA,IAAI,CAACttB,SAAL,CAAeC,QAAf,CAAwBsJ,mBAAxB,CADlB,EAEG1K,OAFH,CAEWyuB,IAAI,IAAIA,IAAI,CAACttB,SAAL,CAAe2C,MAAf,CAAsB4G,mBAAtB,CAFnB;EAGD,GA/MmC;;;EAmNd,SAAf5H,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIkiB,SAAJ,CAAc,IAAd,EAAoBvb,OAApB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;EApOmC;EAuOtC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB7I,MAAhB,EAAwBgR,mBAAxB,EAA6C,MAAM;EACjD5C,EAAAA,cAAc,CAACC,IAAf,CAAoBqf,iBAApB,EACGhsB,OADH,CACW0uB,GAAG,IAAI,IAAIjC,SAAJ,CAAciC,GAAd,EAAmBtjB,WAAW,CAACI,iBAAZ,CAA8BkjB,GAA9B,CAAnB,CADlB;EAED,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEAlsB,kBAAkB,CAAC0G,MAAD,EAAOujB,SAAP,CAAlB;;EC5TA;EACA;EACA;EACA;EACA;EACA;EAeA;EACA;EACA;EACA;EACA;;EAEA,MAAMvjB,MAAI,GAAG,KAAb;EACA,MAAMH,UAAQ,GAAG,QAAjB;EACA,MAAMI,WAAS,GAAI,IAAGJ,UAAS,EAA/B;EACA,MAAMK,YAAY,GAAG,WAArB;EAEA,MAAM6M,YAAU,GAAI,OAAM9M,WAAU,EAApC;EACA,MAAM+M,cAAY,GAAI,SAAQ/M,WAAU,EAAxC;EACA,MAAM4M,YAAU,GAAI,OAAM5M,WAAU,EAApC;EACA,MAAM6M,aAAW,GAAI,QAAO7M,WAAU,EAAtC;EACA,MAAMK,oBAAoB,GAAI,QAAOL,WAAU,GAAEC,YAAa,EAA9D;EAEA,MAAMulB,wBAAwB,GAAG,eAAjC;EACA,MAAMjkB,iBAAiB,GAAG,QAA1B;EACA,MAAMhB,iBAAe,GAAG,MAAxB;EACA,MAAMC,iBAAe,GAAG,MAAxB;EAEA,MAAM0iB,iBAAiB,GAAG,WAA1B;EACA,MAAMJ,uBAAuB,GAAG,mBAAhC;EACA,MAAMnc,eAAe,GAAG,SAAxB;EACA,MAAM8e,kBAAkB,GAAG,uBAA3B;EACA,MAAMjkB,oBAAoB,GAAG,0EAA7B;EACA,MAAM2hB,wBAAwB,GAAG,kBAAjC;EACA,MAAMuC,8BAA8B,GAAG,iCAAvC;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,GAAN,SAAkBnmB,aAAlB,CAAgC;EAC9B;EAEmB,aAARI,QAAQ,GAAG;EACpB,WAAOA,UAAP;EACD,GAL6B;;;EAS9BuO,EAAAA,IAAI,GAAG;EACL,QAAK,KAAKzO,QAAL,CAAclI,UAAd,IACH,KAAKkI,QAAL,CAAclI,UAAd,CAAyB3B,QAAzB,KAAsCiC,IAAI,CAACC,YADxC,IAEH,KAAK2H,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCsJ,iBAAjC,CAFE,IAGF1J,UAAU,CAAC,KAAK6H,QAAN,CAHZ,EAG6B;EAC3B;EACD;;EAED,QAAI0E,QAAJ;EACA,UAAM9H,MAAM,GAAGvH,sBAAsB,CAAC,KAAK2K,QAAN,CAArC;;EACA,UAAMkmB,WAAW,GAAG,KAAKlmB,QAAL,CAAcsB,OAAd,CAAsB8hB,uBAAtB,CAApB;;EAEA,QAAI8C,WAAJ,EAAiB;EACf,YAAMC,YAAY,GAAGD,WAAW,CAAC7L,QAAZ,KAAyB,IAAzB,IAAiC6L,WAAW,CAAC7L,QAAZ,KAAyB,IAA1D,GAAiE0L,kBAAjE,GAAsF9e,eAA3G;EACAvC,MAAAA,QAAQ,GAAGb,cAAc,CAACC,IAAf,CAAoBqiB,YAApB,EAAkCD,WAAlC,CAAX;EACAxhB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAAC5H,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,UAAM+V,SAAS,GAAGnO,QAAQ,GACxBrI,YAAY,CAACwC,OAAb,CAAqB6F,QAArB,EAA+B0I,YAA/B,EAA2C;EACzC7B,MAAAA,aAAa,EAAE,KAAKvL;EADqB,KAA3C,CADwB,GAIxB,IAJF;EAMA,UAAMmS,SAAS,GAAG9V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,YAApC,EAAgD;EAChE3B,MAAAA,aAAa,EAAE7G;EADiD,KAAhD,CAAlB;;EAIA,QAAIyN,SAAS,CAACjT,gBAAV,IAA+B2T,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC3T,gBAAnE,EAAsF;EACpF;EACD;;EAED,SAAKkmB,SAAL,CAAe,KAAKplB,QAApB,EAA8BkmB,WAA9B;;EAEA,UAAM9W,QAAQ,GAAG,MAAM;EACrB/S,MAAAA,YAAY,CAACwC,OAAb,CAAqB6F,QAArB,EAA+B2I,cAA/B,EAA6C;EAC3C9B,QAAAA,aAAa,EAAE,KAAKvL;EADuB,OAA7C;EAGA3D,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,aAApC,EAAiD;EAC/C5B,QAAAA,aAAa,EAAE7G;EADgC,OAAjD;EAGD,KAPD;;EASA,QAAI9H,MAAJ,EAAY;EACV,WAAKwoB,SAAL,CAAexoB,MAAf,EAAuBA,MAAM,CAAC9E,UAA9B,EAA0CsX,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF,GAzD6B;;;EA6D9BgW,EAAAA,SAAS,CAACzwB,OAAD,EAAUia,SAAV,EAAqBrV,QAArB,EAA+B;EACtC,UAAM6sB,cAAc,GAAGxX,SAAS,KAAKA,SAAS,CAACyL,QAAV,KAAuB,IAAvB,IAA+BzL,SAAS,CAACyL,QAAV,KAAuB,IAA3D,CAAT,GACrBxW,cAAc,CAACC,IAAf,CAAoBiiB,kBAApB,EAAwCnX,SAAxC,CADqB,GAErB/K,cAAc,CAACM,QAAf,CAAwByK,SAAxB,EAAmC3H,eAAnC,CAFF;EAIA,UAAMof,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;EACA,UAAM5W,eAAe,GAAGjW,QAAQ,IAAK8sB,MAAM,IAAIA,MAAM,CAAC/tB,SAAP,CAAiBC,QAAjB,CAA0BsI,iBAA1B,CAA/C;;EAEA,UAAMuO,QAAQ,GAAG,MAAM,KAAKkX,mBAAL,CAAyB3xB,OAAzB,EAAkC0xB,MAAlC,EAA0C9sB,QAA1C,CAAvB;;EAEA,QAAI8sB,MAAM,IAAI7W,eAAd,EAA+B;EAC7B,YAAMja,kBAAkB,GAAGD,gCAAgC,CAAC+wB,MAAD,CAA3D;EACAA,MAAAA,MAAM,CAAC/tB,SAAP,CAAiB2C,MAAjB,CAAwB6F,iBAAxB;EAEAzE,MAAAA,YAAY,CAACkC,GAAb,CAAiB8nB,MAAjB,EAAyB,eAAzB,EAA0CjX,QAA1C;EACAhZ,MAAAA,oBAAoB,CAACiwB,MAAD,EAAS9wB,kBAAT,CAApB;EACD,KAND,MAMO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDkX,EAAAA,mBAAmB,CAAC3xB,OAAD,EAAU0xB,MAAV,EAAkB9sB,QAAlB,EAA4B;EAC7C,QAAI8sB,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAAC/tB,SAAP,CAAiB2C,MAAjB,CAAwB4G,iBAAxB;EAEA,YAAM0kB,aAAa,GAAG1iB,cAAc,CAACK,OAAf,CAAuB8hB,8BAAvB,EAAuDK,MAAM,CAACvuB,UAA9D,CAAtB;;EAEA,UAAIyuB,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAACjuB,SAAd,CAAwB2C,MAAxB,CAA+B4G,iBAA/B;EACD;;EAED,UAAIwkB,MAAM,CAACxxB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzCwxB,QAAAA,MAAM,CAACpkB,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAEDtN,IAAAA,OAAO,CAAC2D,SAAR,CAAkBqS,GAAlB,CAAsB9I,iBAAtB;;EACA,QAAIlN,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1CF,MAAAA,OAAO,CAACsN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAEDhJ,IAAAA,MAAM,CAACtE,OAAD,CAAN;;EAEA,QAAIA,OAAO,CAAC2D,SAAR,CAAkBC,QAAlB,CAA2BsI,iBAA3B,CAAJ,EAAiD;EAC/ClM,MAAAA,OAAO,CAAC2D,SAAR,CAAkBqS,GAAlB,CAAsB7J,iBAAtB;EACD;;EAED,QAAInM,OAAO,CAACmD,UAAR,IAAsBnD,OAAO,CAACmD,UAAR,CAAmBQ,SAAnB,CAA6BC,QAA7B,CAAsCutB,wBAAtC,CAA1B,EAA2F;EACzF,YAAMU,eAAe,GAAG7xB,OAAO,CAAC2M,OAAR,CAAgBkiB,iBAAhB,CAAxB;;EAEA,UAAIgD,eAAJ,EAAqB;EACnB3iB,QAAAA,cAAc,CAACC,IAAf,CAAoB2f,wBAApB,EACGtsB,OADH,CACWsvB,QAAQ,IAAIA,QAAQ,CAACnuB,SAAT,CAAmBqS,GAAnB,CAAuB9I,iBAAvB,CADvB;EAED;;EAEDlN,MAAAA,OAAO,CAACsN,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAI1I,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF,GA1H6B;;;EA8HR,SAAfU,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,YAAMC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,KAA4B,IAAI+lB,GAAJ,CAAQ,IAAR,CAAzC;;EAEA,UAAI,OAAOlvB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;EA1I6B;EA6IhC;EACA;EACA;EACA;EACA;;;EAEAsF,YAAY,CAACiC,EAAb,CAAgB9J,QAAhB,EAA0BmM,oBAA1B,EAAgDmB,oBAAhD,EAAsE,UAAU5F,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC0D,cAAN;EAEA,QAAM8B,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,UAAf,KAA4B,IAAI+lB,GAAJ,CAAQ,IAAR,CAAzC;EACAvkB,EAAAA,IAAI,CAAC+M,IAAL;EACD,CALD;EAOA;EACA;EACA;EACA;EACA;EACA;;EAEA9U,kBAAkB,CAAC0G,MAAD,EAAO4lB,GAAP,CAAlB;;ECzNA;EACA;EACA;EACA;EACA;EACA;EAcA;EACA;EACA;EACA;EACA;;EAEA,MAAM5lB,IAAI,GAAG,OAAb;EACA,MAAMH,QAAQ,GAAG,UAAjB;EACA,MAAMI,SAAS,GAAI,IAAGJ,QAAS,EAA/B;EAEA,MAAMuU,mBAAmB,GAAI,gBAAenU,SAAU,EAAtD;EACA,MAAM8M,UAAU,GAAI,OAAM9M,SAAU,EAApC;EACA,MAAM+M,YAAY,GAAI,SAAQ/M,SAAU,EAAxC;EACA,MAAM4M,UAAU,GAAI,OAAM5M,SAAU,EAApC;EACA,MAAM6M,WAAW,GAAI,QAAO7M,SAAU,EAAtC;EAEA,MAAMO,eAAe,GAAG,MAAxB;EACA,MAAM6lB,eAAe,GAAG,MAAxB;EACA,MAAM5lB,eAAe,GAAG,MAAxB;EACA,MAAM6lB,kBAAkB,GAAG,SAA3B;EAEA,MAAMlhB,WAAW,GAAG;EAClB8X,EAAAA,SAAS,EAAE,SADO;EAElBqJ,EAAAA,QAAQ,EAAE,SAFQ;EAGlBlJ,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,MAAMxY,OAAO,GAAG;EACdqY,EAAAA,SAAS,EAAE,IADG;EAEdqJ,EAAAA,QAAQ,EAAE,IAFI;EAGdlJ,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,MAAMvI,qBAAqB,GAAG,2BAA9B;EAEA;EACA;EACA;EACA;EACA;;EAEA,MAAM0R,KAAN,SAAoB/mB,aAApB,CAAkC;EAChCC,EAAAA,WAAW,CAACpL,OAAD,EAAUoC,MAAV,EAAkB;EAC3B,UAAMpC,OAAN;EAEA,SAAK0T,OAAL,GAAe,KAAKC,UAAL,CAAgBvR,MAAhB,CAAf;EACA,SAAK0oB,QAAL,GAAgB,IAAhB;;EACA,SAAKI,aAAL;EACD,GAP+B;;;EAWV,aAAXpa,WAAW,GAAG;EACvB,WAAOA,WAAP;EACD;;EAEiB,aAAPP,OAAO,GAAG;EACnB,WAAOA,OAAP;EACD;;EAEkB,aAARhF,QAAQ,GAAG;EACpB,WAAOA,QAAP;EACD,GArB+B;;;EAyBhCuO,EAAAA,IAAI,GAAG;EACL,UAAM0D,SAAS,GAAG9V,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCkN,UAApC,CAAlB;;EAEA,QAAIiF,SAAS,CAACjT,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAK4nB,aAAL;;EAEA,QAAI,KAAKze,OAAL,CAAakV,SAAjB,EAA4B;EAC1B,WAAKvd,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B9J,eAA5B;EACD;;EAED,UAAMuO,QAAQ,GAAG,MAAM;EACrB,WAAKpP,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B0rB,kBAA/B;;EACA,WAAK3mB,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B7J,eAA5B;;EAEAzE,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCmN,WAApC;;EAEA,UAAI,KAAK9E,OAAL,CAAaue,QAAjB,EAA2B;EACzB,aAAKnH,QAAL,GAAgB7oB,UAAU,CAAC,MAAM;EAC/B,eAAK4X,IAAL;EACD,SAFyB,EAEvB,KAAKnG,OAAL,CAAaqV,KAFU,CAA1B;EAGD;EACF,KAXD;;EAaA,SAAK1d,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+ByrB,eAA/B;;EACAztB,IAAAA,MAAM,CAAC,KAAK+G,QAAN,CAAN;;EACA,SAAKA,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4Bgc,kBAA5B;;EACA,QAAI,KAAKte,OAAL,CAAakV,SAAjB,EAA4B;EAC1B,YAAMhoB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EACAhZ,MAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDZ,EAAAA,IAAI,GAAG;EACL,QAAI,CAAC,KAAKxO,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,eAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,UAAM+R,SAAS,GAAGxW,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCoN,UAApC,CAAlB;;EAEA,QAAIyF,SAAS,CAAC3T,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMkQ,QAAQ,GAAG,MAAM;EACrB,WAAKpP,QAAL,CAAc1H,SAAd,CAAwBqS,GAAxB,CAA4B+b,eAA5B;;EACArqB,MAAAA,YAAY,CAACwC,OAAb,CAAqB,KAAKmB,QAA1B,EAAoCqN,YAApC;EACD,KAHD;;EAKA,SAAKrN,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,eAA/B;;EACA,QAAI,KAAKuH,OAAL,CAAakV,SAAjB,EAA4B;EAC1B,YAAMhoB,kBAAkB,GAAGD,gCAAgC,CAAC,KAAK0K,QAAN,CAA3D;EAEA3D,MAAAA,YAAY,CAACkC,GAAb,CAAiB,KAAKyB,QAAtB,EAAgC,eAAhC,EAAiDoP,QAAjD;EACAhZ,MAAAA,oBAAoB,CAAC,KAAK4J,QAAN,EAAgBzK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL6Z,MAAAA,QAAQ;EACT;EACF;;EAEDjP,EAAAA,OAAO,GAAG;EACR,SAAK2mB,aAAL;;EAEA,QAAI,KAAK9mB,QAAL,CAAc1H,SAAd,CAAwBC,QAAxB,CAAiCuI,eAAjC,CAAJ,EAAuD;EACrD,WAAKd,QAAL,CAAc1H,SAAd,CAAwB2C,MAAxB,CAA+B6F,eAA/B;EACD;;EAEDzE,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK0D,QAAtB,EAAgCyU,mBAAhC;EAEA,UAAMtU,OAAN;EACA,SAAKkI,OAAL,GAAe,IAAf;EACD,GAtG+B;;;EA0GhCC,EAAAA,UAAU,CAACvR,MAAD,EAAS;EACjBA,IAAAA,MAAM,GAAG,EACP,GAAGmO,OADI;EAEP,SAAG3C,WAAW,CAACI,iBAAZ,CAA8B,KAAK3C,QAAnC,CAFI;EAGP,UAAI,OAAOjJ,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD;EAHO,KAAT;EAMAF,IAAAA,eAAe,CAACwJ,IAAD,EAAOtJ,MAAP,EAAe,KAAKgJ,WAAL,CAAiB0F,WAAhC,CAAf;EAEA,WAAO1O,MAAP;EACD;;EAED8oB,EAAAA,aAAa,GAAG;EACdxjB,IAAAA,YAAY,CAACiC,EAAb,CAAgB,KAAK0B,QAArB,EAA+ByU,mBAA/B,EAAoDU,qBAApD,EAA2E,MAAM,KAAK3G,IAAL,EAAjF;EACD;;EAEDsY,EAAAA,aAAa,GAAG;EACdtc,IAAAA,YAAY,CAAC,KAAKiV,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;EACD,GA7H+B;;;EAiIV,SAAfxlB,eAAe,CAAClD,MAAD,EAAS;EAC7B,WAAO,KAAK0K,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGzB,IAAI,CAACtF,GAAL,CAAS,IAAT,EAAeuF,QAAf,CAAX;;EACA,YAAMmI,OAAO,GAAG,OAAOtR,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAAC2K,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAImlB,KAAJ,CAAU,IAAV,EAAgBxe,OAAhB,CAAP;EACD;;EAED,UAAI,OAAOtR,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO2K,IAAI,CAAC3K,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIW,SAAJ,CAAe,oBAAmBX,MAAO,GAAzC,CAAN;EACD;;EAED2K,QAAAA,IAAI,CAAC3K,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAfM,CAAP;EAgBD;;EAlJ+B;EAqJlC;EACA;EACA;EACA;EACA;EACA;;;EAEA4C,kBAAkB,CAAC0G,IAAD,EAAOwmB,KAAP,CAAlB;;ECxNA;EACA;EACA;EACA;EACA;EACA;AAeA,kBAAe;EACb9lB,EAAAA,KADa;EAEbgB,EAAAA,MAFa;EAGb6F,EAAAA,QAHa;EAIbgG,EAAAA,QAJa;EAKb8D,EAAAA,QALa;EAMb4D,EAAAA,KANa;EAOb8D,EAAAA,SAPa;EAQbyJ,EAAAA,OARa;EASbe,EAAAA,SATa;EAUbqC,EAAAA,GAVa;EAWbY,EAAAA,KAXa;EAYbtH,EAAAA;EAZa,CAAf;;;;;;;;"}
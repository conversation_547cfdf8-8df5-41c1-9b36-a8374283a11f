<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WALIX :: <?= $title ?></title>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/highlight/styles/github-gist.css" rel="stylesheet">
    <link href="<?= _assets() ?>/css/main.min.css" rel="stylesheet">
    <link href="<?= _assets() ?>/css/custom.css" rel="stylesheet">
</head>

<body>
    <div class="app align-content-stretch d-flex flex-wrap">
        <?php require_once(VIEWPATH . '/include_head.php') ?>

        <div class="app-container">
            <div class="app-header">
                <nav class="navbar navbar-light navbar-expand-lg">
                    <div class="container-fluid">
                        <div class="navbar-nav" id="navbarNav">
                            <ul class="navbar-nav">
                                <li class="nav-item">
                                    <a class="nav-link hide-sidebar-toggle-button" href="#"><i class="material-icons">first_page</i></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </nav>
            </div>
            <div class="app-content">
                <div class="content-wrapper">
                    <div class="container">
                        <div class="row">
                            <div class="col">
                                <div class="page-description p-0">
                                    <h4><?= $title ?></h4>
                                </div>
                            </div>
                        </div>
                        <?= _alert() ?>
                        
                        <div class="card">
                            <div class="card-body">
                                <h5>Webhook Configuration</h5>
                                <p>To receive incoming messages and status updates, you need to configure a webhook URL in your device settings or using the API.</p>
                                
                                <div class="alert alert-info">
                                    <strong>Note:</strong> Your webhook URL must be publicly accessible from the internet.
                                </div>
                                
                                <h6 class="mt-4">Configure Webhook via API</h6>
                                <div class="example-container">
                                    <div class="example-code">
                                        <pre><code class="php"><?php
echo htmlspecialchars('<?php
// Configure webhook URL

// Konfigurasi
$api_url = \'https://domain-anda.com/api/webhook\';
$api_key = \'' . $user->api_key . '\';
$webhook_url = \'https://your-domain.com/webhook-handler.php\';
$events = \'all\'; // Options: message, status, all

// Metode GET
$url = $api_url . \'?apikey=\' . $api_key . \'&url=\' . urlencode($webhook_url) . \'&events=\' . $events;

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => \'\',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => \'GET\'
]);

$response = curl_exec($curl);
curl_close($curl);

// Proses response
$result = json_decode($response, true);
if ($result[\'status\']) {
    echo "Webhook berhasil dikonfigurasi!";
} else {
    echo "Gagal mengkonfigurasi webhook: " . $result[\'msg\'];
}');
?></code></pre>
                                    </div>
                                </div>
                                
                                <h5 class="mt-4">Basic Webhook Handler</h5>
                                <p>Create a file (e.g., webhook-handler.php) that will receive webhook notifications:</p>
                                <div class="example-container">
                                    <div class="example-code">
                                        <pre><code class="php"><?php
echo htmlspecialchars('<?php
// webhook-handler.php - File yang akan menerima notifikasi dari WhatsApp Gateway

// Terima data JSON dari webhook
$input = file_get_contents(\'php://input\');
$data = json_decode($input, true);

// Log data untuk debugging
file_put_contents(\'webhook_log.txt\', date(\'Y-m-d H:i:s\') . " - " . $input . PHP_EOL, FILE_APPEND);

// Pastikan ini adalah event pesan
if (isset($data[\'event\']) && $data[\'event\'] === \'message\') {
    $messageData = $data[\'data\'];
    
    // Ekstrak informasi pesan
    $messageId = $messageData[\'id\'];
    $sender = $messageData[\'from\'];
    $message = $messageData[\'message\'];
    $timestamp = $messageData[\'timestamp\'];
    
    // Proses pesan sesuai kebutuhan aplikasi Anda
    // Contoh: Simpan ke database
    $db = new PDO(\'mysql:host=localhost;dbname=your_database\', \'username\', \'password\');
    $stmt = $db->prepare("INSERT INTO incoming_messages (message_id, sender, message, received_at) VALUES (?, ?, ?, ?)");
    $stmt->execute([$messageId, $sender, $message, date(\'Y-m-d H:i:s\')]);
    
    // Berikan respons sukses
    http_response_code(200);
    echo json_encode([\'status\' => \'success\']);
} else if (isset($data[\'event\']) && $data[\'event\'] === \'status\') {
    // Proses update status pesan
    $statusData = $data[\'data\'];
    
    // Ekstrak informasi status
    $messageId = $statusData[\'id\'];
    $status = $statusData[\'status\'];
    $timestamp = $statusData[\'timestamp\'];
    
    // Proses update status
    // Contoh: Update status di database
    $db = new PDO(\'mysql:host=localhost;dbname=your_database\', \'username\', \'password\');
    $stmt = $db->prepare("UPDATE messages SET status = ?, updated_at = ? WHERE message_id = ?");
    $stmt->execute([$status, date(\'Y-m-d H:i:s\'), $messageId]);
    
    // Berikan respons sukses
    http_response_code(200);
    echo json_encode([\'status\' => \'success\']);
} else {
    // Respons untuk event lain atau format tidak valid
    http_response_code(400);
    echo json_encode([\'status\' => \'error\', \'message\' => \'Invalid webhook data\']);
}');
?></code></pre>
                                    </div>
                                </div>
                                
                                <h5 class="mt-4">Auto-Reply Bot Example</h5>
                                <p>Create a simple auto-reply bot that responds to specific keywords:</p>
                                <div class="example-container">
                                    <div class="example-code">
                                        <pre><code class="php"><?php
echo htmlspecialchars('<?php
// auto-reply-bot.php - Bot sederhana untuk membalas pesan otomatis

header(\'content-type: application/json\');
$data = json_decode(file_get_contents(\'php://input\'), true);

// Log data untuk debugging
file_put_contents(\'whatsapp.txt\', \'[\' . date(\'Y-m-d H:i:s\') . "]\n" . json_encode($data) . "\n\n", FILE_APPEND);

// Pastikan ini adalah event pesan
if (isset($data[\'event\']) && $data[\'event\'] === \'message\') {
    $message = $data[\'data\'][\'message\']; // Pesan yang masuk
    $from = $data[\'data\'][\'from\']; // Nomor pengirim pesan

    // Logika bot sederhana
    if (strtolower($message) == \'hai\' || strtolower($message) == \'halo\') {
        $result = [
            \'mode\' => \'chat\', // mode chat = normal chat
            \'pesan\' => \'Hai! Terima kasih telah menghubungi kami. Ada yang bisa kami bantu?\'
        ];
    } else if (strtolower($message) == \'info\' || strtolower($message) == \'informasi\') {
        $result = [
            \'mode\' => \'reply\', // mode reply = reply pesan
            \'pesan\' => \'Berikut informasi layanan kami: ...\' 
        ];
    } else if (strtolower($message) == \'gambar\' || strtolower($message) == \'foto\') {
        $result = [
            \'mode\' => \'picture\', // mode picture = kirim pesan dengan gambar
            \'data\' => [
                \'caption\' => \'*Ini adalah gambar contoh*\',
                \'url\' => \'https://example.com/path/to/image.jpg\'
            ]
        ];
    } else if (strtolower($message) == \'menu\' || strtolower($message) == \'bantuan\') {
        $result = [
            \'mode\' => \'button\', // mode button = kirim pesan dengan tombol
            \'data\' => [
                \'message\' => \'*Menu Layanan*\nSilakan pilih layanan yang Anda butuhkan:\',
                \'footer\' => \'Pilih salah satu opsi di bawah\',
                \'button1\' => \'Info Produk\',
                \'button2\' => \'Hubungi CS\'
            ]
        ];
    } else {
        $result = [
            \'mode\' => \'chat\',
            \'pesan\' => \'Maaf, saya tidak mengerti pesan Anda. Ketik "menu" untuk melihat pilihan yang tersedia.\'
        ];
    }

    // Kirim respons
    echo json_encode($result);
} else {
    // Respons untuk event lain
    echo json_encode([\'status\' => \'error\', \'message\' => \'Invalid webhook data\']);
}');
?></code></pre>
                                    </div>
                                </div>
                                
                                <h5 class="mt-4">Webhook Payload Examples</h5>
                                <p>Below are examples of webhook payloads you will receive:</p>
                                
                                <h6>Incoming Message Payload</h6>
                                <div class="example-container">
                                    <div class="example-code">
                                        <pre><code class="json">{
    "event": "message",
    "data": {
        "id": "ABCDEF123456",
        "from": "628xxxxxxxxx",
        "to": "628xxxxxxxxx",
        "message": "Hello, this is a test message",
        "timestamp": 1625472000,
        "type": "text"
    }
}</code></pre>
                                    </div>
                                </div>
                                
                                <h6>Message Status Update Payload</h6>
                                <div class="example-container">
                                    <div class="example-code">
                                        <pre><code class="json">{
    "event": "status",
    "data": {
        "id": "ABCDEF123456",
        "status": "delivered",
        "timestamp": 1625472030
    }
}</code></pre>
                                    </div>
                                </div>
                                
                                <h6>Media Message Payload</h6>
                                <div class="example-container">
                                    <div class="example-code">
                                        <pre><code class="json">{
    "event": "message",
    "data": {
        "id": "ABCDEF123456",
        "from": "628xxxxxxxxx",
        "to": "628xxxxxxxxx",
        "message": "Check this image",
        "media_url": "https://domain-anda.com/storage/media/image123.jpg",
        "timestamp": 1625472000,
        "type": "image"
    }
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Javascripts -->
    <script src="<?= _assets() ?>/plugins/jquery/jquery-3.5.1.min.js"></script>
    <script src="<?= _assets() ?>/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="<?= _assets() ?>/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
    <script src="<?= _assets() ?>/plugins/pace/pace.min.js"></script>
    <script src="<?= _assets() ?>/plugins/highlight/highlight.pack.js"></script>
    <script src="<?= _assets() ?>/js/main.min.js"></script>
    <script src="<?= _assets() ?>/js/custom.js"></script>
    <script>
        $(document).ready(function() {
            $('pre code').each(function(i, block) {
                hljs.highlightBlock(block);
            });
        });
    </script>
</body>

</html>


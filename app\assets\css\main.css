/*------------------------------------------------------------
[Table of contents]

1. Global Styles
2. CSS Utilities
3. Layout
4. UI Elements
5. Components
6. Charts
7. Plugins
8. Apps
9. Responsive

------------------------------------------------------------*/

/*------------------------------------------------------------
[1. <PERSON> Styles]
------------------------------------------------------------*/

html, body {
    font-size: 14px;
    width: 100%;
    height: 100%;
    min-height: 100vh;
}

html {
    overflow-x: hidden;
}

body {
    background-color: #E7ECF8;
    font-weight: normal;
    color: #24292e;
    line-height: 1.6;
    -webkit-transition: background .2s ease-in-out;
    -moz-transition: background .2s ease-in-out;
    -o-transition: background .2s ease-in-out;
    transition: background .2s ease-in-out;
}

a {
    color: #3599fd;
}

a:hover {
    color: #2482e0;
}

p {
    line-height: 1.6;
}

small {
    font-size: 80%;
    font-weight: 400;
}

menu {
    padding-left: 0px;
}

body, span, text, a {
    font-family: 'Poppins', sans-serif;
}

a:hover, a:active, a:focus {
    outline: none!important;
    text-decoration: none!important;
}

b, strong, .bold {
    font-weight: 600;
}

/*------------------------------------------------------------
[1. Global Styles]
------------------------------------------------------------*/

/* Overflow */

.overflow-hidden {
    overflow: hidden;
}

.overflow-x-hidden {
    overflow-x: hidden
}

.overflow-y-hidden {
    overflow-y: hidden;
}

/* Headings */

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
}

/* Divider */

.divider {
    display: block;
    content: '';
    width: 100%;
    height: 1px;
    background: #e8e8e8;
    margin: 30px 0;
}

/* Margins & Paddings */

.no-s {
    margin: 0px!important;
    padding: 0px!important;
}

.no-m {
    margin: 0!important;
}

.no-p {
    padding: 0!important;
}

.m {
    margin: 15px;
}

.m-t-xxs {
    margin-top: 5px;
}

.m-r-xxs {
    margin-right: 5px;
}

.m-b-xxs {
    margin-bottom: 5px;
}

.m-l-xxs {
    margin-left: 5px;
}

.m-t-xs {
    margin-top: 10px;
}

.m-r-xs {
    margin-right: 10px;
}

.m-b-xs {
    margin-bottom: 10px;
}

.m-l-xs {
    margin-left: 10px;
}

.m-t-sm {
    margin-top: 15px;
}

.m-r-sm {
    margin-right: 15px;
}

.m-b-sm {
    margin-bottom: 15px;
}

.m-l-sm {
    margin-left: 15px;
}

.m-t-md {
    margin-top: 20px;
}

.m-r-md {
    margin-right: 20px;
}

.m-b-md {
    margin-bottom: 20px;
}

.m-l-md {
    margin-left: 20px;
}

.m-t-lg {
    margin-top: 25px;
}

.m-r-lg {
    margin-right: 25px;
}

.m-b-lg {
    margin-bottom: 25px;
}

.m-l-lg {
    margin-left: 25px;
}

.m-t-xxl {
    margin-top: 30px;
}

.m-r-xxl {
    margin-right: 30px;
}

.m-b-xxl {
    margin-bottom: 30px;
}

.m-l-xxl {
    margin-left: 30px;
}

.m-t-n-xxs {
    margin-top: -5px;
}

.m-r-n-xxs {
    margin-right: -5px;
}

.m-b-n-xxs {
    margin-bottom: -5px;
}

.m-l-n-xxs {
    margin-left: -5px;
}

.m-t-n-xs {
    margin-top: -10px;
}

.m-r-n-xs {
    margin-right: -10px;
}

.m-b-n-xs {
    margin-bottom: -10px;
}

.m-l-n-xs {
    margin-left: -10px;
}

.m-t-n-sm {
    margin-top: -15px;
}

.m-r-n-sm {
    margin-right: -15px;
}

.m-b-n-sm {
    margin-bottom: -15px;
}

.m-l-n-sm {
    margin-left: -15px;
}

.m-t-n-md {
    margin-top: -20px;
}

.m-r-n-md {
    margin-right: -20px;
}

.m-b-n-md {
    margin-bottom: -20px;
}

.m-l-n-md {
    margin-left: -20px;
}

.m-t-n-lg {
    margin-top: -25px;
}

.m-r-n-lg {
    margin-right: -25px;
}

.m-b-n-lg {
    margin-bottom: -25px;
}

.m-l-n-lg {
    margin-left: -25px;
}

.m-t-n-xxl {
    margin-top: -30px;
}

.m-r-n-xxl {
    margin-right: -30px;
}

.m-b-n-xxl {
    margin-bottom: -30px;
}

.m-l-n-xxl {
    margin-left: -30px;
}

.p {
    padding: 15px;
}

.p-h-xxs {
    padding-left: 5px;
    padding-right: 5px;
}

.p-h-xs {
    padding-left: 10px;
    padding-right: 10px;
}

.p-h-sm {
    padding-left: 15px;
    padding-right: 15px;
}

.no-p-h {
    padding-left: 0;
    padding-right: 0;
}

.no-p-v {
    padding-top: 0;
    padding-bottom: 0;
}

.p-h-md {
    padding-left: 20px;
    padding-right: 20px;
}

.p-h-lg {
    padding-left: 25px;
    padding-right: 25px;
}

.p-h-xxl {
    padding-left: 30px;
    padding-right: 30px;
}

.p-v-xxs {
    padding-top: 5px;
    padding-bottom: 5px;
}

.p-v-xs {
    padding-top: 10px;
    padding-bottom: 10px;
}

.p-v-sm {
    padding-top: 15px;
    padding-bottom: 15px;
}

.p-v-md {
    padding-top: 20px;
    padding-bottom: 20px;
}

.p-v-lg {
    padding-top: 25px;
    padding-bottom: 25px;
}

.p-v-xxl {
    padding-top: 30px;
    padding-bottom: 30px;
}

/* Width & Height */

.full-height {
    height: 100%!important;
}

.full-width {
    width: 100%!important;
}

.w-xxs {
    width: 50px;
}

.w-xs {
    width: 90px;
}

.w-sm {
    width: 140px;
}

.w {
    width: 200px;
}

.w-md {
    width: 240px;
}

.w-lg {
    width: 280px;
}

.w-xl {
    width: 330px;
}

.w-xxl {
    width: 370px;
}

.w-auto {
    width: auto;
}

.h-auto {
    height: auto;
}

/* Borders */

.no-b {
    border: 0!important;
}

.b {
    border: 1px solid #E6E6E6;
}

.b-t {
    border-top: 1px solid #E6E6E6;
}

.b-r {
    border-right: 1px solid #E6E6E6;
}

.b-b {
    border-bottom: 1px solid #E6E6E6;
}

.b-l {
    border-left: 1px solid #E6E6E6;
}

.b-default {
    border-color: #E6E6E6;
}

.b-primary {
    border-color: #037AFB;
}

.b-info {
    border-color: #5780F7;
}

.b-success {
    border-color: #4bad48;
}

.b-warning {
    border-color: #FACC35;
}

.b-danger {
    border-color: #EB463F;
}

.b-2 {
    border-width: 2px;
}

.b-3 {
    border-width: 3px;
}

.b-4 {
    border-width: 4px;
}

.b-n-t {
    border-top: 0;
}

.b-n-r {
    border-right: 0;
}

.b-n-b {
    border-bottom: 0;
}

.b-n-l {
    border-left: 0;
}

/* Radius */

.rounded {
    border-radius: 5px!important;
}

.no-r {
    border-radius: 0!important;
}

.r {
    border-radius: 2px;
}

.r-t-l {
    border-top-left-radius: 2px;
}

.r-t-r {
    border-top-right-radius: 2px;
}

.r-b-l {
    border-bottom-left-radius: 2px;
}

.r-b-r {
    border-bottom-right-radius: 2px;
}

.no-r-t-l {
    border-top-left-radius: 0!important;
}

.no-r-t-r {
    border-top-right-radius: 0!important;
}

.no-r-b-l {
    border-bottom-left-radius: 0!important;
}

.no-r-b-r {
    border-bottom-right-radius: 0!important;
}

/* Backgrounds */

.bg-primary {
    background-color: #2269F4!important;
}

.bg-secondary {
    background-color: #FCEACE!important;
}

.bg-success {
    background-color: #4bad48!important;
}

.bg-danger {
    background-color: #FF4857!important;
}

.bg-warning {
    background-color: #FF9500!important;
}

.bg-info {
    background-color: #61ACFC!important;
}

.bg-light {
    background-color: #F4F7FA!important;
}

.bg-dark {
    background-color: #3F485B!important;
}

/* Text */

.text-muted {
    color: #8e98a5!important;
}

.text-primary {
    color: #2269F4!important;
}

.text-secondary {
    color: #F4F4F5!important;
}

.text-success {
    color: #4bad48!important;
}

.text-danger {
    color: #FF4857!important;
}

.text-warning {
    color: #FF9500!important;
}

.text-info {
    color: #61ACFC!important;
}

.text-light {
    color: #F4F7FA!important;
}

.text-dark {
    color: #3F485B!important;
}

.blockquote-footer {
    margin-top: -12px;
    margin-bottom: 12px;
    font-size: 15px;
    color: #8e98a5;
}

/*------------------------------------------------------------
[3. Layout]
------------------------------------------------------------*/

/* App */

.app {
    height: 100%;
}

/* App Container */

.app-container {
    margin-left: 280px;
    width: calc(100vw - 280px);
    padding: 30px;
    position: relative;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

/* App Header */

.app-header {
    position: fixed;
    width: calc(100% - 340px);
    right: 30px;
    z-index: 1001;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-header::before {
    content: '';
    display: block;
    width: 100%;
    height: 131px;
    background: linear-gradient(180deg, rgba(231, 236, 248, 0.8) 0%, rgba(231, 236, 248, 1) 40%, rgba(231, 236, 248, 1) 78%, rgba(231, 236, 248, 0) 100%);
    position: fixed;
    top: 0;
    left: 0;
}

.full-width-header .app-header::before {
    height: 100px;
}

.app-header .navbar {
    height: 70px;
    padding: 0 15px;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.app-header .navbar .navbar-nav > li {
    position: relative;
}

.app-header .navbar .navbar-nav>li>a {
    padding-left: 10px;
    padding-right: 10px;
    margin-right: 5px;
    line-height: 30px;
}

.app-header .navbar .navbar-nav>li:last-child>a {
    margin-right: 0;
}

.app-header .navbar .navbar-nav>li>a>i {
    vertical-align: middle;
}

.app-header .navbar .navbar-nav>li>a {
    position: relative;
}

.app-header .navbar .navbar-nav>li>a.active {
    color: #293442;
}

.app-header .navbar .navbar-nav>li>a.active::after {
    content: '';
    width: 5px;
    height: 5px;
    display: block;
    position: absolute;
    left: 50%;
    bottom: 0px;
    transform: translateX(-2.5px);
    border-radius: 2.5px;
    background: #2269F5;
}

.app-header .nav-notifications-toggle {
    padding: 6.5px 15px;
    line-height: 22px!important;
    border-radius: 17.5px;
    min-width: 35px;
    text-align: center;
    font-weight: bold;
    margin: 4.5px 10px!important;
    background: #E3E3FF;
    color: #696AFF!important;
}

.app-header .notifications-dropdown {
    width: 350px;
    max-height: 400px;
}

.app-header .notifications-dropdown .notifications-dropdown-list {
    overflow-y: auto;
    max-height: 350px;
    margin: 0 -10px;
}

.app-header .notifications-dropdown a {
    text-decoration: none;
}

.app-header .notifications-dropdown-item {
    padding: 10px 20px;
    display: flex;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-header .notifications-dropdown-item:hover {
    background-color: #F4F7FA;
}

.app-header .notifications-dropdown .notifications-dropdown-item-image {
    float: left;
    margin-right: 10px;
    margin-top: 3px;
    border-radius: 50%;
}

.app-header .notifications-dropdown .notifications-dropdown-item-image span {
    width: 40px;
    height: 40px;
    display: block;
    line-height: 40px;
    text-align: center;
    border-radius: 100%;
    display: inline-block;
}

.app-header .notifications-dropdown .notifications-dropdown-item-image span i {
    line-height: 40px;
    font-size: 22px;
}

.app-header .notifications-dropdown .notifications-dropdown-item-image img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.app-header .notifications-dropdown .notifications-dropdown-item-text {
    flex: 1;
    padding: 0 5px;
    display: inline-block;
    color: #5A6C88;
    font-weight: 400;
    overflow: hidden;
}

.app-header .notifications-dropdown .notifications-dropdown-item-text .bold-notifications-text {
    font-weight: 500;
}

.app-header .notifications-dropdown .notifications-dropdown-item-text p {
    margin: 0;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.app-header .notifications-dropdown .notifications-dropdown-item-text small {
    opacity: 0.7;
    color: #919ba7;
}

.app-header .language-dropdown-toggle {
    margin: 6px 5px!important;
    padding: 0;
}

.app-header .language-dropdown-toggle img {
    height: 30px;
    width: 30px;
}

.app-header .language-dropdown {
    min-width: 150px;
}

.app-header .language-dropdown a img {
    width: 25px;
    height: 25px;
    margin-right: 15px;
}

/* App Sidebar */

.app-sidebar {
    width: 280px;
    background-color: #fff;
    height: 100vh;
    position: fixed;
    padding: 0;
    z-index: 1003;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-sidebar .logo {
    overflow: hidden;
    padding: 45px 28px;
    position: relative;
    background-color: #f5f7fa;
    -webkit-transition: opacity .2s ease-in-out;
    -moz-transition: opacity .2s ease-in-out;
    -o-transition: opacity .2s ease-in-out;
    transition: opacity .2s ease-in-out;
}

.app-sidebar .logo-icon .logo-text {
    color: #40475c;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-sidebar .logo .logo-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    background: url(../images/neptune.png) no-repeat;
    background-position: center left;
    background-size: 40px;
    display: block;
    text-decoration: none;
    font-weight: 700;
    border-radius: 30px;
    float: left;
    padding-left: 45px;
    line-height: 40px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-sidebar .logo .logo-icon:hover {
    padding-left: 55px;
}

.app-sidebar .logo .logo-icon:hover .logo-text {
    visibility: visible;
    opacity: 1;
}

.app-sidebar .logo .logo-icon:hover+.sidebar-user-switcher {
    margin-right: -10px;
    visibility: hidden;
    opacity: 0;
}

.app-sidebar .logo .sidebar-user-switcher {
    float: right;
    position: relative;
    opacity: 1;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-sidebar .logo .sidebar-user-switcher a {
    display: block;
    overflow: hidden;
}

.app-sidebar .logo .sidebar-user-switcher img {
    float: right;
    width: 40px;
    border-radius: 5px;
    margin-left: 10px;
}

.app-sidebar .logo .sidebar-user-switcher .user-info-text {
    float: right;
    text-align: right;
    color: #333;
    line-height: 20px;
}

.app-sidebar .logo .sidebar-user-switcher .user-info-text .user-state-info {
    opacity: .6;
    font-size: 13px;
}

.app-sidebar .logo .sidebar-user-switcher.user-activity-online .activity-indicator {
    display: block;
    position: absolute;
    width: 14px;
    height: 14px;
    background: #4bad48;
    border-radius: 7px;
    border: 2px solid #f5f7fa;
    bottom: -4px;
    right: -4px;
}

/* Small logo */

.app-sidebar .logo.logo-sm {
    padding: 15px 33px;
    background: #fff;
}

.app-sidebar .logo.logo-sm a {
    text-decoration: none;
    font-size: 20px;
    color: #293442;
    font-weight: bold;
    line-height: 40px;
}

/* Menu */

.app-menu {
    height: calc(100% - 131px)!important;
    padding-top: 15px;
    padding-bottom: 15px;
    position: relative;
    width: 100%;
}

.app-menu ul {
    margin: 0;
    padding: 0;
}

.app-menu ul li {
    list-style-type: none;
}

.app-menu>ul>li ul {
    margin: 10px 35px 10px 30px;
    padding: 20px 0;
    border-radius: 10px;
    background: #f5f7fa;
}

.app-menu>ul>li>a {
    text-decoration: none;
    color: #40475c;
    font-size: 14px;
    margin: 3px 15px;
    padding: 10px 15px;
    display: block;
    border-radius: 3px;
    line-height: 20px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-menu>ul>li>a:hover, .app-menu>ul>li.open>a, .app-menu>ul>li.active-page>a {
    color: #2269F5;
}

.app-menu>ul>li>a:hover {
    padding-left: 20px;
}

.app-menu>ul>li>a:hover>i:not(.has-sub-menu).material-icons-two-tone, .app-menu>ul>li.open>a>i:not(.has-sub-menu).material-icons-two-tone, .app-menu>ul>li.active-page>a>i:not(.has-sub-menu).material-icons-two-tone {
    filter: invert(34%) sepia(23%) saturate(3309%) hue-rotate(195deg) brightness(109%) contrast(115%);
}

.app-menu>ul>li>a>i:not(.has-sub-menu) {
    margin-right: 13px;
    vertical-align: top;
    line-height: 20px;
    filter: invert(41%) sepia(17%) saturate(674%) hue-rotate(182deg) brightness(95%) contrast(89%);
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.accordion-menu li>a>i.has-sub-menu {
    float: right;
    margin-right: 0;
    margin-top: -2px;
    color: #6d7b91;
    font-size: 19px;
    line-height: 24px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.accordion-menu li.open>a>i.has-sub-menu {
    transform: rotate(90deg);
}

.app-menu>ul>li ul li a {
    padding: 5px 33px;
    text-decoration: none;
    color: #5e606b;
    font-size: 13px;
    display: block;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-menu>ul>li ul li a:hover, .app-menu>ul>li ul li a.active, .app-menu>ul ul li.open>a {
    color: #1e1e24;
    font-weight: 500;
}

.accordion-menu li.sidebar-title {
    padding: 20px 15px 10px 30px;
    font-weight: 700;
    text-transform: uppercase;
    color: #40475c;
    font-size: 12px;
    opacity: .35;
    font-family: 'Montserrat', sans-serif;
}

.accordion-menu>li>ul ul {
    margin: 0;
    padding: 5px 0 5px 20px;
}

/* App Content */

.pace .pace-progress {
    z-index: 999999;
}

.app-content {
    margin-top: 100px;
}

.app-content .container-fluid {
    padding: 0;
}

.app-content .card {
    margin-bottom: 20px;
}

/* Hidden Sidebar */

.app.sidebar-hidden .app-container {
    margin-left: 0;
    width: 100%;
}

.app.sidebar-hidden .app-header {
    width: calc(100% - 360px);
}

.app.sidebar-hidden .app-sidebar {
    transform: translateX(-280px);
}

.app.sidebar-hidden .app-sidebar .logo {
    opacity: 0;
}

.app.sidebar-hidden .app-sidebar .logo.hidden-sidebar-logo {
    position: fixed;
    width: 280px;
    transform: translateX(310px);
    height: 70px;
    top: 30px;
    padding-top: 15px;
    padding-bottom: 15px;
    border-radius: 10px;
    background-color: #ffffff;
    opacity: 1;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

/* Off-canvas menu */

.app.menu-off-canvas .app-container {
    margin-left: 0;
    width: 100%;
}

.app.menu-off-canvas .app-header {
    left: 310px;
}

.app.menu-off-canvas .app-sidebar {
    transform: translateX(-280px);
}

.app.menu-off-canvas .app-sidebar .app-menu {
    height: 100%!important;
}

.app.menu-off-canvas.menu-off-canvas-show .app-sidebar {
    transform: translateX(0);
}

.app.menu-off-canvas .app-sidebar .logo.canvas-sidebar-hidden-logo {
    transform: translateX(0);
    opacity: 0;
    visibility: hidden;
}

.app.menu-off-canvas .app-sidebar .logo {
    position: fixed;
    width: 260px;
    transform: translateX(310px);
    height: 70px;
    top: 30px;
    padding-top: 15px;
    padding-bottom: 15px;
    border-radius: 10px;
    background-color: #ffffff;
    opacity: 1;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

/* Dark Menu */

.app.menu-dark .app-sidebar {
    background: #13171f;
}

.app.menu-dark .app-sidebar .logo {
    background-color: #1f2433;
}

.app.menu-dark:not(.sidebar-hidden) .app-sidebar .logo.logo-sm a {
    color: #fff;
}

.app.menu-dark .app-menu>ul>li>a {
    color: #969cad;
}

.app.menu-dark .app-menu>ul>li>a>i:not(.has-sub-menu).material-icons-two-tone {
    filter: invert(50%) sepia(8%) saturate(768%) hue-rotate(187deg) brightness(91%) contrast(81%);
}

.app.menu-dark .app-menu>ul>li>a:hover {
    color: #d3daed;
}

.app.menu-dark .app-menu>ul>li>a:hover>i:not(.has-sub-menu).material-icons-two-tone, .app.menu-dark .app-menu>ul>li.open>a>i:not(.has-sub-menu).material-icons-two-tone, .app.menu-dark .app-menu>ul>li.active-page>a>i:not(.has-sub-menu).material-icons-two-tone {
    filter: invert(86%) sepia(24%) saturate(158%) hue-rotate(188deg) brightness(101%) contrast(86%);
}

.app.menu-dark .app-menu>ul li.open>a, .app.menu-dark .app-menu>ul>li.active-page>a {
    color: #fff;
}

.app.menu-dark .app-menu>ul>li ul {
    background: #1f2433;
}

.app.menu-dark .app-menu>ul>li ul li a {
    color: #798094;
}

.app.menu-dark .app-menu>ul>li ul li a:hover, .app.menu-dark .app-menu>ul>li ul li a.active {
    color: #fff;
}

.app.menu-dark .app-sidebar .logo .sidebar-user-switcher .user-info-text, .app.menu-dark .app-sidebar .logo-icon .logo-text {
    color: #b0b7c7;
}

.app.menu-dark .accordion-menu li.sidebar-title {
    color: #b0b7c7;
    opacity: .4;
}

.app.menu-dark.sidebar-hidden .app-sidebar .logo .sidebar-user-switcher .user-info-text {
    color: #333;
}

.app.menu-dark.sidebar-hidden .app-sidebar .logo-icon .logo-text {
    color: #40475c;
}

/* Hover Menu */

@media (min-width: 1200px) {
    .menu-hover .app-sidebar {
        overflow: visible;
        display: inline-block;
        position: relative;
        height: auto;
    }
    
    .menu-hover .app-sidebar::before {
        position: fixed;
        top: 0;
        height: 100%;
        width: 280px;
        background: #fff;
        display: block;
        content: '';
        box-shadow: 0 0 1.25rem rgba(31, 45, 61, .04);
        z-index: -1;
    }
    
    .menu-dark.menu-hover .app-sidebar::before {
        background: #13171f;
    }
    
    .menu-hover .app-container {
        display: inline-block;
        margin-left: 0;
    }
    
    .menu-hover.sidebar-hidden .app-sidebar {
        transform: none;
        margin-left: -280px;
    }
    
    .menu-hover .app-menu {
        overflow: visible!important;
    }
    
    .menu-hover .app-menu .accordion-menu {
        overflow-x: visible;
    }
    
    .menu-hover .app-menu li {
        position: relative;
    }
    
    .menu-hover .app-menu ul>li>a {
        -webkit-transition: padding-right 0s, color 0.2s ease-in-out, padding-left 0.2s ease-in-out, filter 0.2s ease-in-out;
        -moz-transition: padding-right 0s, color 0.2s ease-in-out, padding-left 0.2s ease-in-out, filter 0.2s ease-in-out;
        -o-transition: padding-right 0s, color 0.2s ease-in-out, padding-left 0.2s ease-in-out, filter 0.2s ease-in-out;
        transition: padding-right 0s, color 0.2s ease-in-out, padding-left 0.2s ease-in-out, filter 0.2s ease-in-out;
    }
    
    .menu-hover .app-menu ul>li:hover {
        width: calc(100% + 20px)
    }
    
    .menu-hover .app-menu>ul>li:hover>a {
        padding-left: 20px;
        margin-right: 40px;
        color: #2269F5;
    }
    
    .menu-dark.menu-hover .app-menu>ul>li:hover>a {
        color: #d3daed;
    }
    
    .menu-hover .app-menu ul>li>ul>li:hover>a {
        margin-right: 20px;
        color: #1e1e24;
        font-weight: 500;
    }
    
    .menu-dark.menu-hover .app-menu ul>li ul>li:hover>a {
        color: #fff;
    }
    
    .app-menu>ul>li:hover>a>i:not(.has-sub-menu).material-icons-two-tone {
        filter: invert(34%) sepia(23%) saturate(3309%) hue-rotate(195deg) brightness(109%) contrast(115%);
    }
    
    .menu-hover .app-menu ul>li>a+.sub-menu {
        display: block!important;
        position: absolute;
        margin-left: 20px;
        top: -15px;
        left: 180px;
        padding: 20px 0;
        background: #fff;
        box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
        -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
        -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
        min-width: 200px;
        opacity: 0;
        visibility: hidden;
        -webkit-transition: all .2s ease-in-out;
        -moz-transition: all .2s ease-in-out;
        -o-transition: all .2s ease-in-out;
        transition: all .2s ease-in-out;
    }
    
    .menu-hover.menu-dark .app-menu>ul>li ul {
        background: #1f2433;
    }
    
    .menu-hover .app-menu>ul>li>a+.sub-menu {
        top: -40px;
        left: 270px;
    }
    
    .menu-hover .app-menu>ul>li>a.active+.sub-menu {
        display: none;
    }
    
    .menu-hover .app-menu ul>li:hover>.sub-menu {
        height: auto;
        visibility: visible;
        opacity: 1;
        left: 200px;
    }
    
    .menu-hover .app-menu>ul>li:hover>.sub-menu {
        left: 280px;
    }
    
    .menu-hover .app-menu ul>li>.sub-menu:after {
        position: absolute;
        top: 20px;
        left: -7px;
        right: auto;
        display: inline-block !important;
        border-right: 7px solid #fff;
        border-bottom: 7px solid transparent;
        border-top: 7px solid transparent;
        content: '';
    }
    
    .menu-hover.menu-dark .app-menu ul>li>.sub-menu:after {
        border-right-color: #1f2433;
    }
    
    .menu-hover .app-menu>ul>li>.sub-menu:after {
        top: 42.5px;
    }
    
}

/* Colored Sidebar */

.app.sidebar-colored .app-sidebar {
    background: #004be0;
}

.app.sidebar-colored .app-sidebar .logo {
    background-color: rgba(0, 0, 0, .1);
}

.app.sidebar-colored .app-sidebar .logo-sm a {
    color: #fff;
}

.app.sidebar-colored .app-menu>ul>li>a {
    color: #a1baff;
    font-weight: 500;
}

.app.sidebar-colored .app-menu>ul>li>a>i:not(.has-sub-menu).material-icons-two-tone {
    filter: invert(83%) sepia(24%) saturate(6045%) hue-rotate(190deg) brightness(105%) contrast(101%);
}

.app.sidebar-colored .app-menu > ul > li > a > i.has-sub-menu {
    color: #a1baff;
}

.app.sidebar-colored .app-menu>ul>li>a:hover {
    color: #fff;
}

.app.sidebar-colored .app-menu>ul>li>a:hover>i:not(.has-sub-menu).material-icons-two-tone, .app.sidebar-colored .app-menu>ul>li.open>a>i:not(.has-sub-menu).material-icons-two-tone, .app.sidebar-colored .app-menu>ul>li.active-page>a>i:not(.has-sub-menu).material-icons-two-tone {
    filter: invert(99%) sepia(5%) saturate(548%) hue-rotate(211deg) brightness(117%) contrast(100%);
}

.app.sidebar-colored .app-menu>ul li.open>a, .app.sidebar-colored .app-menu>ul>li.active-page>a {
    color: #fff;
}

.app.sidebar-colored .app-menu>ul>li ul {
    background: transparent;
}

.app.sidebar-colored .app-menu>ul>li>ul {
    background-color: rgba(0, 0, 0, .1);
}

.app.sidebar-colored .app-menu>ul>li ul li a {
    color: #a1baff;
}

.app.sidebar-colored .app-menu>ul>li ul li a:hover, .app.sidebar-colored .app-menu>ul>li ul li a.active {
    color: #fff;
}

.app.sidebar-colored .app-sidebar .logo .sidebar-user-switcher .user-info-text, .app.sidebar-colored .app-sidebar .logo-icon .logo-text {
    color: #fff;
}

.app.sidebar-colored .accordion-menu li.sidebar-title {
    color: #a1baff;
    opacity: .6;
}

.app.sidebar-colored.sidebar-hidden .app-sidebar .logo .sidebar-user-switcher .user-info-text {
    color: #333;
}

.app.sidebar-colored.sidebar-hidden .app-sidebar .logo-icon .logo-text {
    color: #40475c;
}

/* Full-width Header */

.full-width-header .app-header {
    position: fixed;
    width: calc(100% - 280px);
    right: 0;
    z-index: 1001;
    top: 0;
    border-radius: 0;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.full-width-header .app-header .navbar {
    height: 70px;
    padding: 0 45px;
    border-radius: 0;
    background-color: #fff;
}

.full-width-header .app-menu {
    height: calc(100% - 70px)!important;
}

.full-width-header .app-content {
    margin-top: 70px;
}

.app.sidebar-hidden.full-width-header .app-header {
    left: auto;
    width: calc(100% - 240px);
}

.app.sidebar-hidden.full-width-header .app-sidebar .logo.hidden-sidebar-logo {
    top: 0;
    left: 0;
    transform: translateX(280px);
    width: 240px;
    padding-left: 60px;
    border-radius: 0;
    background: #fff;
    color: #293442;
    box-shadow: none;
}

/* Transaprent Header */

.header-transaprent .app-header {
    position: relative;
    width: 100%;
    right: 0;
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
}

.header-transaprent .app-header .navbar {
    background-color: transparent;
    box-shadow: none;
}

.header-transaprent .app-header::before {
    content: none;
}

.app.sidebar-hidden.header-transaprent .app-header {
    left: 0;
    padding-left: 280px;
}

.app.sidebar-hidden.header-transaprent .app-sidebar .logo.hidden-sidebar-logo {
    position: relative;
}

.app.sidebar-hidden.header-transaprent .app-sidebar {
    transform: none;
    margin-left: -280px;
    position: relative;
}

.header-transaprent .app-content {
    margin-top: 30px;
}

/* Large Header */

.header-large .app-header {
    position: relative;
    width: 100%;
    right: 0;
    background: #fff;
    height: 131px;
    margin: -30px;
    width: calc(100% + 60px);
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.header-large .app-header::before {
    display: none;
}

.header-large .app-header .navbar {
    box-shadow: none;
    background-color: transparent;
    padding: 0 30px;
}

.header-large .app-content {
    margin-top: 50px;
}

.header-large.sidebar-hidden .app-sidebar .logo.hidden-sidebar-logo {
    top: 0;
    left: 0;
    transform: translateX(280px);
    border-radius: 0;
    width: 310px;
    box-shadow: none;
    border-right: 1px solid #E3E6EA;
    padding: 15px 40.5px;
}

.header-large.sidebar-hidden .app-header {
    width: calc(100% + 60px);
    margin-right: 0;
    left: 0;
}

.header-large.sidebar-hidden .app-header .navbar {
    margin-left: 310px;
}

/* Header Toolbar */

.app-header .header-toolbar {
    height: 61px;
    position: relative;
    border-top: 1px solid #F4F7FA;
    background: #f4f7fa;
    padding: 0 30px;
}

.app-header .header-toolbar .header-toolbar-menu {
    float: left;
    margin-bottom: 0;
    padding-left: 0;
}

.app-header .header-toolbar .header-toolbar-menu>li {
    display: inline-block;
    height: 60px;
}

.app-header .header-toolbar .header-toolbar-menu>li>a {
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    color: #a0b0cc;
    margin: 10px 5px;
    padding: 9px 10px;
    display: block;
    border-radius: 10px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-header .header-toolbar .header-toolbar-menu>li>a:hover {
    color: #037AFB;
    background-color: rgba(229, 234, 240, 0.6);
}

.app-header .header-toolbar .header-toolbar-actions {
    float: right;
    padding: 13.5px 0;
}

/* Colorful Header */

.header-colorful .app-header .navbar {
    background-color: #2269f4;
}

.header-colorful .app-header .navbar .navbar-nav>li>a {
    color: #c4d8ff;
}

.header-colorful .app-header .navbar .navbar-nav>li>a.active, .header-colorful .app-header .navbar .navbar-nav>li>a:hover {
    color: #fff;
}

.header-colorful .app-header .navbar .navbar-nav>li>a.active::after {
    background-color: #fff;
}

/* Content Menu */

.app-content .content-menu {
    width: 250px;
    padding: 20px 0;
    position: fixed;
    height: calc(100vh - 160px);
}

.app-content .content-menu ul {
    margin-bottom: 0;
}

.app-content .content-menu li a {
    padding: 5px 30px;
    display: block;
    color: #435263;
    text-decoration: none;
    margin: 5px 0;
    position: relative;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-content .content-menu li a:hover, .app-content .content-menu li a.active {
    font-weight: 500;
    color: #13171f;
}

.app-content .content-menu li.divider {
    background: #ccd8ed;
}

.app-content .content-menu li a.active::after {
    height: 100%;
    width: 3px;
    content: '';
    display: block;
    position: absolute;
    right: 0;
    top: 0;
    background: #037AFB;
}

.app-content .content-menu li a i {
    vertical-align: middle;
    margin-right: 10px;
}

.app-content .content-menu-toggle {
    display: none;
}

@media (max-width: 1200px) {
    .app-content .content-menu {
        position: absolute!important;
        background: #fff;
        z-index: 999;
        height: auto;
        border-radius: 10px;
        top: 180px;
        left: 30px;
        border-right: none!important;
        opacity: 0;
        visibility: hidden;
        height: auto!important;
        -webkit-transition: all .2s ease-in-out;
        -moz-transition: all .2s ease-in-out;
        -o-transition: all .2s ease-in-out;
        transition: all .2s ease-in-out;
        box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
        -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
        -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    }

    .app-content .content-menu-toggle {
        display: inline-block;
    }

    .app-content .content-menu + .content-wrapper {
        width: 100%!important;
        float: none;
    }

    .content-menu-shown .app-content .content-menu {
        opacity: 1;
        visibility: visible;
    }

    .app-content .content-menu + .content-wrapper .page-description {
        margin: 0 0 30px!important;
    }

    .app-content .content-menu li a.active::after {
        left: 0!important;
    }
}

/* Content Menu - Left */

.app-content .content-menu.content-menu-left {
    float: left;
    border-right: 1px solid #ccd7ed;
    margin-right: -10.5px;
}

.app-content .content-menu.content-menu-left::after {
    content: '';
    width: 1px;
    height: 100%;
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
}

.app-content .content-menu.content-menu-left+.content-wrapper {
    float: right;
    width: calc(100% - 280px);
}

.app-content .content-menu.content-menu-left+.content-wrapper .page-description {
    margin-left: -30px;
}

.app-content .content-menu.content-menu-left+.content-wrapper .container .page-description {
    margin-left: -40.5px;
    padding-left: 40.5px;
}

/* Content Menu - Right */

.app-content .content-menu.content-menu-right {
    right: 30px;
    border-left: 1px solid #ccd7ed;
    margin-left: -10.5px;
}

.app-content .content-menu.content-menu-right::after {
    content: '';
    width: 1px;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
}

.app-content .content-menu.content-menu-right li a.active::after {
    left: 0;
}

.app-content .content-menu.content-menu-right+.content-wrapper {
    float: left;
    width: calc(100% - 280px);
}

.app-content .content-menu.content-menu-right+.content-wrapper .page-description {
    margin-right: -30px;
}

.app-content .content-menu.content-menu-right+.content-wrapper .container .page-description {
    margin-right: -40.5px;
    padding-right: 40.5px;
}

/* Page Headings */

.app-content .page-description {
    overflow: hidden;
    padding: 30px;
    font-size: 15px;
    margin-bottom: 30px;
    border-bottom: 1px solid #ccd6ed;
}

@media (max-width: 1200px) {
    .page-description.d-flex {
        display: block!important;
    }

    .page-description .page-description-actions a {
        margin: 20px 0 0;
    }
}

.app-content .page-description span i {
    font-size: 14px;
}

.app-content .page-description h1 {
    font-weight: bold;
    color: #27313F;
    margin-bottom: 0;
}

.app-content .page-description h1+span {
    margin-top: 25px;
    display: block;
}

.app-content .page-description-tabbed {
    padding-bottom: 0;
    border-bottom: none;
}

.app-content .page-description-tabbed .nav-tabs {
    margin: 20px -30px;
    padding: 0 30px;
    border-bottom-color: #ccd6ed;
}

/* Section Headings */

.section-description {
    overflow: hidden;
    padding: 20px 30px;
    font-size: 15px;
    margin-bottom: 30px;
    border-bottom: 1px solid #ccd6ed;
}

.section-description h1 {
    font-weight: bold;
    color: #27313F;
    margin-bottom: 0;
    font-size: 24px;
}

.section-description span i {
    font-size: 14px;
}

.section-description span {
    font-size: 14px;
}

.section-description h1+span {
    margin-top: 15px;
    display: block;
}

.section-description-inline h1 {
    float: left;
    margin-right: 20px;
}

.section-description-inline h1+span {
    line-height: 28px;
    float: left;
    vertical-align: middle;
    margin-top: 0;
    color: rgba(39, 49, 63, .55);
}

.section-description h1 .badge {
    vertical-align: middle;
    margin-left: 10px;
    font-size: 12px;
}

/*------------------------------------------------------------
[4. UI Elements]
------------------------------------------------------------*/

/* Examples */

.example-container {
    border: 5px solid #F4F7FA;
    border-radius: 10px;
}

.example-container .example-content {
    padding: 20px;
    border-radius: 10px;
}

.example-container .example-code {
    background: #F4F7FA;
    position: relative;
    padding: 10px 20px 20px;
}

.example-container .example-code .tab-content {
    margin-top: 20px;
}

.example-container .example-code pre {
    margin: 0;
    max-height: 300px;
}

.example-container .example-code>pre {
    margin-top: 10px;
}

.example-container .example-code .hljs {
    padding: 0;
    background-color: inherit;
}

.example-container .dropdown-menu-example-open {
    display: block;
    visibility: visible;
    opacity: 1;
    position: relative;
    margin-top: 0;
}

.placeholder-img {
    font-size: 1.125rem;
    text-anchor: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.placeholder-img-lg {
    font-size: calc(1.475rem + 2.7vw);
}

@media (min-width: 1200px) {
    .placeholder-img-lg {
        font-size: 3.5rem;
    }
}

/* Dropdowns */

.dropdown-menu {
    display: block;
    visibility: hidden;
    margin-top: 40px;
    opacity: 0;
    border: none;
    border-radius: 10px;
    padding: 10px;
    min-width: 200px;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.dropdown-menu:after {
    position: absolute;
    top: -7px;
    left: 10px;
    right: auto;
    display: inline-block !important;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #fff;
    border-left: 7px solid transparent;
    content: '';
}

.dropdown-menu.dropdown-menu.dropdown-menu-dark {
    background-color: #27364a;
}

.dropdown-menu.dropdown-menu-dark:after {
    border-bottom-color: #27364a;
}

.dropdown-toggle::after {
    vertical-align: middle;
}

.dropdown-divider, .dropdown-menu-dark .dropdown-divider {
    border-top-color: #8fa5c1;
    margin: 10px;
}

.dropdown.dropup>.dropdown-menu::after, 
.btn-group.dropup>.dropdown-menu::after, 
.dropdown-toggle.dropup>.dropdown-menu::after, 
.dropdown-menu[data-popper-placement="top-start"]::after, 
.dropdown.dropend>.dropdown-menu::after, 
.btn-group.dropend>.dropdown-menu::after, 
.dropdown-toggle.dropend>.dropdown-menu::after, 
.dropdown-menu[data-popper-placement="right-start"]::after,
.dropdown.dropstart>.dropdown-menu::after,
.btn-group.dropstart>.dropdown-menu::after,
.dropdown-toggle.dropstart>.dropdown-menu::after,
.dropdown-menu[data-popper-placement="left-start"]::after,
.dropup .dropdown-menu::after {
    display: none !important;
}

.dropdown-menu[data-popper-placement="bottom-end"]::after {
    right: 23px;
    left: auto;
}

.navbar .dropdown-menu:after {
    left: 23px;
    right: auto;
}

.navbar .dropdown-menu-end:after {
    right: 23px;
    left: auto;
}

.dropdown-menu.show {
    opacity: 1;
    transform: none;
    visibility: visible;
    top: 20px!important;
}

.dropdown-menu[data-popper-placement="top-start"] {
    margin-bottom: 10px!important;
    top: auto!important;
}

.dropdown-menu[data-popper-placement="right-start"] {
    margin-left: 10px!important;
    top: auto!important;
}

.dropdown-menu[data-popper-placement="left-start"] {
    margin-right: 10px!important;
    top: auto!important;
}

.dropdown-menu.dropdown-lg {
    width: 400px;
}

.navbar .dropdown-menu {
    top: calc(100% + 30px)!important;
    margin-top: 20px!important;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.navbar .dropdown-menu:not(.dropdown-menu-end) {
    left: -10px;
}

.navbar .dropdown-menu-end {
    right: 0;
    left: auto;
}

.navbar .dropdown-menu.show {
    margin-top: 0px!important;
}

.navbar .dropdown-toggle::after {
    display: none;
}

.dropdown-menu li a {
    color: #40475c;
    padding: 5px 10px;
    margin: 5px 0;
    display: block;
    font-size: 13px;
    font-weight: 400;
    position: relative;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.dropdown-menu.dropdown-menu.dropdown-menu-dark li a {
    color: #a7b9cf;
}

.dropdown-menu li a:hover {
    background: transparent;
    font-weight: 500;
}

.dropdown-menu li a.active {
    color: #27313F;
    font-weight: 500;
    background-color: transparent;
}

.dropdown-menu.dropdown-menu.dropdown-menu-dark li a:hover {
    color: #bfcfe3;
    background-color: transparent;
}

.dropdown-menu.dropdown-menu.dropdown-menu-dark li a.active {
    color: #fff;
    font-weight: 500;
    background-color: transparent;
}

.dropdown-menu.large-items-menu {
    padding-left: 0;
    padding-right: 0;
}

.dropdown-menu.large-items-menu li a {
    white-space: normal;
    padding: 15px 30px;
    margin: 0;
}

.dropdown-menu.large-items-menu li:not(:last-child) a::after {
    content: "";
    width: calc(100% - 60px);
    position: absolute;
    left: 30px;
    bottom: 0;
    height: 1px;
    background: #e3e6eb;
    opacity: 0.6;
    display: block;
}

.dropdown-menu.large-items-menu li a:hover {
    font-weight: normal;
    background: rgba(238, 241, 245, 0.7);
}

.dropdown-menu.large-items-menu li a .dropdown-item-title .badge {
    font-size: 10px!important;
    vertical-align: middle;
    margin-left: 3px;
}

.dropdown-menu.large-items-menu li a .dropdown-item-description {
    opacity: 0.6;
}

.dropdown-menu.large-items-menu li.dropdown-btn-item {
    padding: 15px 30px;
}

.dropdown-menu.large-items-menu li a .dropdown-item-title .hidden-helper-text {
    float: right;
    font-size: 13px;
    opacity: 0;
    margin: 0 10px 0 0;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.dropdown-menu.large-items-menu li a .dropdown-item-title .hidden-helper-text i {
    font-size: 14px;
    vertical-align: middle;
    margin-right: 0;
}

.dropdown-menu.large-items-menu li a:hover .dropdown-item-title .hidden-helper-text {
    opacity: 0.7;
    margin-right: 0;
}

.dropdown-menu .dropdown-header {
    padding: 10px;
    text-transform: uppercase;
    font-weight: 600;
    color: rgba(64, 71, 92, 0.5);
}

.dropdown-menu.large-items-menu .dropdown-header {
    padding: 10px 30px;
}

/* Tables */

.table-dark th {
    background-color: #40475c;
    color: #fff!important;
}

.table-light th {
    background-color: #F4F7FA;
}

.table th {
    font-weight: 500;
    color: #7B8C9D;
}

.table * {
    border-color: #e1e7ed!important;
}

.table th, .table td {
    padding: 15px 20px!important;
}

.table tbody tr:last-child td, .table tbody tr:last-child th {
    border-bottom: none;
}

.table tfoot tr td, .table tfoot tr th {
    border-bottom: none;
    border-top-width: 1px;
}

.table-hover>tbody>tr {
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.table-hover>tbody>tr:hover {
    --bs-table-hover-bg: transparent;
    background: #F4F7FA;
}

.table-striped>tbody>tr:nth-of-type(odd) {
    --bs-table-striped-bg: #F4F7FA;
}

/* Datatables */

table.dataTable, .dataTables_scroll {
    margin-top: 20px!important;
    margin-bottom: 20px!important;
}

table.dataTable td, table.dataTable th {
    padding: 15px 20px!important;
    border-bottom: 1px solid #e1e7ed!important;
}

table.dataTable tfoot th {
    border-bottom: 0!important;
}

.dataTables_scroll .dataTables_scrollBody thead {
    display: none;
}

table.dataTable thead th, table.dataTable tfoot th {
    font-weight: 500;
    color: #7B8C9D;
}

table.dataTable thead .sorting:before, table.dataTable thead .sorting_asc:before, table.dataTable thead .sorting_desc:before, table.dataTable thead .sorting_asc_disabled:before, table.dataTable thead .sorting_desc_disabled:before {
    content: '\e316';
    font-family: 'Material Icons';
    font-size: 14px;
    right: 10px;
    bottom: calc(50% - 4px);
}

table.dataTable thead .sorting:after, table.dataTable thead .sorting_asc:after, table.dataTable thead .sorting_desc:after, table.dataTable thead .sorting_asc_disabled:after, table.dataTable thead .sorting_desc_disabled:after {
    content: '\e313';
    font-family: 'Material Icons';
    font-size: 14px;
    right: 10px;
    bottom: calc(50% - 14px);
}

div.dataTables_wrapper div.dataTables_length select {
    margin: 0 10px;
}

div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 10px;
}

tr.group {
    background-color: #e1e7ed!important;
}

/* Alerts */

.alert {
    border: none;
    border-radius: 10px;
    padding: 20px 30px;
}

.alert-rounded {
    border-radius: 100px;
}

.alert.alert-primary {
    background: #2269F5;
    color: #fff;
}

.alert.alert-secondary {
    background: #FCEACE;
    color: #4d546b;
}

.alert.alert-success {
    background: #4bad48;
    color: #fff;
}

.alert.alert-danger {
    background: #FF4857;
    color: #fff;
}

.alert.alert-warning {
    background: #ff9500;
    color: #fff;
}

.alert.alert-info {
    background: #61acfc;
    color: #fff;
}

.alert.alert-light {
    background: #F4F7FA;
    color: #4d546b;
}

.alert.alert-dark {
    background: #40475c;
    color: #fff;
}

.alert.alert-style-light.alert-primary {
    background: #DFF0FE;
    color: #2269F5;
}

.alert.alert-style-light.alert-success {
    background: #e6fcf0;
    color: #4bad48;
}

.alert.alert-style-light.alert-danger {
    background: #fce3e5;
    color: #FF4857;
}

.alert.alert-style-light.alert-warning {
    background: #fff3e3;
    color: #ff9500;
}

.alert.alert-style-light.alert-info {
    background: #e0efff;
    color: #61acfc;
}

.alert.alert-style-light.alert-dark {
    background: #d4d4d4;
    color: #40475c;
}

.alert-custom {
    position: relative;
    padding: 10px 15px;
    border-radius: 10px;
    background-color: #fff;
    overflow: hidden;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.alert-custom img {
    width: 60px;
    height: 60px;
    float: left;
    border-radius: 30px;
}

.alert-custom img+.alert-content, .alert-custom .custom-alert-icon+.alert-content {
    padding-left: 20px;
    float: right;
    width: calc(100% - 60px);
}

.alert-custom .alert-content {
    padding: 8px 10px;
    overflow: hidden;
}

.alert-custom .alert-content .alert-title {
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
}

.alert-custom .alert-content .alert-text {
    font-size: 13px;
    display: block;
}

.alert.alert-custom .custom-alert-icon {
    width: 60px;
    height: 60px;
    float: left;
    border-radius: 30px;
    font-size: 14px;
    text-align: center;
}

.alert.alert-custom .custom-alert-icon.icon-primary {
    background: #DFF0FE;
    color: #2269F5;
}

.alert.alert-custom .custom-alert-icon.icon-success {
    background: #e6fcf0;
    color: #4bad48;
}

.alert.alert-custom .custom-alert-icon.icon-danger {
    background: #fce3e5;
    color: #FF4857;
}

.alert.alert-custom .custom-alert-icon.icon-warning {
    background: #fff3e3;
    color: #ff9500;
}

.alert.alert-custom .custom-alert-icon.icon-info {
    background: #e0efff;
    color: #61acfc;
}

.alert.alert-custom .custom-alert-icon.icon-dark {
    background: #40475c;
    color: #fff;
}

.alert.alert-custom .custom-alert-icon i {
    line-height: 60px;
}

.alert-custom.alert-indicator-top, .alert-custom.alert-indicator-left, .alert-custom.alert-indicator-right, .alert-custom.alert-indicator-bottom {
    border-radius: 5px;
}

.alert-custom.alert-indicator-top::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    content: ' ';
}

.alert-custom.alert-indicator-left::before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 3px;
    content: ' ';
}

.alert-custom.alert-indicator-right::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 3px;
    content: ' ';
}

.alert-custom.alert-indicator-bottom::before {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    content: ' ';
}

.alert-custom.indicator-success::before {
    background: #4bad48;
}

.alert-custom.indicator-danger::before {
    background: #FF4857;
}

.alert-custom.indicator-info::before {
    background: #61acfc;
}

.alert-custom.indicator-warning::before {
    background: #ff9500;
}

/* Avatars */

.avatar {
    border-radius: 5px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    font-weight: bold;
    color: #364459;
    display: inline-block;
    position: relative;
}

.avatar img {
    width: inherit;
    height: inherit;
    border-radius: inherit;
}

.avatar-xxl {
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 32px;
}

.avatar-xl {
    width: 80px;
    height: 80px;
    line-height: 80px;
    font-size: 24px;
}

.avatar-sm {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
}

.avatar-xs {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
}

.avatar .avatar-title {
    width: inherit;
    height: inherit;
    background-color: #BFC9D4;
    border-radius: inherit;
    text-align: center;
}

.avatar.status::after {
    content: '';
    display: block;
    position: absolute;
    width: 16px;
    height: 16px;
    background: #364459;
    border-radius: 8px;
    border: 2px solid #fff;
    bottom: -4px;
    right: -4px;
}

.avatar.status.status-online::after {
    background: #4bad48;
}

.avatar.status.status-offline::after {
    background: #FF4857;
}

.avatar.avatar-xxl.status::after {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    bottom: -6px;
    right: -6px;
    border-width: 3px;
}

.avatar.avatar-xl.status::after {
    width: 20px;
    height: 20px;
    border-radius: 10px;
    border-width: 3px;
}

.avatar.avatar-sm.status::after {
    width: 14px;
    height: 14px;
    border-radius: 7px;
}

.avatar.avatar-xs.status::after {
    width: 12px;
    height: 12px;
    border-radius: 6px;
}

.avatar.avatar-rounded {
    border-radius: 50%!important;
}

.avatar-container .avatar {
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.avatar-container .avatar:hover {
    transform: translateY(-7px);
}

.avatar-container .avatar>* {
    border: 3px solid #fff;
}

.avatar-container .avatar:not(:first-child) {
    margin-left: -15px;
}

.avatar .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 11px;
}

/* Badges */

.badge.badge-primary {
    background: #2269F5;
    color: #fff;
}

.badge.badge-secondary {
    color: #4d546b;
    background: #FCEACE;
}

.badge.badge-success {
    background: #4bad48;
    color: #fff;
}

.badge.badge-danger {
    background: #FF4857;
    color: #fff;
}

.badge.badge-warning {
    background: #ff9500;
    color: #fff;
}

.badge.badge-info {
    background: #61acfc;
    color: #fff;
}

.badge.badge-light {
    background: #F4F7FA;
    color: #4d546b;
}

.badge.badge-dark {
    background: #40475c;
    color: #fff;
}

.badge.badge-style-light.badge-primary {
    background: #DFF0FE;
    color: #2269F5;
}

.badge.badge-style-light.badge-success {
    background: #e6fcf0;
    color: #4bad48;
}

.badge.badge-style-light.badge-danger {
    background: #fce3e5;
    color: #FF4857;
}

.badge.badge-style-light.badge-warning {
    background: #fff3e3;
    color: #ff9500;
}

.badge.badge-style-light.badge-info {
    background: #e0efff;
    color: #61acfc;
}

.badge.badge-style-light.badge-dark {
    background: #d4d4d4;
    color: #40475c;
}

.badge.badge-style-bordered {
    background-color: transparent;
    border: 1px solid #f1f1f1;
}

.badge.badge-style-bordered.badge-primary {
    border-color: #2269F5;
    color: #2269F5;
}

.badge.badge-style-bordered.badge-success {
    border-color: #4bad48;
    color: #4bad48;
}

.badge.badge-style-bordered.badge-danger {
    border-color: #FF4857;
    color: #FF4857;
}

.badge.badge-style-bordered.badge-warning {
    border-color: #ff9500;
    color: #ff9500;
}

.badge.badge-style-bordered.badge-info {
    border-color: #61acfc;
    color: #61acfc;
}

.badge.badge-style-bordered.badge-dark {
    border-color: #40475c;
    color: #40475c;
}

a.badge {
    text-decoration: none;
}

/* Breadcrumbs */

.breadcrumb.breadcrumb-container {
    padding: 12px 30px;
    background: #F4F7FA;
    border-radius: 5px;
}

.breadcrumb .breadcrumb-item a {
    text-decoration: none;
}

.breadcrumb .breadcrumb-item.active {
    color: #787f96;
}

.breadcrumb .breadcrumb-item::before {
    color: #787f96;
}

.breadcrumb.bg-secondary .breadcrumb-item a {
    color: #4d546b;
}

.breadcrumb.bg-primary .breadcrumb-item a, .breadcrumb.bg-success .breadcrumb-item a, .breadcrumb.bg-danger .breadcrumb-item a, .breadcrumb.bg-warning .breadcrumb-item a, .breadcrumb.bg-info .breadcrumb-item a, .breadcrumb.bg-dark .breadcrumb-item a {
    color: #fff;
}

.breadcrumb.bg-primary .breadcrumb-item::before, .breadcrumb.bg-success .breadcrumb-item::before, .breadcrumb.bg-danger .breadcrumb-item::before, .breadcrumb.bg-warning .breadcrumb-item::before, .breadcrumb.bg-info .breadcrumb-item::before, .breadcrumb.bg-dark .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.4);
}

.breadcrumb.bg-primary .breadcrumb-item.active, .breadcrumb.bg-success .breadcrumb-item.active, .breadcrumb.bg-danger .breadcrumb-item.active, .breadcrumb.bg-warning .breadcrumb-item.active, .breadcrumb.bg-info .breadcrumb-item.active, .breadcrumb.bg-dark .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-primary {
    background-color: #DFF0FE!important;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-primary .breadcrumb-item a, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-primary .breadcrumb-item.active, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-primary .breadcrumb-item::before {
    color: #2269F5;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-success {
    background-color: #e6fcf0!important;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-success .breadcrumb-item a, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-success .breadcrumb-item.active, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-success .breadcrumb-item::before {
    color: #4bad48;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-danger {
    background-color: #fce3e5!important;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-danger .breadcrumb-item a, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-danger .breadcrumb-item.active, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-danger .breadcrumb-item::before {
    color: #FF4857;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-warning {
    background-color: #fff3e3!important;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-warning .breadcrumb-item a, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-warning .breadcrumb-item.active, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-warning .breadcrumb-item::before {
    color: #ff9500;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-info {
    background-color: #e0efff!important;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-info .breadcrumb-item a, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-info .breadcrumb-item.active, .breadcrumb.breadcrumb-container.breadcrumb-container-light.bg-info .breadcrumb-item::before {
    color: #61acfc;
}

.breadcrumb.breadcrumb-container.breadcrumb-container-light .breadcrumb-item.active, .breadcrumb.breadcrumb-container.breadcrumb-container-light .breadcrumb-item:not(.active)::before {
    opacity: 0.5;
}

.breadcrumb.breadcrumb-separator-chevron .breadcrumb-item+.breadcrumb-item::before {
    font-family: 'Material Icons';
    content: var(--bs-breadcrumb-divider, "\e5cc");
}

.breadcrumb.breadcrumb-separator-arrow .breadcrumb-item+.breadcrumb-item::before {
    font-family: 'Material Icons';
    content: var(--bs-breadcrumb-divider, "\e5c8");
}

.breadcrumb.breadcrumb-separator-dot .breadcrumb-item+.breadcrumb-item::before {
    font-family: 'Material Icons';
    content: var(--bs-breadcrumb-divider, "\e061");
    font-size: 6px;
    line-height: 22px;
    margin: 0 4px;
}

/* Buttons */

.btn {
    display: inline-block;
    padding: 6px 20px;
    border-radius: 5px;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.btn-lg, .btn-group-lg .btn {
    padding: 9px 30px;
    font-size: 16px;
}

.btn-sm, .btn-group-sm .btn {
    padding: 4px 12px;
    font-size: 13px;
}

.dropdown-toggle-split {
    padding: 6px 10px;
}

.btn-group-lg .dropdown-toggle-split {
    padding: 9px 15px;
}

.btn i {
    vertical-align: text-top;
    line-height: 21px;
    margin-right: 6px;
    margin-left: -8px;
    font-size: 20px;
}

.btn-lg i {
    line-height: 24px;
}

.btn-sm i {
    margin-right: 2px;
    line-height: 19px;
    font-size: 20px;
}

.btn-rounded {
    border-radius: 18px;
}

.btn-lg.btn-rounded {
    border-radius: 22px;
}

.btn-sm.btn-rounded {
    border-radius: 15px;
}

.btn:focus, .btn.active, .btn:active, .btn.dropdown-toggle.show, .btn-check:active+.btn, .btn-check:checked+.btn, .btn-check:focus+.btn, .btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show>.btn-primary.dropdown-toggle:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus, .show>.btn-secondary.dropdown-toggle:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus, .show>.btn-success.dropdown-toggle:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:disabled):not(.disabled):active:focus, .show>.btn-danger.dropdown-toggle:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:disabled):not(.disabled):active:focus, .show>.btn-warning.dropdown-toggle:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabled):not(.disabled):active:focus, .show>.btn-info.dropdown-toggle:focus, .btn-light:not(:disabled):not(.disabled).active:focus, .btn-light:not(:disabled):not(.disabled):active:focus, .show>.btn-light.dropdown-toggle:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabled):not(.disabled):active:focus, .show>.btn-dark.dropdown-toggle:focus, .btn-link:not(:disabled):not(.disabled).active:focus, .btn-link:not(:disabled):not(.disabled):active:focus, .show>.btn-link.dropdown-toggle:focus {
    box-shadow: none!important;
}

.btn-primary, .btn-primary.disabled, .btn-primary:disabled {
    color: #fff;
    background-color: #2269F5;
    border-color: #2269F5;
}

.btn-primary:hover, .btn-primary.focus, .btn-primary:focus, .btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #3e7dfa;
    border-color: #3e7dfa;
}

.btn-secondary, .btn-secondary.disabled, .btn-secondary:disabled {
    color: #4d546b;
    background-color: #FCEACE;
    border-color: #FCEACE;
}

.btn-secondary:hover, .btn-secondary.focus, .btn-secondary:focus, .btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show>.btn-secondary.dropdown-toggle {
    color: #4d546b;
    background-color: #f2dbb3;
    border-color: #f2dbb3;
}

.btn-success, .btn-success.disabled, .btn-success:disabled {
    color: #fff;
    background-color: #4bad48;
    border-color: #4bad48;
}

.btn-success:hover, .btn-success.focus, .btn-success:focus, .btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active, .show>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #70d16d;
    border-color: #70d16d;
}

.btn-danger, .btn-danger.disabled, .btn-danger:disabled {
    color: #fff;
    background-color: #FF4857;
    border-color: #FF4857;
}

.btn-danger:hover, .btn-danger.focus, .btn-danger:focus, .btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show>.btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #ff6673;
    border-color: #ff6673;
}

.btn-warning, .btn-warning.disabled, .btn-warning:disabled {
    color: #fff;
    background-color: #ff9500;
    border-color: #ff9500;
}

.btn-warning:hover, .btn-warning.focus, .btn-warning:focus, .btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active, .show>.btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ffa930;
    border-color: #ffa930;
}

.btn-info, .btn-info.disabled, .btn-info:disabled {
    color: #fff;
    background-color: #61acfc;
    border-color: #61acfc;
}

.btn-info:hover, .btn-info.focus, .btn-info:focus, .btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active, .show>.btn-info.dropdown-toggle {
    color: #fff;
    background-color: #79b9fc;
    border-color: #79b9fc;
}

.btn-light, .btn-light.disabled, .btn-light:disabled {
    color: #4d546b;
    background-color: #F4F7FA;
    border-color: #F4F7FA;
}

.btn-light:hover, .btn-light.focus, .btn-light:focus, .btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):not(.disabled):active, .show>.btn-light.dropdown-toggle {
    color: #4d546b;
    background-color: #dfe3e8;
    border-color: #dfe3e8;
}

.btn-dark, .btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #40475c;
    border-color: #40475c;
}

.btn-dark:hover, .btn-dark.focus, .btn-dark:focus, .btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active, .show>.btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #4d556b;
    border-color: #4d556b;
}

.btn-link, .btn-link.disabled, .btn-link:disabled {
    color: #3599fd;
    background-color: transparent;
    border-color: transparent;
    text-decoration: none;
}

.btn-link:hover, .btn-link.focus, .btn-link:focus, .btn-link:not(:disabled):not(.disabled).active, .btn-link:not(:disabled):not(.disabled):active, .show>.btn-link.dropdown-toggle {
    color: #2482e0;
    background-color: #DFF0FE;
    border-color: #DFF0FE;
}

.btn-style-light, .btn-style-light:focus, .btn-style-light.active, .btn-style-light:active {
    border: none!important;
}

.btn-style-light.btn-primary, .btn-style-light.btn-primary.disabled, .btn-style-light.btn-primary:disabled {
    background: #DFF0FE;
    color: #2269F5;
}

.btn-style-light.btn-primary:hover, .btn-style-light.btn-primary.focus, .btn-style-light.btn-primary:focus, .btn-style-light.btn-primary:not(:disabled):not(.disabled).active, .btn-style-light.btn-primary:not(:disabled):not(.disabled):active, .show>.btn-style-light.btn-primary.dropdown-toggle {
    color: #2269F5;
    background-color: #badfff;
}

.btn-style-light.btn-success, .btn-style-light.btn-success.disabled, .btn-style-light.btn-success:disabled {
    background: #e6fcf0;
    color: #4bad48;
}

.btn-style-light.btn-success:hover, .btn-style-light.btn-success.focus, .btn-style-light.btn-success:focus, .btn-style-light.btn-success:not(:disabled):not(.disabled).active, .btn-style-light.btn-success:not(:disabled):not(.disabled):active, .show>.btn-style-light.btn-success.dropdown-toggle {
    color: #4bad48;
    background-color: #cfffe4;
}

.btn-style-light.btn-danger, .btn-style-light.btn-danger.disabled, .btn-style-light.btn-danger:disabled {
    background: #fce3e5;
    color: #FF4857;
}

.btn-style-light.btn-danger:hover, .btn-style-light.btn-danger.focus, .btn-style-light.btn-danger:focus, .btn-style-light.btn-danger:not(:disabled):not(.disabled).active, .btn-style-light.btn-danger:not(:disabled):not(.disabled):active, .show>.btn-style-light.btn-danger.dropdown-toggle {
    color: #FF4857;
    background-color: #ffc7cc;
}

.btn-style-light.btn-warning, .btn-style-light.btn-warning.disabled, .btn-style-light.btn-warning:disabled {
    background: #fff3e3;
    color: #ff9500;
}

.btn-style-light.btn-warning:hover, .btn-style-light.btn-warning.focus, .btn-style-light.btn-warning:focus, .btn-style-light.btn-warning:not(:disabled):not(.disabled).active, .btn-style-light.btn-warning:not(:disabled):not(.disabled):active, .show>.btn-style-light.btn-warning.dropdown-toggle {
    color: #ff9500;
    background-color: #ffe7c7;
}

.btn-style-light.btn-info, .btn-style-light.btn-info.disabled, .btn-style-light.btn-info:disabled {
    background: #e0efff;
    color: #61acfc;
}

.btn-style-light.btn-info:hover, .btn-style-light.btn-info.focus, .btn-style-light.btn-info:focus, .btn-style-light.btn-info:not(:disabled):not(.disabled).active, .btn-style-light.btn-primary:not(:disabled):not(.disabled):active, .show>.btn-style-light.btn-primary.dropdown-toggle {
    color: #61acfc;
    background-color: #c2dfff;
}

.btn-style-light.btn-dark, .btn-style-light.btn-dark.disabled, .btn-style-light.btn-dark:disabled {
    background: #d4d4d4;
    color: #40475c;
}

.btn-style-light.btn-dark:hover, .btn-style-light.btn-dark.focus, .btn-style-light.btn-dark:focus, .btn-style-light.btn-dark:not(:disabled):not(.disabled).active, .btn-style-light.btn-dark:not(:disabled):not(.disabled):active, .show>.btn-style-light.btn-dark.dropdown-toggle {
    color: #40475c;
    background-color: #c2c2c2;
}

.btn-outline-primary, .btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #2269F5;
    background-color: transparent;
    border-color: #2269F5;
}

.btn-outline-primary:hover, .btn-outline-primary.focus, .btn-outline-primary:focus, .btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active, .show>.btn-outline-primary.dropdown-toggle, .btn-check:active+.btn-outline-primary, .btn-check:checked+.btn-outline-primary, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show, .btn-outline-primary:active {
    color: #fff;
    background-color: #2269F5;
    border-color: #2269F5;
}

.btn-outline-secondary, .btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #4d546b;
    background-color: transparent;
    border-color: #FCEACE;
}

.btn-outline-secondary:hover, .btn-outline-secondary.focus, .btn-outline-secondary:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show>.btn-outline-secondary.dropdown-toggle, .btn-check:active+.btn-outline-secondary, .btn-check:checked+.btn-outline-secondary, .btn-outline-secondary.active, .btn-outline-secondary.dropdown-toggle.show, .btn-outline-secondary:active {
    color: #4d546b;
    background-color: #FCEACE;
    border-color: #FCEACE;
}

.btn-outline-success, .btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #4bad48;
    background-color: transparent;
    border-color: #4bad48;
}

.btn-outline-success:hover, .btn-outline-success.focus, .btn-outline-success:focus, .btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active, .show>.btn-outline-success.dropdown-toggle, .btn-check:active+.btn-outline-success, .btn-check:checked+.btn-outline-success, .btn-outline-success.active, .btn-outline-success.dropdown-toggle.show, .btn-outline-success:active {
    color: #fff;
    background-color: #4bad48;
    border-color: #4bad48;
}

.btn-outline-danger, .btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #FF4857;
    background-color: transparent;
    border-color: #FF4857;
}

.btn-outline-danger:hover, .btn-outline-danger.focus, .btn-outline-danger:focus, .btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active, .show>.btn-outline-danger.dropdown-toggle, .btn-check:active+.btn-outline-danger, .btn-check:checked+.btn-outline-danger, .btn-outline-danger.active, .btn-outline-danger.dropdown-toggle.show, .btn-outline-danger:active {
    color: #fff;
    background-color: #FF4857;
    border-color: #FF4857;
}

.btn-outline-warning, .btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #ff9500;
    background-color: transparent;
    border-color: #ff9500;
}

.btn-outline-warning:hover, .btn-outline-warning.focus, .btn-outline-warning:focus, .btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active, .show>.btn-outline-warning.dropdown-toggle, .btn-check:active+.btn-outline-warning, .btn-check:checked+.btn-outline-warning, .btn-outline-warning.active, .btn-outline-warning.dropdown-toggle.show, .btn-outline-warning:active {
    color: #fff;
    background-color: #ff9500;
    border-color: #ff9500;
}

.btn-outline-info, .btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #61acfc;
    background-color: transparent;
    border-color: #61acfc;
}

.btn-outline-info:hover, .btn-outline-info.focus, .btn-outline-info:focus, .btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active, .show>.btn-outline-info.dropdown-toggle, .btn-check:active+.btn-outline-info, .btn-check:checked+.btn-outline-info, .btn-outline-info.active, .btn-outline-info.dropdown-toggle.show, .btn-outline-info:active {
    color: #fff;
    background-color: #61acfc;
    border-color: #61acfc;
}

.btn-outline-light, .btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #F4F7FA;
    background-color: transparent;
    border-color: #F4F7FA;
}

.btn-outline-light:hover, .btn-outline-light.focus, .btn-outline-light:focus, .btn-outline-light:not(:disabled):not(.disabled).active, .btn-outline-light:not(:disabled):not(.disabled):active, .show>.btn-outline-light.dropdown-toggle, .btn-check:active+.btn-outline-light, .btn-check:checked+.btn-outline-light, .btn-outline-light.active, .btn-outline-light.dropdown-toggle.show, .btn-outline-light:active {
    color: #4d546b;
    background-color: #F4F7FA;
    border-color: #F4F7FA;
}

.btn-outline-dark, .btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #40475c;
    background-color: transparent;
    border-color: #40475c;
}

.btn-outline-dark:hover, .btn-outline-dark.focus, .btn-outline-dark:focus, .btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):active, .show>.btn-outline-dark.dropdown-toggle, .btn-check:active+.btn-outline-dark, .btn-check:checked+.btn-outline-dark, .btn-outline-dark.active, .btn-outline-dark.dropdown-toggle.show, .btn-outline-dark:active {
    color: #fff;
    background-color: #40475c;
    border-color: #40475c;
}

.btn-burger {
    width: 55px;
    height: 55px;
    line-height: 55px!important;
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%), 0 2px 10px 0 rgb(0 0 0 / 12%);
    position: relative;
    overflow: hidden;
    z-index: 1;
    padding: 0;
    border-radius: 50%;
    cursor: pointer;
    vertical-align: middle!important;
}

.btn-burger:focus, .btn-burger.active, .btn-burger:active {
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%), 0 2px 10px 0 rgb(0 0 0 / 12%)!important;
}

.btn-burger i {
    vertical-align: middle!important;
    line-height: 55px!important;
    margin: 0!important;
    font-size: 24px!important;
}

/* Images */

.img-thumbnail {
    border-radius: 5px;
    padding: 5px;
}

/* Pagination */

.pagination {
    margin-bottom: 0;
}

.page-link, .page-item.disabled .page-link {
    background: #F4F7FA;
    color: #4d546b;
    border-color: #e3e6eb;
    padding: 8px 20px;
}

.page-item.active .page-link {
    z-index: 3;
    color: #2269f4;
    font-weight: 500;
    background-color: #fff;
    border-color: #e3e6eb;
}

.page-item.disabled .page-link {
    pointer-events: none;
    opacity: .65;
}

.page-link:focus, .page-link.active, .page-link:active {
    color: #293442;
    font-weight: 500;
    box-shadow: none;
    background: #f7fafc;
}

.page-link:hover {
    background: #f7fafc;
    color: #293442;
}

.page-item:first-child .page-link {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.page-item:last-child .page-link {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.pagination-separated .page-link {
    border-radius: 20px!important;
    margin-right: 5px;
}

.pagination-separated .page-item:last-child .page-link {
    margin-right: 0px;
}

.pagination-outline .page-link {
    background: transparent;
}

.pagination-outline .page-link:hover {
    background: #f7fafc;
    color: #293442;
}

/* Popovers */

.popover {
    border-radius: 10px;
    border-color: #e3e6eb;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.bs-popover-auto[data-popper-placement^=right]>.popover-arrow::before, .bs-popover-end>.popover-arrow::before {
    border-right-color: #e3e6eb;
}

.bs-popover-auto[data-popper-placement^=left]>.popover-arrow::before, .bs-popover-start>.popover-arrow::before {
    border-left-color: #e3e6eb;
}

.bs-popover-auto[data-popper-placement^=bottom]>.popover-arrow::before, .bs-popover-bottom>.popover-arrow::before {
    border-bottom-color: #e3e6eb;
}

.bs-popover-auto[data-popper-placement^=top]>.popover-arrow::before, .bs-popover-top>.popover-arrow::before {
    border-top-color: #e3e6eb;
}

.popover-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: #F1F4F7;
    padding: 12px 20px;
    color: #27313F;
    border-color: #e3e6eb;
}

.popover-body {
    padding: 12px 20px;
    color: #4d546b;
    font-size: 14px;
}

/* Progress */

.progress {
    border-radius: 5px;
}

.progress-bar {
    background-color: #2269F4;
}

/* Spinners */

.btn .spinner-border, .btn .spinner-grow {
    vertical-align: middle;
}

/* Toasts */

.toast {
    border-radius: 10px;
    border-color: #e1e7ed;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.toast-header {
    color: #293442;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 12px 16px;
    border-color: #e1e7ed;
}

.toast-body {
    padding: 12px 16px;
}

/*------------------------------------------------------------
[5. Components]
------------------------------------------------------------*/

/* Accordions */

.accordion-item {
    border-color: #e1e7ed;
}

.accordion-item .accordion-button {
    padding: 14px 20px;
}

.accordion-item .accordion-body {
    padding: 14px 20px;
}

.accordion-item .accordion-button, .accordion-item .accordion-button:active, .accordion-item .accordion-button:focus {
    box-shadow: none!important;
    border-color: #e1e7ed;
}

.accordion-item .accordion-button:not(.collapsed) {
    background-color: #f5fbff;
    font-weight: 500;
    color: #2269F4;
    border-bottom: 1px solid #e1e7ed;
}

.accordion-item .accordion-button.collapsed {
    background-color: #F4F7FA;
}

.accordion-item .accordion-button.collapsed:hover {
    background: #f7fafc;
}

.accordion-item:first-of-type, .accordion-item:first-of-type .accordion-button {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.accordion-item:last-of-type, .accordion-item:last-of-type .accordion-button.collapsed {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.accordion-flush .accordion-item .accordion-button.collapsed {
    background: #fff;
}

.accordion-separated .accordion-item, .accordion-separated .accordion-item .accordion-button.collapsed {
    border-radius: 10px;
}

.accordion-separated .accordion-item .accordion-button {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.accordion-separated .accordion-item:not(:last-of-type) {
    margin-bottom: 10px;
}

.accordion-item .accordion-button i {
    margin-right: 10px;
}

.accordion-item .accordion-button.collapsed i {
    filter: invert(24%) sepia(38%) saturate(342%) hue-rotate(186deg) brightness(95%) contrast(88%);
}

.accordion-item .accordion-button i {
    filter: invert(34%) sepia(23%) saturate(3309%) hue-rotate(195deg) brightness(109%) contrast(115%)
}

/* BlockUI */

.blockUI.blockOverlay {
    border-radius: inherit;
    background: rgba(255, 255, 255)!important;
    opacity: .8!important;
    z-index: 9999!important;
}

.blockUI.blockMsg {
    background: transparent!important;
    width: 25px!important;
    border: 0!important;
    height: 25px!important;
    left: calc(50% - 12.5px)!important;
    top: calc(50% - 12.5px)!important;
    z-index: 99999!important;
}

/*------------------------------------------------------------
[6. Charts]
------------------------------------------------------------*/

/* ApexCharts */

.apexcharts-menu {
    display: block;
    visibility: hidden;
    transform: translateY(10px);
    opacity: 0!important;
    border: none!important;
    border-radius: 10px!important;
    padding: 10px!important;
    min-width: 140px!important;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.apexcharts-menu.apexcharts-menu-open {
    opacity: 1!important;
    transform: none;
    visibility: visible;
}

.apexcharts-menu .apexcharts-menu-item {
    text-align: left!important;
    font-size: 13px;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.apexcharts-menu .apexcharts-menu-item:hover {
    background-color: transparent!important;
    color: #2269F5;
}

.apexcharts-canvas text {
    color: #4d546b;
    font-weight: 500;
}

/* Cards */

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.card-img, .card-img-top {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.card .card-header {
    background: transparent;
    border: none;
    padding: 25px 30px 10px;
    font-size: 14px;
    color: #293442;
    margin: 0;
    font-weight: 600;
}

.card .card-title {
    font-size: 15px;
    color: #293442;
    margin: 0;
    font-weight: 600;
}

.card .card-title .badge {
    margin-left: 10px;
    font-size: 10px;
    vertical-align: middle;
    float: right;
}

.card .card-body .card-title {
    margin-bottom: 10px;
}

.card .card-body {
    padding: 25px 30px;
}

.card .card-header+.card-body {
    padding-top: 7px;
}

.card .card-description {
    margin-bottom: 20px;
}

.card .card-description i {
    vertical-align: text-bottom;
}

.card>.list-group:last-child {
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
}

.card>.list-group:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.card-footer {
    border-color: #e3e6ea;
    background: #f4f7fa;
    padding: 15px 30px;
}

.card-footer:last-child {
    border-radius: 0 0 10px 10px;
}

.card-img-overlay {
    padding: 25px 30px;
    border-radius: 10px;
    background: rgba(0, 0, 0, .25);
}

.card-img, .card-img-bottom {
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
}

.card-header-tabs {
    margin: -25px -30px 0;
    border-bottom: 1px solid #e1e7eb;
}

.card-header a {
    font-weight: 500;
}

/* CountDown */

.countdown-container {
    text-align: center;
}

.countdown-container .countdown {
    clear: both;
    display: block;
    font-size: 24px;
    color: #364459;
    margin: 20px 0 0;
    font-weight: 500;
}

.countdown-container .countdown .countdown-block-container {
    display: inline-block;
    padding: 0 10px;
    position: relative;
    min-width: 100px;
}

.countdown-container .countdown .countdown-block-container:not(:last-child)::after {
    content: ':';
    display: inline-block;
    font-size: 24px;
    position: absolute;
    right: 0px;
    top: 0;
    line-height: 40px;
}

.countdown-container .countdown .countdown-block-container h4 {
    color: #7884ad;
    font-size: 16px;
}

/* LightBox */

.lightbox-example a img {
    max-width: 200px;
    margin-bottom: 10px;
    margin-right: 10px;
}

/* Lists */

.list-group-item {
    border-color: #e1e7ec;
    color: #4d546b;
    padding: 12px 20px;
}

.list-group-item:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.list-group-item:last-child {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.list-group-item.active {
    font-weight: 500;
    background: #DFF0FE;
    color: #2269F5;
    border-color: #e1e7ec;
}

.list-group-flush>.list-group-item {
    padding: 15px 30px;
}

.list-group-item.list-group-item-primary {
    background: #DFF0FE;
    color: #2269F5;
}

.list-group-item.list-group-item-secondary {
    background: #FCEACE;
    color: #4d546b;
}

.list-group-item.list-group-item-success {
    background: #e6fcf0;
    color: #4bad48;
}

.list-group-item.list-group-item-danger {
    background: #fce3e5;
    color: #FF4857;
}

.list-group-item.list-group-item-warning {
    background: #fff3e3;
    color: #ff9500;
}

.list-group-item.list-group-item-info {
    background: #e0efff;
    color: #61acfc;
}

.list-group-item.list-group-item-light {
    background: #F4F7FA;
    color: #4d546b;
}

.list-group-item.list-group-item-dark {
    color: #40475c;
}

/* Modals */

body.modal-open {
    overflow: visible;
}

.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.modal-header {
    border-bottom: 0;
    padding: 25px 30px 10px;
}

.modal-title {
    font-size: 16px;
    color: #293442;
    margin: 0;
    font-weight: 600;
}

.modal-body {
    padding: 25px 30px;
}

.modal-footer {
    padding: 15px 30px;
    border: 0;
}

.modal-header .btn-close {
    padding: 10px;
    margin: -10px -10px -10px auto;
}

/* Tabs */

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    border-top: none;
    border-left: none;
    border-right: none;
    background: none;
    color: #27323f;
    border-bottom: 2px solid #2269f4;
    opacity: 1;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
    border-top: none;
    border-left: none;
    border-right: none;
    opacity: 1;
}

.nav-tabs .nav-link {
    opacity: 0.5;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    padding: 12px 20px;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.nav-pills .nav-link {
    padding: 8px 16px;
    border-radius: 5px;
    opacity: 0.5;
    font-weight: 500;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
    opacity: 1;
    background: #DFF0FE;
    color: #2269F5;
}

.nav-pills .nav-link:focus, .nav-pills .nav-link:hover {
    opacity: 1;
}

/* Pricing Tables */

.pricing-basic .plan-title {
    font-size: 14px;
    text-transform: uppercase;
    color: #27323f;
}

.pricing-basic .plan-title .badge {
    vertical-align: middle;
    float: right;
}

.pricing-basic .plan-price {
    margin-top: 20px;
}

.pricing-basic .plan-price .plan-price-value {
    font-size: 42px;
    color: #27313F;
    font-weight: bold;
    line-height: 42px;
}

.pricing-basic.pricing-selected .plan-price-value {
    color: #2269F5;
}

.pricing-basic .plan-price .plan-price-period {
    vertical-align: top;
    line-height: 42px;
    opacity: 0.5;
    margin-left: 10px;
}

.pricing-basic .plan-description {
    margin-top: 20px;
    display: block;
    opacity: 0.7;
}

.pricing-basic .plan-list {
    list-style-type: none;
    padding-left: 0;
    margin-top: 15px;
    margin-bottom: 0;
}

.pricing-basic .plan-list li {
    padding: 10px 10px 10px 36px;
    position: relative;
    color: #27323f;
}

.pricing-basic .plan-list li::before {
    display: inline-block;
    content: '\e86c';
    font-family: 'Material Icons Two Tone';
    font-size: 22px;
    left: 0;
    position: absolute;
    line-height: 22px;
    filter: invert(24%) sepia(38%) saturate(342%) hue-rotate(186deg) brightness(95%) contrast(88%);
}

.pricing-basic.pricing-selected .plan-list li::before {
    filter: invert(34%) sepia(23%) saturate(3309%) hue-rotate(195deg) brightness(109%) contrast(115%);
}

/*------------------------------------------------------------
[5. Widgets]
------------------------------------------------------------*/

/* List Widgets */

.widget-list .widget-list-content {
    margin: 0;
}

.widget-list .widget-list-content .widget-list-item {
    padding: 12px 0;
    display: block;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-check {
    vertical-align: middle;
    width: 30px;
    height: 45px;
    padding: 12.5px 5px 12.5px 0;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-check .form-check {
    margin: 0;
    vertical-align: middle;
    padding-left: 21px;
    padding-top: 0;
    padding-bottom: 0;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-check .form-check .form-check-input {
    height: 20px;
    width: 20px;
    margin-top: 0;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-icon {
    width: 45px;
    height: 45px;
    background: #F4F7FA;
    border-radius: 10px;
    text-align: center;
    margin-right: 10px;
    vertical-align: middle;
    float: left;
}

.widget-list .widget-list-content .widget-list-item.widget-list-item-green .widget-list-item-icon {
    background: #e6fcf0;
    color: #4bad48;
}

.widget-list .widget-list-content .widget-list-item.widget-list-item-blue .widget-list-item-icon {
    background: #DFF0FE;
    color: #2269F5;
}

.widget-list .widget-list-content .widget-list-item.widget-list-item-red .widget-list-item-icon {
    background: #fce3e5;
    color: #FF4857;
}

.widget-list .widget-list-content .widget-list-item.widget-list-item-yellow .widget-list-item-icon {
    background: #fff3e3;
    color: #ff9500;
}

.widget-list .widget-list-content .widget-list-item.widget-list-item-purple .widget-list-item-icon {
    background: #e3e3ff;
    color: #6363ff;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-icon i {
    line-height: 45px;
    font-size: 22px;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-avatar {
    width: 45px;
    height: 45px;
    margin-right: 10px;
    vertical-align: middle;
    float: left;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-avatar .avatar {
    width: 45px;
    height: 45px;
    line-height: 45px;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-description {
    flex: 1;
    padding-left: 10px;
    overflow: hidden;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-check+.widget-list-item-description {
    width: calc(100% - 45px);
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-avatar+.widget-list-item-description {
    width: calc(100% - 55px);
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-description .widget-list-item-description-title {
    display: block;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    color: #13171f;
    text-decoration: none;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-description .widget-list-item-description-title:hover {
    color: #037AFB;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-description .widget-list-item-description-subtitle {
    color: #919ba7;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-description .widget-list-item-description-progress {
    margin: 7px 0;
    display: block;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-description .widget-list-item-description-progress .progress {
    height: 5px;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-transaction-amount-positive, .widget-list .widget-list-content .widget-list-item .widget-list-item-transaction-amount-negative {
    padding-left: 15px;
    font-size: 14px;
    font-weight: 600;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-transaction-amount-positive {
    color: #4bad48;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-transaction-amount-negative {
    color: #FF4857;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-description-date {
    font-size: 13px;
    color: #919ba7;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-icon.widget-list-item-icon-large {
    width: 120px;
    height: 70px;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-icon .widget-list-item-icon-image {
    width: 100%;
    height: 100%;
    background-size: cover!important;
    border-radius: 10px;
    background-position: center center!important;
}

.widget-list .widget-list-content .widget-list-item .widget-list-item-product-amount {
    font-size: 16px;
    font-weight: bold;
    color: #404a5d;
}

.widget-list .widget-list-content .widget-list-item-blog-post .widget-list-item-description-content {
    display: -webkit-box;
    max-width: 100%;
    margin: 0 auto;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #919ba7;
}

/* Newsletter subscription widget */

.widget-newsletter h5 {
    text-align: center;
    font-size: 15px;
    display: block;
}

.widget-newsletter p {
    margin: 8px 0;
    display: block;
    text-align: center;
    font-size: 13px;
    opacity: 0.7;
}

/* File List Widget */

.widget-files .widget-files-list {
    padding-left: 0;
    margin: 0;
}

.widget-files .widget-files-list .widget-files-list-item {
    padding: 10px 10px;
    border-radius: 10px;
    background: #f4f7fa;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.widget-files .widget-files-list .widget-files-list-item:last-child {
    margin-bottom: 0;
}

.widget-files .widget-files-list .widget-files-list-item .widget-files-list-item-icon {
    display: block;
    width: 45px;
    height: 45px;
    text-align: center;
    margin-right: 10px;
}

.widget-files .widget-files-list .widget-files-list-item .widget-files-list-item-icon i {
    line-height: 45px;
    font-size: 18px;
    color: #037AFB;
}

.widget-files .widget-files-list .widget-files-list-item .widget-files-list-item-content {
    flex: 1;
}

.widget-files .widget-files-list .widget-files-list-item .widget-files-list-item-content .widget-files-list-item-title {
    display: block;
    font-size: 13px;
    font-weight: 500;
}

.widget-files .widget-files-list .widget-files-list-item .widget-files-list-item-content .widget-files-list-item-size {
    font-size: 13px;
    opacity: 0.7;
}

.widget-files .widget-files-list .widget-files-list-item .widget-files-list-item-download-btn {
    margin: 0 10px;
}

.widget-files .widget-files-list .widget-files-list-item .widget-files-list-item-download-btn i {
    font-size: 18px;
}

/* Info Widget */

.widget-info .widget-info-container {
    text-align: center;
}

.widget-info .widget-info-container .widget-info-image {
    width: 100%;
    height: 150px;
    background-size: 150px!important;
    background-position: center center!important;
    background-repeat: no-repeat!important;
}

.widget-info .widget-info-container .widget-info-title {
    margin: 20px 0;
    color: #27323f;
    font-weight: 600;
}

.widget-info .widget-info-container .widget-info-text {
    text-align: center;
    opacity: 0.7;
}

.widget-info .widget-info-container .widget-info-action {
    margin: 20px 0 10px;
    display: inline-block;
}

/* Inline Info Widget */

.widget-info-inline .widget-info-container .widget-info-image {
    width: 60%;
    height: 60%;
    position: absolute;
    right: 20px;
    bottom: 20px;
    background-size: contain!important;
    background-repeat: no-repeat!important;
    background-position: right bottom!important;
    z-index: 1;
}

.widget-info-inline .widget-info-container .widget-info-text {
    max-width: 50%;
    opacity: 0.7;
}

/* Popular Product Widget */

.widget-popular-product .widget-popular-product-container .widget-popular-product-image {
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.widget-popular-product .widget-popular-product-container .widget-popular-product-image img {
    width: 100%;
}

.widget-popular-product .widget-popular-product-container .widget-popular-product-tags {
    padding: 10px 0;
}

.widget-popular-product .widget-popular-product-container .widget-popular-product-content .widget-popular-product-title {
    padding: 5px 0 6px;
    font-size: 18px;
    color: #27323f;
    text-decoration: none;
    font-weight: 700;
    display: block;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.widget-popular-product .widget-popular-product-container .widget-popular-product-content .widget-popular-product-title:hover {
    opacity: 0.6;
}

.widget-popular-product .widget-popular-product-container .widget-popular-product-content .widget-popular-product-text {
    color: #919ba7;
}

.widget-popular-product .widget-popular-product-container .widget-popular-product-content .widget-popular-product-rating i {
    font-size: 18px;
    color: #FED548;
    vertical-align: middle;
}

.widget-popular-product .widget-popular-product-container .widget-popular-product-content .widget-popular-product-rating .widget-popular-product-rating-num {
    vertical-align: middle;
    margin-left: 5px;
    color: #919ba7;
}

/* Popular Blog Widget */

.widget-popular-blog .widget-popular-blog-container {
    display: flex;
}

.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-image {
    width: 120px;
    border-radius: 10px;
}

.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-image img {
    width: 100%;
    border-radius: 10px;
}

.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-content {
    flex: 1;
    overflow: hidden;
}

.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-title, .widget-popular-blog .widget-popular-blog-container .widget-popular-blog-text {
    display: -webkit-box;
    max-width: 100%;
    margin: 0 auto;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-title {
    font-weight: 500;
    font-size: 15px;
    margin-bottom: 5px;
    -webkit-line-clamp: 2;
}

.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-text {
    -webkit-line-clamp: 4;
    color: #919ba7;
}

.widget-popular-blog .widget-popular-blog-date {
    color: #919ba7;
    line-height: 35px;
}

/* Action List Widget */

.widget-action-list .widget-action-list-container .widget-action-list-item {
    flex: 1;
    text-align: center;
}

.widget-action-list .widget-action-list-container .widget-action-list-item a {
    display: inline-block;
    align-self: center;
    text-align: center;
    text-decoration: none;
    padding: 9px 0;
}

.widget-action-list .widget-action-list-container .widget-action-list-item a .widget-action-list-item-icon {
    display: block;
    width: 80px;
    height: 80px;
    border-radius: 40px;
    background: #f4f7fa;
    border: 1px solid #e3e6ea;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.widget-action-list .widget-action-list-container .widget-action-list-item a .widget-action-list-item-icon i {
    line-height: 80px;
    font-size: 28px;
    color: #1f2433;
}

.widget-action-list .widget-action-list-container .widget-action-list-item a .widget-action-list-item-title {
    display: block;
    color: #4d546b;
    font-weight: 500;
    margin-top: 15px;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.widget-action-list .widget-action-list-container .widget-action-list-item a:hover .widget-action-list-item-icon {
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.widget-action-list .widget-action-list-container .widget-action-list-item a:hover .widget-action-list-item-title {
    color: #3a4055;
}

/* Tweet Widget */

.widget-tweet {
    background: #1FA1F0;
    overflow: hidden;
}

.widget-tweet:after {
    content: "";
    background: transparent url(../images/icons/twitter.svg) 0 0 no-repeat;
    position: absolute;
    right: 20px;
    height: 150px;
    width: 150px;
    z-index: 1;
    bottom: -40px;
    background-size: cover;
    opacity: 0.15;
    filter: invert(99%) sepia(77%) saturate(97%) hue-rotate( 281deg) brightness(115%) contrast(100%);
}

.widget-tweet .widget-tweet-container .widget-tweet-content .widget-tweet-text {
    color: #fff;
    max-width: 85%;
    font-size: 21px;
    margin-bottom: 0;
}

.widget-tweet .widget-tweet-container .widget-tweet-content .widget-tweet-author {
    color: #fff;
    margin-top: 10px;
    font-size: 16px;
    opacity: 0.7;
    margin-bottom: 0px;
}

/* Connection Request Widget */

.widget-connection-request .widget-connection-request-container {
    margin-bottom: 15px;
}

.widget-connection-request .widget-connection-request-container .widget-connection-request-avatar {
    margin-right: 10px;
}

.widget-connection-request .widget-connection-request-container .widget-connection-request-info .widget-connection-request-info-name {
    display: block;
    font-weight: 500;
    font-size: 15px;
    line-height: 30px;
}

.widget-connection-request .widget-connection-request-container .widget-connection-request-info .widget-connection-request-info-count,
.widget-connection-request .widget-connection-request-container .widget-connection-request-info .widget-connection-request-info-about {
    display: block;
    font-size: 13px;
    color: #919ba7;
}

/* Bank Card Widget */

.widget-bank-card {
    height: 213px;
}
.widget-bank-card .card-body {
    padding: 0;
}
.widget-bank-card .widget-bank-card-container {
    height: 100%;
    width: 100%;
    padding: 25px 30px;
    border-radius: 10px;
}

.widget-bank-card .widget-bank-card-visa {
    background: #21336e;
}

.widget-bank-card .widget-bank-card-visa::after {
    position: absolute;
    bottom: 5px;
    right: 30px;
    content: '';
    display: block;
    height: 80px;
    width: 80px;
    background: url(../images/other/visa.svg) no-repeat;
    background-size: 80px 80px;
    background-position: bottom right;
    filter: invert(41%) sepia(6%) saturate(4020%) hue-rotate(189deg) brightness(87%) contrast(90%);
}

.widget-bank-card .widget-bank-card-container .widget-bank-card-logo {
    position: absolute;
    right: 30px;
    top: 25px;
}

.widget-bank-card .widget-bank-card-visa .widget-bank-card-logo {
    filter: invert(41%) sepia(6%) saturate(4020%) hue-rotate(189deg) brightness(87%) contrast(90%);
    background: url('../images/other/bank_of_canada.png') no-repeat;
    background-size: contain;
    width: 80px;
    height: 50px;
}

.widget-bank-card .widget-bank-card-container .widget-bank-card-balance-title {
    display: block;
    font-size: 14px;
    font-weight: 500;
    opacity: 0.7;
}

.widget-bank-card .widget-bank-card-container .widget-bank-card-balance {
    font-size: 30px;
    font-weight: 600;
    letter-spacing: 2px;
}

.widget-bank-card .widget-bank-card-container .widget-bank-card-number {
    font-size: 18px;
    font-weight: 500;
    line-height: 30px;
    word-spacing: 8px;
}

.widget-bank-card .widget-bank-card-visa .widget-bank-card-balance-title,
.widget-bank-card .widget-bank-card-visa .widget-bank-card-balance,
.widget-bank-card .widget-bank-card-visa .widget-bank-card-number {
    color: #fff;
}

.widget-bank-card .widget-bank-card-mastercard::after {
    position: absolute;
    bottom: 20px;
    right: 30px;
    content: '';
    display: block;
    height: 50px;
    width: 80px;
    background: url(../images/other/mastercard.png) no-repeat;
    background-size: 80px 50px;
    background-position: bottom right;
}

.widget-bank-card .widget-bank-card-mastercard .widget-bank-card-logo {
    background: url('../images/other/bank_of_america.png') no-repeat;
    background-size: contain;
    width: 150px;
    height: 20px;
}

/* Stats Widget */

.widget-stats .widget-stats-container .widget-stats-icon {
    width: 60px;
    height: 60px;
    display: block;
    text-align: center;
    margin-right: 20px;
    background: #f4f7fa;
    border-radius: 10px;
}

.widget-stats .widget-stats-container .widget-stats-icon i {
    line-height: 60px;
    font-size: 28px;
}

.widget-stats .widget-stats-container .widget-stats-icon.widget-stats-icon-primary {
    background: #DFF0FE;
    color: #2269F5;
}

.widget-stats .widget-stats-container .widget-stats-icon.widget-stats-icon-warning {
    background: #fff3e3;
    color: #ff9500;
}

.widget-stats .widget-stats-container .widget-stats-icon.widget-stats-icon-danger {
    background: #fce3e5;
    color: #FF4857;
}

.widget-stats .widget-stats-container .widget-stats-icon.widget-stats-icon-success {
    background: #e6fcf0;
    color: #4bad48;
}

.widget-stats .widget-stats-container .widget-stats-icon.widget-stats-icon-purple {
    background: #EDEAFD;
    color: #562CF5;
}

.widget-stats .widget-stats-container .widget-stats-content .widget-stats-title,
.widget-stats .widget-stats-container .widget-stats-content .widget-stats-amount,
.widget-stats .widget-stats-container .widget-stats-content .widget-stats-info {
    display: block;
}

.widget-stats .widget-stats-container .widget-stats-indicator {
    padding: 5px 9px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 12px;
    background: #F4F7FA;
    color: #3F485B;
}

.widget-stats .widget-stats-container .widget-stats-indicator.widget-stats-indicator-positive {
    background: #d6f5e3;
    color: #4bad48;
}

.widget-stats .widget-stats-container .widget-stats-indicator.widget-stats-indicator-negative {
    background: #fce3e5;
    color: #FF4857;
}

.widget-stats .widget-stats-container .widget-stats-indicator i {
    vertical-align: text-bottom;
    font-size: 18px;
}

.widget-stats .widget-stats-container .widget-stats-content .widget-stats-title {
    text-transform: uppercase;
    font-weight: 500;
    color: #a1a5b5;
    font-size: 13px;
}

.widget-stats .widget-stats-container .widget-stats-content .widget-stats-amount {
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 2px;
    color: #27323f;
}

.widget-stats .widget-stats-container .widget-stats-content .widget-stats-info {
    color: #a1a5b5;
    font-size: 13px;
}

.widget-stats .widget-stats-progress {
    padding-top: 15px;
}

.widget-stats .widget-stats-progress .progress {
    height: 5px;
    margin-bottom: 0;
}

.widget-stats .widget-stats-chart {
    margin: 10px -30px -25px;
}

/* Large Stats Widget */

.widget-stats-large .widget-stats-large-chart-container {
    border-right: 1px solid #e2e6e9;
    margin: 0 -10.5px 0 -1px;
}

.widget-stats-large .widget-stats-large-info-container {
    background: #f4f7fa;
    margin: 0 -1px 0 -10.5px;
    border-radius: 0 10px 10px 0;
    height: 100%;
}

@media (max-width: 1200px) {
    .widget-stats-large .widget-stats-large-info-container {
        margin: 0 -1px 0 0;
        border-radius: 0 0 10px 10px;
    }
}

/* Info Navigation Widget */

.widget-info-navigation .widget-info-navigation-container {
    display: flex;
    align-items: center;
}

.widget-info-navigation .widget-info-navigation-container .widget-info-navigation-content {
    flex: 1;
}

/* Payment Request Widget */


.widget-payment-request .widget-payment-request-container .widget-payment-request-author,
.widget-payment-request .widget-payment-request-container .widget-payment-request-product {
    display: flex;
    align-items: center;
    background: #f4f7fa;
    padding: 10px 30px;
    margin: 0 -30px;
    border-top: 1px solid #e2e6e9;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-product {
    border-bottom: 1px solid #e2e6e9;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-author .widget-payment-request-author-info,
.widget-payment-request .widget-payment-request-container .widget-payment-request-product .widget-payment-request-product-info {
    flex: 1;
    align-items: center;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-author .widget-payment-request-author-name,
.widget-payment-request .widget-payment-request-container .widget-payment-request-product .widget-payment-request-product-name {
    display: block;
    font-weight: 500;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-author .widget-payment-request-author-about,
.widget-payment-request .widget-payment-request-container .widget-payment-request-product .widget-payment-request-product-about {
    color: #919ba7;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-product .widget-payment-request-product-image {
    position: relative;
    width: 60px;
    height: 60px;
}
.widget-payment-request .widget-payment-request-container .widget-payment-request-product .widget-payment-request-product-image img {
    width: 100%;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-product .widget-payment-request-product-price {
    font-size: 17px;
    color: #4bad48;
    font-weight: bold;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-product .widget-payment-request-product-info-content {
    flex: 1;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-info .widget-payment-request-info-title {
    font-weight: 500;
    color: #27313F;
    font-size: 13px;
    text-transform: uppercase;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-info .widget-payment-request-info-item:not(:last-child) {
    padding-bottom: 13px;
    border-bottom: 1px solid #F4F7FA;
    margin-bottom: 13px;
}

.widget-payment-request .widget-payment-request-container .widget-payment-request-actions {
    padding-top: 20px;
    border-top: 1px solid #F4F7FA;
}

/* Chat Widget */

.widget-chat .widget-chat-active-user::after,
.widget-chat .widget-chat-inactive-user::after {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    margin-left: 10px;
    vertical-align: middle;
    display: inline-block;
    content: '';
}

.widget-chat .widget-chat-active-user::after {
    background: #4bad48;
}

.widget-chat .widget-chat-inactive-user::after {
    background: #FF4857;
}

.widget-chat .widget-chat-messages-container {
    margin-top: 7px;
    position: relative;
    padding: 20px 30px;
    border-top: 1px solid #E2E6E9;
    background: #F4F7FA;
    max-height: 395px;
    display: flex;
    flex-direction: column-reverse;
    overflow-y: auto;
}

.widget-chat .widget-chat-messages-container .widget-chat-message-item {
    display: flex;
    align-items: end;
    margin-bottom: 20px;
    
}

.widget-chat .widget-chat-messages-container .widget-chat-message-item img {
    width: 40px;
    height: 40px;
    border-radius: 25px;
    margin-right: 20px;
}

.widget-chat .widget-chat-messages-container .widget-chat-message-item .widget-chat-message-item-text {
    border-radius: 10px;
    padding: 15px 20px;
    background: #fff;
    box-shadow: 0px 0px 11px 1px rgb(0 0 0 / 5%);
    width: auto;
    display: table;
    margin-right: 30px;
}

.widget-chat .widget-chat-messages-container .widget-chat-message-item .widget-chat-message-item-text:not(:last-child) {
    margin-bottom: 7px;
}

.widget-chat .widget-chat-messages-container .widget-chat-message-item.own .widget-chat-message-item-text {
    background: #037AFB;
    color: #fff;
}

.widget-chat .widget-chat-compose {
    height: 70px;
    border-top: 1px solid #e2e6e9;
    position: relative;
    display: flex;
    align-items: center;
}

.widget-chat .widget-chat-compose .widget-chat-compose-input {
    border: none;
    height: 100%;
    padding: 0 30px;
    flex: 1;
    border-radius: 10px;
    color: #1f2433;
}

.widget-chat .widget-chat-compose .widget-chat-compose-input::placeholder {
    opacity: 0.5;
}

.widget-chat .widget-chat-compose .widget-chat-compose-input:focus {
    box-shadow: none;
    border: none;
    outline: 0;
}

.widget-chat .widget-chat-compose .widget-chat-compose-send-btn {
    background: none;
    border: none;
    box-shadow: none;
    text-align: center;
    padding: 0;
    margin: 0 15px;
    height: 40px;
    width: 40px;
    display: block;
}

.widget-chat .widget-chat-compose .widget-chat-compose-send-btn i {
    font-size: 17px;
    line-height: 40px;
    color: #037AFB;
}

/*------------------------------------------------------------
[6. Forms]
------------------------------------------------------------*/

/* Basic */

.form-label {
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
}

.form-control {
    border-radius: 8px;
    padding: 10px 18px;
    border-color: #e3e6ea;
    color: #24292e;
    -webkit-transition: border .2s ease-in-out, background-color .2s ease-in-out;
    -moz-transition: border .2s ease-in-out, background-color .2s ease-in-out;
    -o-transition: border .2s ease-in-out, background-color .2s ease-in-out;
    transition: border .2s ease-in-out, background-color .2s ease-in-out;
}

.form-control-sm {
    padding: 8px 14px;
    font-size: 12px;
}

.form-control-lg {
    padding: 12px 20px;
    font-size: 16px;
}

.form-control::-webkit-input-placeholder {
    color: #8e98a5;
}

.form-text {
    margin-top: 4px;
    font-size: 12px;
    color: #8e98a5;
}

.form-control:focus {
    border-color: rgba(34, 105, 245, 1);
    box-shadow: none;
}

.form-control.light-focus:focus {
    border-color: #ccd9e6;
}

.form-control.form-control-solid {
    border: none;
    background: #f4f7fa;
}

.form-control.form-control-solid:focus {
    background: #ebf0f5;
}

.form-control.form-control-solid-bordered {
    background: #f4f7fa;
}

.form-control.form-control-material {
    border-radius: 0;
    border-width: 0 0 1px;
}

.form-control.form-control-transparent {
    border-width: 0;
}

.form-control.form-control-rounded {
    border-radius: 20px;
}

.form-control:disabled, .form-control[readonly] {
    background-color: #e3e6ea;
}

/* Floating Labels */

.form-floating>label {
    padding: 19px 18px;
}

.form-floating>.form-control, .form-floating>.form-select {
    height: 60px;
    padding: 14px 18px;
}

/* Checks & Radios */

.form-check {
    min-height: 24px;
    padding-left: 28px;
    margin-bottom: 4px;
}

.form-check-input {
    width: 16px;
    height: 16px;
    border-color: #e3e6ea;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.form-check-input:focus {
    box-shadow: none;
    border-color: rgba(34, 105, 245, 0.6);
}

.form-check-input:checked {
    background-color: #2269F5;
    border-color: #2269F5;
}

.form-check-label {
    margin-left: 5px;
}

/* Input Groups */

.input-group-text {
    padding: 10px 14px;
    font-size: 13px;
    font-weight: 500;
    color: #24292e;
    background-color: #f4f7fa;
    border: 1px solid #e3e6ea;
    border-radius: 8px;
    position: relative;
}

.input-group-text.input-group-text-solid {
    border: none;
    background: #ebf0f5;
}

.input-group-text.input-group-text-material {
    border-radius: 0;
    border-width: 0 0 1px;
    background: transparent;
}

.input-group-text.input-group-text-transparent {
    border: none;
    background-color: transparent;
}

/* Range */

.form-range:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(34, 105, 245, 0.25);
}

.form-range:focus::-moz-range-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(34, 105, 245, 0.25);
}

.form-range:focus::-ms-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(34, 105, 245, 0.25);
}

.form-range::-webkit-slider-thumb {
    background-color: #2269F5;
}

.form-range::-webkit-slider-thumb:active {
    background-color: #75a3ff;
}

.form-range::-moz-range-thumb {
    background-color: #2269F5;
}

.form-range::-moz-range-thumb:active {
    background-color: #75a3ff;
}

.form-range::-ms-thumb {
    background-color: #2269F5;
}

.form-range::-ms-thumb:active {
    background-color: #75a3ff;
}

.form-range::-webkit-slider-runnable-track {
    background-color: #edf0f5;
}

/* Select */

.form-select {
    border-radius: 8px;
    padding: 10px 36px 10px 18px;
    border-color: #e3e6ea;
    color: #24292e;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.form-select:focus {
    border-color: rgba(34, 105, 245, 1);
    box-shadow: none;
}

/* Validation */

.form-control.is-valid, .was-validated .form-control:valid {
    border-color: #4bad48;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='24px' viewBox='0 0 24 24' width='24px' fill='%234bad48'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z'/%3E%3C/svg%3E");
}

.form-select.is-valid, .was-validated .form-select:valid {
    border-color: #4bad48;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='24px' viewBox='0 0 24 24' width='24px' fill='%234bad48'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z'/%3E%3C/svg%3E");
    background-position: right .75rem center, center right 2.25rem;
    background-size: 16px 12px, calc(.75em + .375rem) calc(.75em + .375rem);
}

.form-control.is-valid:focus, .was-validated .form-control:valid:focus, .form-select.is-valid:focus, .was-validated .form-select:valid:focus {
    border-color: #4bad48;
    box-shadow: none;
}

.valid-feedback {
    color: #4bad48;
    font-size: 12px;
}

.form-control.is-invalid, .was-validated .form-control:invalid {
    border-color: #FF4857;
    background-image: url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' height='24px' viewBox='0 0 24 24' width='24px' fill='%23FF4857'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E%0A");
}

.form-select.is-invalid, .was-validated .form-select:invalid {
    border-color: #FF4857;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%0A%3Csvg xmlns='http://www.w3.org/2000/svg' height='24px' viewBox='0 0 24 24' width='24px' fill='%23FF4857'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E%0A");
    background-position: right .75rem center, center right 2.25rem;
    background-size: 16px 12px, calc(.75em + .375rem) calc(.75em + .375rem);
}

.form-control.is-invalid:focus, .was-validated .form-control:invalid:focus, .form-select.is-invalid:focus, .was-validated .form-select:invalid:focus {
    border-color: #FF4857;
    box-shadow: none;
}

.invalid-feedback {
    color: #FF4857;
    font-size: 12px;
}

.form-check-input.is-valid:checked, .was-validated .form-check-input:valid:checked {
    background-color: #4bad48;
}

.form-check-input.is-valid:checked, .was-validated .form-check-input:valid:checked, .form-check-input.is-valid:focus, .was-validated .form-check-input:valid:focus {
    box-shadow: none;
    border-color: #4bad48;
}

.form-check-input.is-invalid:checked, .was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:focus, .was-validated .form-check-input:invalid:focus {
    box-shadow: none;
    border-color: #FF4857;
}

.form-check-input.is-valid~.form-check-label, .was-validated .form-check-input:valid~.form-check-label {
    color: #4bad48;
}

.form-check-input.is-invalid, .was-validated .form-check-input:invalid {
    border-color: #FF4857;
}

.form-check-input.is-invalid~.form-check-label, .was-validated .form-check-input:invalid~.form-check-label {
    color: #FF4857;
}

.valid-tooltip {
    background-color: rgba(89, 187, 86, .9);
}

.invalid-tooltip {
    background-color: rgba(255, 72, 87, .9);
}

/* Dropzone */

.dropzone {
    border-radius: 10px;
    background: #f4f7fa;
    border: 2px solid #e3e6ea;
}

.dropzone .dz-preview {
    background-color: transparent;
}

.dropzone .dz-preview .dz-image {
    border-radius: 10px;
}

.dropzone .dz-preview .dz-details {
    border-radius: 10px;
    background: #e3e6ea;
    bottom: 0;
}

.dropzone .dz-preview .dz-error-message {
    background: #ff4857;
}

.dropzone .dz-preview .dz-error-message:after {
    border-bottom-color: #ff4857;
}

/* SummerNote */

.note-editor {
    border-radius: 10px;
}

.note-editor.note-airframe, .note-editor.note-frame {
    border-color: #e2e6e9;
}

.note-editor.note-airframe .note-statusbar, .note-editor.note-frame .note-statusbar {
    border-color: #e2e6e9;
    background: #f4f7fa;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.note-editor .note-toolbar {
    padding: 15px 20px;
    border-bottom: 1px solid #e2e6e9;
    background: #f4f7fa;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.note-editor.note-airframe .note-editing-area .note-editable, .note-editor.note-frame .note-editing-area .note-editable {
    padding: 15px 20px;
}

[class*=" note-icon"]:before, [class^=note-icon]:before {
    color: #4d546b;
}

.note-btn {
    display: inline-block;
    padding: 6px 20px;
    border-radius: 5px;
    font-size: 13px;
    height: 33px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.note-btn:hover, .note-btn:focus {
    border-radius: 5px;
    box-shadow: none;
}

.note-btn::after {
    display: none;
}

.note-dropdown-menu {
    display: block;
    visibility: hidden;
    margin-top: 40px;
    opacity: 0;
    border: none;
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    min-width: 345px;
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-transition: all .2s ease-in-out!important;
    -moz-transition: all .2s ease-in-out!important;
    -o-transition: all .2s ease-in-out!important;
    transition: all .2s ease-in-out!important;
}

.note-dropdown-menu:after {
    position: absolute;
    top: -7px;
    left: 10px;
    right: auto;
    display: inline-block !important;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #fff;
    border-left: 7px solid transparent;
    content: '';
}

.note-btn-group.open .note-dropdown-menu {
    opacity: 1;
    transform: none;
    visibility: visible;
    top: 20px!important;
}

.note-editor .note-toolbar .note-dropdown-menu, .note-popover .popover-content .note-dropdown-menu {
    min-width: 200px;
}

.note-editor .note-toolbar .note-color-all .note-dropdown-menu, .note-popover .popover-content .note-color-all .note-dropdown-menu {
    min-width: 355px;
}

.note-dropdown-menu .note-dropdown-item {
    color: #40475c;
    padding: 5px 10px;
    margin: 5px 0;
    display: block;
    position: relative;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.note-dropdown-menu .note-dropdown-item:hover {
    background: transparent;
    font-weight: 500;
}

.note-dropdown-menu .note-dropdown-item.active {
    color: #27313F;
    font-weight: 500;
    background-color: transparent;
}

.note-icon-caret {
    margin: 0 -10px 0 10px;
    line-height: 18px;
    color: #40475c;
}

.note-icon-caret::before {
    content: '\e5cf';
    font-family: 'Material Icons';
    font-size: 16px;
}

.note-dropdown-menu .note-btn {
    padding: 6px 10px;
    font-size: 12px;
}

.note-modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.note-modal-header {
    border: 0;
    padding: 25px 30px 10px;
}

.note-modal-title {
    font-size: 16px;
    color: #293442;
    margin: 0;
    font-weight: 600;
}

.note-modal-header .close {
    padding: 10px;
    margin: -10px -10px -10px auto;
}

.note-modal-body {
    padding: 25px 30px;
}

.note-modal-footer {
    padding: 15px 30px;
    border: 0;
    height: auto;
}

.note-modal-footer a {
    color: #3599fd;
}

.note-modal-body kbd {
    border-radius: 5px;
    padding: 4px 7px;
    font-weight: normal;
    font-size: 11px;
    background-color: #40475c;
}

.note-frame {
    color: #24292e;
    background: #fff;
}

.note-editor.note-airframe .note-placeholder,
.note-editor.note-frame .note-placeholder {
    padding: 15px 20px;
}

/* Flatpickr */

.flatpickr-calendar {
    width: 340px;
    margin-top: 10px;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.flatpickr-input {
    background-color: #fff!important;
}

.form-control.form-control-solid.flatpickr-input {
    background-color: #f4f7fa;
}

.flatpickr-months .flatpickr-prev-month, .flatpickr-months .flatpickr-next-month {
    top: 15px;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month, .flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
    left: 15px;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month, .flatpickr-months .flatpickr-next-month.flatpickr-next-month {
    right: 15px;
}

.flatpickr-months .flatpickr-prev-month:hover svg, .flatpickr-months .flatpickr-next-month:hover svg {
    filter: invert(42%) sepia(56%) saturate(7488%) hue-rotate(214deg) brightness(98%) contrast(95%);
}

.flatpickr-months {
    margin-bottom: 15px;
}

.flatpickr-months .flatpickr-month {
    height: 44px;
}

.flatpickr-current-month {
    font-size: 14px;
    height: 44px;
    padding: 0;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    border-radius: 8px;
    padding: 10px 20px 10px 18px;
    border-color: #e3e6ea;
    max-width: 100px;
}

.flatpickr-current-month .numInputWrapper {
    width: 80px;
    border-radius: 10px;
    margin-left: 10px;
}

.flatpickr-current-month input.cur-year {
    border-radius: 8px;
    padding: 10px 0 10px 18px;
    border-color: #e3e6ea;
}

.numInputWrapper span.arrowUp {
    background: transparent;
    border: none;
    height: 10px;
    top: 10px;
    right: 10px;
}

.numInputWrapper span.arrowDown {
    background: transparent;
    border: none;
    height: 10px;
    top: 20px;
    right: 10px;
}

span.flatpickr-weekday {
    color: #24292e;
}

.flatpickr-day {
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.flatpickr-day.today {
    border-color: #FCEACE;
    background-color: #FCEACE;
}

.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange, .flatpickr-day.selected.inRange, .flatpickr-day.startRange.inRange, .flatpickr-day.endRange.inRange, .flatpickr-day.selected:focus, .flatpickr-day.startRange:focus, .flatpickr-day.endRange:focus, .flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover, .flatpickr-day.selected.prevMonthDay, .flatpickr-day.startRange.prevMonthDay, .flatpickr-day.endRange.prevMonthDay, .flatpickr-day.selected.nextMonthDay, .flatpickr-day.startRange.nextMonthDay, .flatpickr-day.endRange.nextMonthDay {
    background-color: #2269F5;
    border-color: #2269F5;
}

.flatpickr-calendar:before {
    display: none!important;
}

.flatpickr-calendar:after {
    position: absolute;
    top: -10px;
    left: 20px;
    right: auto;
    display: inline-block !important;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #fff;
    border-left: 7px solid transparent;
    content: '';
}

/* Select2 */

.select2-selection {
    height: auto!important;
    box-shadow: none;
    border-radius: 8px!important;
    padding: 10px 36px 10px 18px;
    border-color: #e3e6ea!important;
    color: #24292e;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.select2-container--open .select2-selection {
    border-color: #ccd9e6!important;
}

.select2-selection .select2-selection__rendered {
    line-height: 1.5!important;
    padding-left: 0!important;
    font-size: 14px;
    font-weight: normal;
    color: #24292e;
}

.select2-selection .select2-selection__arrow {
    height: 41px!important;
    right: 20px!important;
}

.select2-selection.select2-selection--multiple {
    padding: 9px 25px!important;
    min-height: 0px!important;
}

.select2-selection.select2-selection--multiple .select2-selection__rendered {
    margin-top: 3px;
    margin-bottom: -3px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b, .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    width: 0;
    height: 0;
    border: none!important;
    margin: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b::before {
    display: block;
    font-family: 'Material Icons';
    content: "\e5cf";
    width: 10px;
    height: 10px;
    position: absolute;
    top: -15px;
    font-size: 18px;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b::before {
    display: block;
    font-family: 'Material Icons';
    content: "\e5ce";
    width: 10px;
    height: 10px;
    position: absolute;
    top: -15px;
    font-size: 18px;
}

.select2-selection.select2-selection--multiple .select2-selection__rendered input {
    margin-top: 0!important;
}

.select2-search--dropdown .select2-search__field {
    border-radius: 8px;
    padding: 10px 18px;
    border-color: #e3e6ea!important;
    color: #24292e;
    -webkit-transition: border .2s ease-in-out, background-color .2s ease-in-out;
    -moz-transition: border .2s ease-in-out, background-color .2s ease-in-out;
    -o-transition: border .2s ease-in-out, background-color .2s ease-in-out;
    transition: border .2s ease-in-out, background-color .2s ease-in-out;
}

.select2-search--dropdown .select2-search__field:focus, .select2-search--dropdown .select2-search__field:active {
    box-shadow: none!important;
    border-color: #2269F5!important;
    outline: none;
}

.select2-dropdown {
    padding: 10px 15px!important;
    border: none!important;
    margin-top: 3px!important;
    border-radius: 10px!important;
    box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
}

.select2-dropdown--above {
    margin-top: -3px!important;
    border-bottom: 1px solid #E6E6E6!important;
}

.select2-selection__choice {
    margin-top: 0!important;
    height: 19px!important;
    border: 1px solid #DFF0FE!important;
    background: #DFF0FE!important;
    color: #2269F5!important;
    font-size: 12px!important;
    font-weight: 500!important;
    padding: 0 10px;
}

.select2-results__option {
    border-radius: 20px;
    padding: 9px 16px;
}

.select2-selection__choice span {
    color: #2269F5!important;
}

.select2-container--default .select2-results__option .select2-results__option {
    color: #5d657e;
    padding: 5px 10px;
    margin: 5px 0;
    display: block;
    font-size: 13px;
    font-weight: 400;
    position: relative;
    border-radius: 5px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background: transparent;
    color: #000;
}

.select2-container--default .select2-results__option[aria-selected=true]:hover {
    color: #2269F5;
}

.select2-container--default .select2-results__group {
    padding: 6px;
    font-size: 11px;
}

.select2-container--default .select2-results__option[aria-selected=true] {
    border: 1px solid #DFF0FE!important;
    background: #DFF0FE;
    color: #2269F5;
}

/*------------------------------------------------------------
[8. Apps]
------------------------------------------------------------*/

/* Mailbox */

@media (min-width: 1200px) {
    .mailbox-container {
        height: calc(100vh - 160px);
    }
    .mailbox-container > .card {
        height: 100%;
    }
}

.mailbox-container > .card {
    overflow: hidden;
}

.mailbox-container .mailbox-compose-btn {
    position: fixed;
    right: 70px;
    bottom: 70px;
}


.mailbox-container > .card > .container-fluid,
.mailbox-container > .card > .container-fluid > .row {
    height: 100%;
    margin: 0;
}

.mailbox-container .mailbox-list {
    height: 100%;
    background: #f4f7fa;
    border-right: 1px solid #e3e6ea;
    margin: 0;
    padding: 0;
    overflow:auto;
}

.mailbox-container .mailbox-list ul {
    margin: 0;
}

.mailbox-container .mailbox-list .mailbox-list-item a {
    display: flex;
    align-items: center;
    text-decoration: none;
    padding: 10px;
    border-bottom: 1px solid #e3e6ea;
}

.mailbox-container .mailbox-list .mailbox-list-item.active a {
    background: #eaeff5;
}

.mailbox-container .mailbox-list .mailbox-list-item a img {
    height: 50px;
    width: 50px;
    border-radius: 25px;
}

.mailbox-container .mailbox-list .mailbox-list-item .mailbox-list-item-content {
    padding: 10px 15px;
}

.mailbox-container .mailbox-list .mailbox-list-item .mailbox-list-item-content .mailbox-list-item-title {
    color: #000;
    font-weight: 500;
}

.mailbox-container .mailbox-list .mailbox-list-item .mailbox-list-item-content .mailbox-list-item-text {
    font-size: 13px;
    color: #3e485b;
    margin-bottom: 0;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    display: -webkit-box;
}

.mailbox-open-content {
    margin: 0;
    padding: 30px;
    overflow: auto;
    height: 100%;
}

.mailbox-open-content .mailbox-open-title {
    font-size: 21px;
    font-weight: normal;
    color: #293542;
}

.mailbox-open-content .mailbox-open-date {
    font-size: 13px;
    margin-bottom: 10px;
    display: block;
    color: #939da9;
}

.mailbox-open-content .mailbox-open-author {
    display: flex;
    margin-top: 20px;
    align-items: center;
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 20px;
}

.mailbox-open-content .mailbox-open-author img {
    width: 50px;
    height: 50px;
    border-radius: 25px;
}

.mailbox-open-content .mailbox-open-author .mailbox-open-author-info {
    flex: 1;
    margin-left: 10px;
}

.mailbox-open-content .mailbox-open-author .mailbox-open-author-info .mailbox-open-author-info-email {
    font-weight: 500;
}

.mailbox-open-content .mailbox-open-author .mailbox-open-author-info .mailbox-open-author-info-to {
    color: #7B8C9D;
    font-size: 13px;
}

.mailbox-open-content .mailbox-open-author .mailbox-open-author-info .mailbox-open-author-info-to .badge {
    vertical-align: middle;
}

.mailbox-open-content .mailbox-open-content-email {
    padding: 30px 0;
}

.mailbox-open-content-email-attachments {
    margin: 30px 0 10px;
    display: block;
    padding: 20px 0 0;
    border-top: 1px solid #f1f1f1;
}

.mailbox-open-content-email-attachments .attachments-files-list {
    padding-left: 0;
    margin: 0;
    display: flex;
    -ms-flex-align:center;
    margin-bottom: 10px;
}

.mailbox-open-content-email-attachments .attachments-files-list:not(:last-child) {
    margin-right: 20px;
}

.mailbox-open-content-email-attachments .attachments-files-list-item {
    padding: 10px 10px;
    border-radius: 10px;
    background: #f4f7fa;
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.mailbox-open-content-email-attachments .attachments-files-list-item:last-child {
    margin-bottom: 0;
}

.mailbox-open-content-email-attachments .attachments-files-list-item .attachments-files-list-item-icon {
    display: block;
    width: 45px;
    height: 45px;
    text-align: center;
    margin-right: 10px;
}

.mailbox-open-content-email-attachments .attachments-files-list-item .attachments-files-list-item-icon i {
    line-height: 45px;
    font-size: 18px;
    color: #037AFB;
}

.mailbox-open-content-email-attachments .attachments-files-list-item .attachments-files-list-item-content {
    flex: 1;
}

.mailbox-open-content-email-attachments .attachments-files-list-item .attachments-files-list-item-content .attachments-files-list-item-title {
    display: block;
    font-size: 13px;
    font-weight: 500;
}

.mailbox-open-content-email-attachments .attachments-files-list-item .attachments-files-list-item-content .attachments-files-list-item-size {
    font-size: 13px;
    opacity: 0.7;
}

.mailbox-open-content-email-attachments .attachments-files-list-item .attachments-files-list-item-download-btn {
    margin: 0 10px;
}

.mailbox-open-content-email-attachments .attachments-files-list-item .attachments-files-list-item-download-btn i {
    font-size: 18px;
}

/* File Manager */

.file-manager-folder .file-manager-folder-icon {
    max-width: 70px;
    height: 50px;
    display: block;
    border-radius: 10px;
}

.file-manager-folder .file-manager-folder-title {
    margin: 15px 0 10px;
    display: block;
    color: #435263;
    font-weight: 500;
}

.file-manager-folder .file-manager-folder-capacity {
    font-size: 24px;
    font-weight: 500;
}

.file-manager-recent-item .file-manager-recent-item-title {
    color: #3F485B;
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.file-manager-recent-item .file-manager-recent-item-title:hover {
    color: #037AFB;
}

.file-manager-recent-item .file-manager-recent-file-actions {
    margin-left: 10px;
    height: 21px;
    display: block;
}

.file-manager-recent-item .file-manager-recent-file-actions::after {
    display: none;
}

.file-manager-recent-item .file-manager-recent-file-actions i {
    font-size: 21px;
    line-height: 21px;
    color: #24292e;
}

.file-manager-recent-item .file-manager-recent-file-actions:hover i {
    opacity: 0.7;
}

.file-manager-group .file-manager-group-info {
    margin-left: 15px;
}

.file-manager-group .file-manager-group-title {
    color: #27323f;
    display: block;
    font-weight: 500;
    text-decoration: none;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.file-manager-group .file-manager-group-title:hover {
    color: #037AFB;
}

.file-manager-group .file-manager-group-about {
    color: #8e99a5;
}

/* Calendar */

.calendar-container {
    height: calc(100vh - 160px);
    margin-bottom: 0!important;
    overflow: auto;
}

.fc .fc-toolbar-title {
    font-weight: 500;
    color: #3e485b;
}

.fc .fc-col-header-cell-cushion {
    text-decoration: none;
    text-transform: uppercase;
    font-weight: 500;
    margin: 7px 0;
    color: #7B8C9D;
}

.fc-theme-standard .fc-scrollgrid,
.fc-theme-standard td,
.fc-theme-standard th {
    border-color: #E3E6EA;
}

.fc .fc-daygrid-day-number {
    text-decoration: none;
    font-size: 16px;
    margin: 5px;
    font-weight: 500;
    color: #8e99a5;
}

.fc-h-event {
    background: #DFF0FE;
    border-color: #DFF0FE;
    padding: 4px 7px;
}

.fc-h-event .fc-event-main {
    color: #2269F5;
}

.fc .fc-non-business {
    background-color: #F4F7FA;
}

.fc .fc-highlight {
    background-color: rgb(34,105,244, 0.06);
}

.fc .fc-daygrid-event {
    color: #2269F5;
    padding: 4px 7px;
}

.fc .fc-daygrid-event:hover {
    background-color: #d4ebff;
}

.fc-daygrid-event-dot {
    border-color: #2269F5;
}

.fc .fc-daygrid-more-link {
    color: #3e485b;
    margin: 4px 10px;
}

.fc .fc-popover-header {
    background-color: #F4F7FA;
    padding: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.fc .fc-popover-title {
    font-size: 14px;
    color: #7B8C9D;
}

.fc .fc-popover {
    border: none;
    border-radius: 10px;
}

/* Todo */

@media (min-width: 1200px) {
    .todo-container {
        height: calc(100vh - 160px);
        margin-bottom: 0!important;
    }

    .todo-container .row,
    .todo-container .row > div {
        height: 100%;
    }
}

.todo-menu {
    margin: 25px 0 25px 25px;
    padding-right: 25px;
    height: calc(100% - 50px);
    overflow-y: auto;
    border-right: 1px solid #E3E6EA;
}

.todo-menu .todo-menu-title {
    padding: 0 20px 10px 0;
    font-weight: 700;
    text-transform: uppercase;
    color: #40475c;
    font-size: 12px;
    opacity: .35;
    font-family: 'Montserrat', sans-serif;
}

.todo-menu .todo-status-filter li a {
    color: #293442;
    padding: 10px 20px;
    display: block;
    text-decoration: none;
    background: #f4f7fa;
    border-radius: 10px;
    margin-bottom: 12px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.todo-menu .todo-status-filter li a:hover,
.todo-menu .todo-status-filter li a.active {
    background: #DFF0FE;
    color: #2269F5;
}

.todo-menu .todo-status-filter li a i {
    font-size: 18px;
    vertical-align: middle;
    margin-right: 10px;
}

.todo-menu .todo-label-filter a {
    margin: 5px 0;
}

.todo-menu .todo-preferences-filter .todo-preferences-item:not(:last-child) {
    margin-bottom: 10px;
}

.todo-list {
    margin: 25px 25px 25px 0;
    height: calc(100% - 50px);
    overflow-y: auto;
}

@media (max-width: 1199px) {
    .todo-list {
        margin: 25px;
        height: auto;
        overflow-y: auto;
    }
}

.todo-list .todo-item {
    padding: 20px 0;
    display: flex;
    align-items: center;
}

.todo-list .todo-item:not(:last-child) {
    border-bottom: 1px solid #E3E6EA;
}

.todo-list .todo-item .todo-item-title {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #3E485B;
}

.todo-list .todo-item .todo-item-title .badge {
    margin-left: 10px;
    font-size: 10px;
    vertical-align: middle;
}

.todo-list .todo-item .todo-item-subtitle {
    margin-top: 10px;
    display: block;
    opacity: 0.5;
}

.todo-list .todo-item .todo-item-content {
    flex: 1
}

.todo-list .todo-item .todo-item-actions {
    opacity: 0;
    padding-left: 50px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.todo-list .todo-item:hover .todo-item-actions {
    opacity: 1;
}

.todo-list .todo-item .todo-item-actions a {
    margin: 0 5px;
    padding: 10px 7px;
    opacity: 0.6;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.todo-list .todo-item .todo-item-actions a:hover {
    opacity: 1;
}

.todo-list .todo-item .todo-item-actions .todo-item-delete {
    color: #FF4857;
}

.todo-list .todo-item .todo-item-actions .todo-item-done {
    color: #4bad48;
}

/* Invoice */

.invoice {
    margin-top: 30px;
}

.invoice-header {
    margin: -25px -30px 40px -30px;
    border-radius: 10px 10px 0 0;
    padding: 40px 30px;
    background: #2269f3;
    display: block;
}

.invoice-header h3 {
    color: #fff;
    font-weight: 600;
    margin: 0;
}

.invoice-header .invoice-issue-date {
    vertical-align: middle;
    float: right;
    color: #fff;
    line-height: 29px;
}

.invoice-description {
    margin-bottom: 40px;
    color: #909aa7;
}

.invoice-table img {
    width: 35px;
    height: 35px;
    border-radius: 16.5px;
    margin-left: 10px;
}

.invoice-table td,
.invoice-table th {
    vertical-align: middle;
}

.invoice-table th:not(:first-child):not(:nth-child(2)),
.invoice-table td:not(:first-child):not(:nth-child(2)) {
    text-align: right;
}

.invoice-table td:nth-child(2) {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}

.invoice-summary {
    padding: 20px 0;
}

@media (max-width: 991px) {
    .invoice-info {
        margin-bottom: 20px;
    }
}

.invoice-info p span {
    float: right;
}

.invoice-info .invoice-info-actions {
    display: flex;
    margin-top: 20px;
}

.invoice-info .invoice-info-actions a {
    flex: 1;
}

/* Settings */

.settings-integrations .settings-integrations-item {
    display: flex;
    align-items: center;
    padding: 20px;
}

.settings-integrations .settings-integrations-item:not(:last-child) {
    margin-bottom: 10px;
    border-bottom: 1px solid #E3E6EA;
}

.settings-integrations .settings-integrations-item .settings-integrations-item-info {
    flex: 1;
    margin-right: 20px;
    display: block;
}

.settings-integrations .settings-integrations-item .settings-integrations-item-info img {
    height: 20px;
}

.settings-integrations .settings-integrations-item .settings-integrations-item-info span {
    margin-top: 20px;
    color: #909aa7;
    display: block;
}

.settings-security-two-factor {
    padding: 25px;
    background-color: #F4F7FA;
    border-radius: 10px;    
    border: 1px solid #E3E6EA;
}

.settings-security-two-factor h5 {
    color: #40475c;
}

.settings-security-two-factor span {
    margin-top: 20px;
    color: #909aa7;
    display: block;
}

@media (min-width: 1199px) {
    .settings-security-two-factor {
        max-width: 75%;
    }
}

/* Authentication */

.app-auth-container {
    height: 100vh;
    width: 650px;
    background: #fff;
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 80px;
}

@media (max-width: 1200px) {
    .app-auth-container {
        width: 100%;
        padding-left: 200px;
        padding-right: 200px;
    }
}

@media (max-width: 780px) {
    .app-auth-container {
        padding-left: 50px;
        padding-right: 50px;
    }
}

.app-auth-sign-in .app-auth-background,
.app-auth-sign-up .app-auth-background,
.app-auth-lock-screen .app-auth-background {
    flex: 1;
    height: 100%;
    content: '';
    display: block;
}

.app-auth-sign-in .app-auth-background {
    background: url('../images/backgrounds/sign-in.svg') no-repeat;
    background-size: 60%;
    background-position: center;
}


.app-auth-sign-up .app-auth-background {
    background: url('../images/backgrounds/sign-up.svg') no-repeat;
    background-size: 60%;
    background-position: center;
}

.app-auth-lock-screen .app-auth-background {
    background: url('../images/backgrounds/lock-screen.svg') no-repeat;
    background-size: 60%;
    background-position: center;
}

.app-auth-container .logo a {
    display: flex;
    padding-left: 70px;
    background: url('../images/neptune.png') no-repeat;
    height: 50px;
    background-size: 50px;
    align-items: center;
    text-decoration: none;
    color: #40475c;
    font-size: 21px;
    font-weight: bold;
}

.app-auth-container .auth-description {
    margin: 30px 0;
    color: #909aa7;
}

.app-auth-container .auth-submit .auth-forgot-password {
    margin: 6px 0;
    display: inline-block;
}

.app-auth-container .auth-alts a {
    width: 30px;
    height: 30px;
    margin-right: 15px;
    display: inline-block;
    content: '';
    opacity: 0.5;
    filter: grayscale(1);
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.app-auth-container .auth-alts a:hover {
    filter: none;
    opacity: 1;
}

.app-auth-container .auth-alts .auth-alts-google {
    background: url('../images/icons/google.png') no-repeat;
    background-size: cover;
}

.app-auth-container .auth-alts .auth-alts-facebook {
    background: url('../images/icons/facebook.png') no-repeat;
    background-size: cover;
}

.app-auth-container .auth-alts .auth-alts-twitter {
    background: url('../images/icons/twitter.png') no-repeat;
    background-size: cover;
}

.app-auth-container .auth-user {
    margin: 0 0 30px 0;
    padding: 15px;
    background: #F4F7FA;
    border-radius: 10px;
    display: flex;
    align-items: center;
}

.app-auth-container .auth-user img {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    margin-right: 20px;
}

.app-auth-container .auth-user .auth-user-fullname {
    font-weight: 500;
    flex: 1;
}

.app-auth-container .auth-user .auth-user-activity {
    color: #909aa7;
}

/* Error */

.app-error {
    align-items: center;
}

.app-error .app-error-info {
    min-width: 50%;
    padding-left: 200px;
    padding-right: 100px;
}

.app-error .app-error-info h5 {
    font-size: 96px;
    font-weight: 800;
    color: #2269F5;
}

.app-error .app-error-info span {
    margin: 50px 0;
    display: block;
    font-size: 16px;
}

.app-error .app-error-background {
    height: 100%;
    flex: 1;
    content: '';
    display: block;
    background: url('../images/backgrounds/error.svg') no-repeat;
    background-size: 60%;
    background-position: center center;
}

@media (max-width: 1200px) {
    .app-error .app-error-info {
        width: 100%;
        text-align: center;
        padding: 0;
    }
    .app-error .app-error-background {
        display: none;
    }
}

@media (max-width: 1600px) {
    .app-error .app-error-info {
        padding-left: 100px;
    }
}

/* Search */

.search {
    z-index: 99999;
    width: calc(100% - 340px);
    display: flex;
    position: fixed;
    background: #fff;
    top: -70px;
    left: 310px;
    right: 30px;
    height: 70px;
    border-radius: 10px;
    padding: 13.5px 30px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

@media (max-width: 1200px) {
    .search {
        width: calc(100% - 60px);
        left: 30px;
    }
}

.app.sidebar-hidden  .search {
    width: calc(100% - 360px);
    left: 330px;
}

.full-width-header .search {
    width: calc(100% - 280px);
    left: 280px;
    right: 0;
    height: 70px;
    top: 0!important;
    border-radius: 0;
    padding: 13.5px 45px;
}

.header-large .search {
    width: calc(100% - 283px);
    left: 283px;
    right: 0;
    height: 70px;
    border-radius: 0;
    padding: 13.5px 45px;
}

.search-visible.header-large .search {
    top: 0;
}

.search form {
    flex: 1;
}

.search .form-control {
    border: none!important;
    background: transparent;
    padding-left: 0;
}

.search .toggle-search {
    float: right;
    margin: 10px 0 10px 10px;
    color: rgba(0,0,0,.55);
}

.app-header .toggle-search {
    margin-right: 0!important;
}

.search-visible .search {
    top: 30px;
    opacity: 1;
    visibility: visible;
}

/*------------------------------------------------------------
[9. Responsive]
------------------------------------------------------------*/

@media (max-width: 1199px) {
    .app-container {
        margin-left: 0;
        width: 100%;
    }

    .app-header {
        left: 330px;
        width: calc(100% - 360px);
    }

    .full-width-header .app-header {
        left: 240px;
        width: calc(100% - 240px);
    }

    .header-transaprent .app-header {
        left: 280px;
        width: calc(100% - 280px);
        right: auto;
    }

    .app.sidebar-hidden.header-transaprent .app-sidebar {
        transform: none;
        margin-left: -280px;
        position: fixed;
        top: 0;
        left: 0;
        margin: 0;
    }

    .app:not(.sidebar-hidden) .app-sidebar{
        transform: translateX(-280px);
    }

    .app.sidebar-hidden .app-sidebar {
        transform: translateX(0);
    }
    
    .app.sidebar-hidden .app-container {
        transform: translateX(280px);
    }

    .app.sidebar-hidden {
        overflow: hidden;
    }

    .app-sidebar .logo {
        position: fixed;
        width: 280px;
        transform: translateX(310px);
        height: 70px;
        top: 30px;
        padding-top: 15px;
        padding-bottom: 15px;
        border-radius: 10px;
        background-color: #ffffff;
        opacity: 1;
        box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
        -webkit-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
        -moz-box-shadow: 0px 0px 11px 1px rgba(0, 0, 0, 0.05);
    }

    .app.full-width-header .app-sidebar .logo {
        top: 0;
        left: 0;
        transform: translateX(280px);
        width: 240px;
        padding-left: 60px;
        border-radius: 0;
        background: #fff;
        color: #293442;
        box-shadow: none;
    }

    .app.sidebar-colored .app-sidebar .logo .sidebar-user-switcher .user-info-text, .app.sidebar-colored .app-sidebar .logo-icon .logo-text {
        color: #333;
    }

    .app.sidebar-colored .app-sidebar .logo {
        background-color: #fff;
    }

    .app-sidebar .app-menu {
        height: 100%!important;
    }

    .header-transaprent .app-sidebar .app-menu {
        margin-top: -70px;
    }

    .hide-app-sidebar-mobile {
        visibility: hidden;
        opacity: 0;
        z-index: 1004;
        position: absolute;
        right: 0;
        background: rgba(0,0,0,.4);
        left: 0;
        height: 100%;
        content: '';
        display: block;
        -webkit-transition: all .2s ease-in-out;
        -moz-transition: all .2s ease-in-out;
        -o-transition: all .2s ease-in-out;
        transition: all .2s ease-in-out;
    }

    .sidebar-hidden .hide-app-sidebar-mobile {
        visibility: visible;
        opacity: 1;
        left: 280px;
    }

    .header-large .app-sidebar .logo {
        top: 0;
        left: 0;
        transform: translateX(280px);
        border-radius: 0;
        width: 310px;
        box-shadow: none;
        border-right: 1px solid #E3E6EA;
        padding: 15px 40.5px;
    }

    .header-large .app-header {
        width: calc(100% + 60px);
        margin-right: 0;
        left: 0;
    }

    .header-large .app-header .navbar {
        margin-left: 310px;
    }
}

@media (max-width: 1100px) {
    .hidden-on-mobile {
        display: none!important;
    }
}

@media (max-width: 576px) {
    .app-container {
        padding: 10px;
    }

    .app-sidebar .logo,
    .app.menu-off-canvas .app-sidebar .logo,
    .app.sidebar-hidden .app-sidebar .logo.hidden-sidebar-logo {
        width: 100px;
        transform: translateX(290px);
        top: 10px;
    }

    .app.full-width-header .app-sidebar .logo,
    .app.sidebar-hidden.full-width-header .app-sidebar .logo.hidden-sidebar-logo {
        padding-left: 20px;
        width: 140px;
        padding-right: 20px;
    }

    .header-large .app-sidebar .logo {
        width: 130px;
    }

    .app-sidebar .logo .sidebar-user-switcher {
        display: none;
    }

    .app-header,
    .app.menu-off-canvas .app-header,
    .header-transaprent .app-header {
        left: 120px;
        width: calc(100% - 130px);
    }

    .full-width-header .app-header {
        left: 140px;
        width: calc(100% - 140px);
    }

    .header-large .app-header {
        margin-top: -10px;
    }

    .header-large .app-header .navbar {
        margin-left: 130px;
    }

    .search {
        width: calc(100% - 20px);
        left: 10px;
        right: 10px;
    }

    .full-width-header.search-visible .search,
    .header-large .search {
        width: 100%;
        left: 0;
        right: 0;
    }

    .search-visible .search {
        top: 10px;
    }

    .full-width-header.search-visible .search {
        top: 0;
    }

    .app-content {
        margin-top: 110px;
    }

    .mailbox-open-content-email-attachments .attachments-files-list {
        flex-direction: column;
    }

    .mailbox-open-content-email-attachments .attachments-files-list-item {
        margin-bottom: 10px;
    }
}


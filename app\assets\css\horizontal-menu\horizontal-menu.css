/* HORIZONTAL MENU */

/* Layout */

body {
    background: #fff;
}

.horizontal-menu.app-auth-sign-in,
.horizontal-menu.app-auth-sign-up,
.horizontal-menu.app-auth-lock-screen {
    background: #e7ecf8;
}

.horizontal-menu .app-container {
    margin-left: 0;
    width: 100%!important;
    padding: 0 0 30px 0;
}

.horizontal-menu .app-header {
    width: 100%;
    left: 0;
    right: 0;
    top: 0;
    background-color: #2269f4;
    z-index: 10010;
}

.horizontal-menu .search,
.horizontal-menu.sidebar-hidden .search {
    left: 0;
    right: 0;
    width: 100%;
    background-color: #2269f4;
    border-radius: 0;
}

.horizontal-menu.search-visible .search {
    top: 0;
}

.horizontal-menu .search input::placeholder,
.horizontal-menu .search .toggle-search  {
    color: rgba(255,255,255,.6);
}

.horizontal-menu .search input,
.horizontal-menu .search .toggle-search:hover {
    color: #fff;
}

.horizontal-menu .app-header .logo a {
    font-size: 16px;
    text-decoration: none;
    color: #fff;
    text-transform: uppercase;
    font-family: 'Mont<PERSON>rat', sans-serif;
    font-weight: 600;
    letter-spacing: 2px;
    vertical-align: middle;
    line-height: 45px;
    margin-right: 30px;
    padding: 0 10px;
}

.horizontal-menu .app-header .navbar {
    border-radius: 0;
    box-shadow: none;
    background: none;
    padding: 0;
}

.horizontal-menu .app-header .navbar .navbar-nav {
    flex-direction: row!important;
}

.horizontal-menu .app-header .navbar .navbar-nav > li > a {
    color: rgba(255,255,255,.6);
}

.horizontal-menu .app-header .navbar .navbar-nav > li > a.active,
.horizontal-menu .app-header .navbar .navbar-nav > li > a:hover {
    color: #fff;
}

.horizontal-menu .app-header .navbar .navbar-nav > li > a.active::after {
    background-color: #fff;
}

.horizontal-menu .navbar .dropdown-menu {
    box-shadow: none;
    border: 1px solid #E2E6E9;
}

.horizontal-menu .navbar .dropdown-menu::after {
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
}

.horizontal-menu .navbar .dropdown-menu::before {
    position: absolute;
    top: -9px;
    left: 22px;
    right: auto;
    display: inline-block !important;
    border-right: 7px solid transparent;
    border-bottom: 8px solid #E2E6E9;
    border-left: 7px solid transparent;
    content: '';
}

.horizontal-menu .navbar .dropdown-menu-end::before {
    right: 22px;
    left: auto;
}

.horizontal-menu .navbar .dropdown-menu.show {
    z-index: 10009;
}

.horizontal-menu .app-header::before {
    display: none;
}

@media (min-width: 1199px) {
    .horizontal-menu .app-header .hide-sidebar-toggle-button {
        display: none;
    }
}

.horizontal-menu.sidebar-hidden .app-header {
    width: 100%;
}


/* Menu */

.horizontal-menu .app-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    height: 55px!important;
    padding: 0;
    background-color: #fff;
    z-index: 10008;
    border-bottom: 1px solid #E2E6E9;
}

.horizontal-menu .app-menu .menu-list > li {
    display: inline-block;
    padding: 5.43px 0;
    height: auto;
    position: relative;
}

.horizontal-menu .app-menu .menu-list > li > a {
    padding: 11px;
    margin: 0;
    display: block;
    text-decoration: none;
    color: #8fa2ac;
    font-weight: 500;
    font-size: 13px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.horizontal-menu .app-menu .menu-list > li > a:hover,
.horizontal-menu .app-menu .menu-list > li:hover > a,
.horizontal-menu .app-menu .menu-list > li > a.active,
.horizontal-menu .app-menu .menu-list > li.active-page > a {
    color: #2269f4;
}

.horizontal-menu .app-menu .menu-list > li:not(:last-child) {
    margin-right: 6px;
}

.horizontal-menu .app-menu .menu-list > li > a > i.has-sub-menu {
    vertical-align: middle;
    font-size: 17px;
    margin-left: 5px;
    line-height: 20px;
}

.horizontal-menu .app-menu .sub-menu {
    display: block;
    position: absolute;
    top: 40px;
    left: 0;
    padding: 15px 0;
    background: #fff;
    border: 1px solid #E2E6E9;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    z-index: 9999999;
    border-radius: 7px;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.horizontal-menu .app-menu .sub-menu::after {
    position: absolute;
    top: -8px;
    left: 23px;
    right: auto;
    display: inline-block !important;
    border-right: 6px solid transparent;
    border-bottom: 8px solid #fff;
    border-left: 6px solid transparent;
    content: '';
}

.horizontal-menu .app-menu .sub-menu::before {
    position: absolute;
    top: -9px;
    left: 22px;
    right: auto;
    display: inline-block !important;
    border-right: 7px solid transparent;
    border-bottom: 8px solid #E2E6E9;
    border-left: 7px solid transparent;
    content: '';
}

@media (min-width: 1199px) {
    .horizontal-menu .app-menu .menu-list li:hover > a + .sub-menu,
    .horizontal-menu .app-menu .menu-list .sub-menu:hover {
        height: auto;
        visibility: visible;
        opacity: 1;
        top: 70px;
    }

    .horizontal-menu .app-menu .menu-list > li:hover > a {
        padding-bottom: 25px;
    }
    
    .horizontal-menu .app-menu .sub-menu li:hover {
        width: calc(100% + 40px);
    }
    
    .horizontal-menu .app-menu .sub-menu li:hover > a {
        width: calc(100% - 40px);
    }
}

.horizontal-menu .app-menu .sub-menu li a {
    padding: 5px 25px;
    text-decoration: none;
    color: #778b96;
    font-size: 13px;
    display: block;
    -webkit-transition: all .2s ease-in-out;
    -moz-transition: all .2s ease-in-out;
    -o-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
}

.horizontal-menu .app-menu .sub-menu li a:hover, 
.horizontal-menu .app-menu .sub-menu li:hover > a,
.horizontal-menu .app-menu .sub-menu li a.active {
    color: rgb(59, 59, 59);
    font-weight: 500;
}

.horizontal-menu .app-menu .sub-menu .sub-menu {
    left: 220px;
    top: 10px;
}

.horizontal-menu .app-menu .sub-menu .sub-menu::after {
    position: absolute;
    top: 17px;
    left: -13px;
    right: auto;
    display: inline-block !important;
    border-top: 6px solid transparent;
    border-right: 8px solid #fff;
    border-bottom: 6px solid transparent;
    content: '';
}

.horizontal-menu .app-menu .sub-menu .sub-menu::before {
    position: absolute;
    top: 16px;
    left: -15px;
    right: auto;
    display: inline-block !important;
    border-top: 7px solid transparent;
    border-right: 8px solid #E2E6E9;
    border-bottom: 7px solid transparent;
    content: '';
}

.horizontal-menu .app-menu .sub-menu li {
    position: relative;
}

.horizontal-menu .app-menu .sub-menu li > a:hover + .sub-menu, 
.horizontal-menu .app-menu .sub-menu li:hover > a + .sub-menu {
    top: -10px;
}

.horizontal-menu .app-menu .menu-list .sub-menu > li > a > i.has-sub-menu {
    vertical-align: middle;
    font-size: 17px;
    margin-left: 5px;
    line-height: 20px;
    float: right;
}

.horizontal-menu .app-content {
    margin-top: 126px;
}

/* UI */

.horizontal-menu .card {
    border: 1px solid #E2E6E9;
    box-shadow: none;
    border-radius: 7px;
}

.horizontal-menu .app-content .page-description {
    padding-left: 11px;
    padding-right: 11px;
    border-bottom-color: #E2E6E9;
}

.example-container,
.example-container .example-content,
.dropdown-menu,
.alert,
.alert-custom,
.popover,
.toast,
.accordion-separated .accordion-item, .accordion-separated .accordion-item .accordion-button.collapsed,
.apexcharts-menu,
.card,
.card-img-overlay,
.modal-content,
.widget-list .widget-list-content .widget-list-item .widget-list-item-icon,
.widget-list .widget-list-content .widget-list-item .widget-list-item-icon .widget-list-item-icon-image,
.widget-files .widget-files-list .widget-files-list-item,
.widget-popular-product .widget-popular-product-container .widget-popular-product-image,
.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-image,
.widget-popular-blog .widget-popular-blog-container .widget-popular-blog-image img,
.widget-bank-card .widget-bank-card-container,
.widget-stats .widget-stats-container .widget-stats-icon,
.widget-chat .widget-chat-messages-container .widget-chat-message-item .widget-chat-message-item-text,
.widget-chat .widget-chat-compose .widget-chat-compose-input,
.dropzone,
.dropzone .dz-preview .dz-image,
.dropzone .dz-preview .dz-details,
.note-editor,
.note-dropdown-menu,
.note-modal-content,
.flatpickr-calendar,
.flatpickr-current-month .numInputWrapper,
.select2-dropdown,
.mailbox-open-content-email-attachments .attachments-files-list-item,
.file-manager-folder .file-manager-folder-icon,
.fc .fc-popover,
.todo-menu .todo-status-filter li a,
.settings-security-two-factor,
.app-auth-container .auth-user,
.app-auth-container .auth-user img {
    border-radius: 7px;
}

.invoice-header {
    border-radius: 7px 7px 0 0;
}

.horizontal-menu .card-img, 
.horizontal-menu .card-img-bottom {
    border-bottom-right-radius: 7px;
    border-bottom-left-radius: 7px;
}

.horizontal-menu .card-img, 
.horizontal-menu .card-img-top {
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
}

@media (min-width: 1200px) {
    .mailbox-container,
    .todo-container {
        height: calc(100vh - 185px);
        margin-top: 30px;
    }
}

.calendar-container {
    height: calc(100vh - 185px);
    margin-top: 30px;
}


/* Widgets */

.horizontal-menu .widget-bank-card .widget-bank-card-container {
    border-radius: 7px;
}

.horizontal-menu .widget-stats-large .widget-stats-large-info-container {
    border-radius: 0 7px 7px 0;
}

/* Loader */

.pace .pace-progress {
    background-color: rgba(255,255,255,.6);
}

/* Responsive */

@media (max-width: 1199px) {
    .horizontal-menu .app-content {
        margin-top: 66px;
    }

    .horizontal-menu .app-menu {
        padding-top: 30px;
        left: -280px;
        top: 0;
        bottom: 0;
        width: 280px;
        height: 100vh!important;
        background: #fff;
        z-index: 999999;
        border-right: 1px solid #E2E6E9;
        border-bottom: none;
    }

    .horizontal-menu .app-menu .container {
        width: 100%!important;
        padding: 0!important;
        margin: 0!important;
    }

    .horizontal-menu .app-menu .menu-list > li {
        padding: 0;
    }

    .horizontal-menu .app-menu .menu-list > li > a {
        margin: 3px 15px;
        padding: 10px 15px;
        font-size: 14px;
    }

    .horizontal-menu .app-menu .sub-menu {
        position: relative;
        left: 0!important;
        top: 0!important;
        opacity: 1;
        visibility: visible;
        border: none;
        margin: 10px 35px 10px 30px;
        padding: 20px 0;
        border-radius: 5px;
        background: #f5f7fa;
        -webkit-transition: none;
        -moz-transition: none;
        -o-transition: none;
        transition: none;
    }

    .horizontal-menu .app-menu .sub-menu .sub-menu {
        left: auto!important;
        top: auto!important;
        margin: 0;
        padding: 5px 0 5px 20px;
    }

    .horizontal-menu .app-menu .sub-menu::before,
    .horizontal-menu .app-menu .sub-menu::after,
    .horizontal-menu .app-menu .sub-menu .sub-menu::before,
    .horizontal-menu .app-menu .sub-menu .sub-menu::after {
        display: none!important;
    }

    .horizontal-menu .app-menu .menu-list > li {
        display: block;
    }

    .app-menu li > a > i.has-sub-menu {
        transform: rotate(-90deg);
        -webkit-transition: all .2s ease-in-out;
        -moz-transition: all .2s ease-in-out;
        -o-transition: all .2s ease-in-out;
        transition: all .2s ease-in-out;
    }

    .app-menu .sub-menu > li > a > i.has-sub-menu {
        transform: rotate(0deg);
    }

    .app-menu li.open > a > i.has-sub-menu {
        transform: rotate(0deg);
    }

    .app-menu .sub-menu > li.open > a > i.has-sub-menu {
        transform: rotate(90deg);
    }

    .mailbox-container,
    .todo-container {
        margin-top: 30px;
    }
}
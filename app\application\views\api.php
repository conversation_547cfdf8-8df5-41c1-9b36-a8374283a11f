<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WALIX :: <?= $title ?></title>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/pace/pace.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/select2/css/select2.min.css" rel="stylesheet">
    <link href="<?= _assets() ?>/css/main.min.css" rel="stylesheet">
    <link href="<?= _assets() ?>/css/custom.css" rel="stylesheet">
    <link href="<?= _assets() ?>/plugins/highlight/styles/github-gist.css" rel="stylesheet">
</head>

<body>
    <div class="app align-content-stretch d-flex flex-wrap">

        <?php require_once(VIEWPATH . '/include_head.php') ?>

        <div class="app-container">
            <div class="app-header">
                <nav class="navbar navbar-light navbar-expand-lg">
                    <div class="container-fluid">
                        <div class="navbar-nav" id="navbarNav">
                            <ul class="navbar-nav">
                                <li class="nav-item">
                                    <a class="nav-link hide-sidebar-toggle-button" href="#"><i class="material-icons">first_page</i></a>
                                </li>
                                <li class="nav-item dropdown hidden-on-mobile">
                                    <a class="nav-link dropdown-toggle" href="#" id="addDropdownLink" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="material-icons">add</i>
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="addDropdownLink">
                                        <li><a class="dropdown-item" href="#">Broadcast</a></li>
                                        <li><a class="dropdown-item" href="#">WA Blast</a></li>
                                        <li><a class="dropdown-item" href="#">Auto Responder</a></li>
                                    </ul>
                                </li>
                            </ul>

                        </div>
                        <div class="d-flex">
                            <ul class="navbar-nav">

                            </ul>
                        </div>
                    </div>
                </nav>
            </div>
            <div class="app-content">
                <div class="content-wrapper">
                    <div class="container">
                        <div class="row">
                            <div class="col">
                                <div class="page-description p-0">
                                    <h4><?= $title ?></h4>
                                </div>
                            </div>
                        </div>
                        <?= _alert() ?>
                        <div class="card">
                            <div class="card-body">
                                <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#p-text" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Text Message</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#p-media" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Media Message</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#p-tombol" type="button" role="tab" aria-controls="pills-contact" aria-selected="false">Button Message</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-broadcast-tab" data-bs-toggle="pill" data-bs-target="#p-broadcast" type="button" role="tab" aria-controls="pills-broadcast" aria-selected="false">Broadcast</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-devices-tab" data-bs-toggle="pill" data-bs-target="#p-devices" type="button" role="tab" aria-controls="pills-devices" aria-selected="false">Devices</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-contacts-tab" data-bs-toggle="pill" data-bs-target="#p-contacts" type="button" role="tab" aria-controls="pills-contacts" aria-selected="false">Contacts</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-messages-tab" data-bs-toggle="pill" data-bs-target="#p-messages" type="button" role="tab" aria-controls="pills-messages" aria-selected="false">Messages</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-webhook-tab" data-bs-toggle="pill" data-bs-target="#p-webhook" type="button" role="tab" aria-controls="pills-webhook" aria-selected="false">Webhook</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="pills-integration-tab" data-bs-toggle="pill" data-bs-target="#p-integration" type="button" role="tab" aria-controls="pills-integration" aria-selected="false">Integration Examples</button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="pills-tabContent">
                                    <div class="tab-pane fade show active" id="p-text" role="tabpanel" aria-labelledby="pills-home-tab">
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">sender</span>
                                                </td>
                                                <td>sender number (must have scanned qr)</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">receiver</span>
                                                </td>
                                                <td>message recipient</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">message</span>
                                                </td>
                                                <td>message content</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/send-message') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">sender</span>=628xxxx&<span class="text-warning">receiver</span>=628xxxx&<span class="text-warning">message</span>=test</code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>POST</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>',
    'sender'  => 'Sender number (make sure its scanned)',
    'number'  => 'Number of destination to send message', // Pastikan menggunakan 'number' bukan 'receiver'
    'message' => 'the message'
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/send-message') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data),
  CURLOPT_HTTPHEADER => array("Content-Type: application/json"))
);

$response = curl_exec($curl);
</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-media" role="tabpanel" aria-labelledby="pills-profile-tab">
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">sender</span>
                                                </td>
                                                <td>sender number (must have scanned qr)</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">receiver</span>
                                                </td>
                                                <td>message recipient</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">message</span>
                                                </td>
                                                <td>message content</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">url</span>
                                                </td>
                                                <td>Url Media Images</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/send-media') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">sender</span>=628xxxx&<span class="text-warning">receiver</span>=628xxxx&<span class="text-warning">message</span>=test&url=https://i.ibb.co/HNdL38S/Untitled-1.png</code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>POST</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>',
    'sender'  => 'sender number (make sure its scanned)',
    'number'  => 'Number of destination to send message',
    'message' => 'caption (fill in if you send a picture)',
    
    'url' => 'Link image/pdf'
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/send-media') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data))
);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
                                                </code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-tombol" role="tabpanel" aria-labelledby="pills-contact-tab">
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">sender</span>
                                                </td>
                                                <td>sender number (must have scanned qr)</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">receiver</span>
                                                </td>
                                                <td>message recipient</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">message</span>
                                                </td>
                                                <td>message content</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">footer</span>
                                                </td>
                                                <td>footer text</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">btn1</span>
                                                </td>
                                                <td>button1</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">btn2</span>
                                                </td>
                                                <td>button2</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/send-button') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">sender</span>=628xxxx&<span class="text-warning">receiver</span>=628xxxx&<span class="text-warning">message</span>=test&<span class="text-warning">footer</span>=footer&<span class="text-warning">btn1</span>=button1&<span class="text-warning">btn2</span>=button2</code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>POST</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>',
    'sender'  => 'sender number (make sure its scanned)',
    'number'  => 'Number of destination to send message',
    'message' => 'message',
    'footer' => 'message below the button',
    'button1' => 'first button name',
    'button2' => 'second button name',
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/send-button') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data))
);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
                                                </code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-broadcast" role="tabpanel" aria-labelledby="pills-broadcast-tab">
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">sender</span>
                                                </td>
                                                <td>sender number (must have scanned qr)</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">numbers</span>
                                                </td>
                                                <td>array of numbers or comma-separated numbers</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">message</span>
                                                </td>
                                                <td>message content</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/broadcast') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">sender</span>=628xxxx&<span class="text-warning">numbers</span>=628xxxx,628yyyy&<span class="text-warning">message</span>=test</code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>POST</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>',
    'sender'  => 'Sender number (make sure its scanned)',
    'numbers' => ['628xxxx', '628yyyy'], // or "628xxxx,628yyyy"
    'message' => 'the message'
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/broadcast') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data))
);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
                                                </code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-devices" role="tabpanel" aria-labelledby="pills-devices-tab">
                                        <h4>Get All Devices</h4>
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/devices') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?></code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>POST</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>'
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/devices') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data))
);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
                                                </code></pre>
                                            </div>
                                        </div>
                                        
                                        <hr>
                                        
                                        <h4>Get Device Status</h4>
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">sender</span>
                                                </td>
                                                <td>device number</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/device-status') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">sender</span>=628xxxx</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-contacts" role="tabpanel" aria-labelledby="pills-contacts-tab">
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">sender</span>
                                                </td>
                                                <td>device number</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/contacts') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">sender</span>=628xxxx</code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>POST</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>',
    'sender'  => '628xxxx'
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/contacts') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data))
);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
                                                </code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-messages" role="tabpanel" aria-labelledby="pills-messages-tab">
                                        <h4>Get Messages</h4>
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">sender</span>
                                                </td>
                                                <td>device number (optional, if not provided will return messages from all devices)</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">limit</span>
                                                </td>
                                                <td>number of messages to return (default: 50)</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>GET</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/messages') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">sender</span>=628xxxx&<span class="text-warning">limit</span>=20</code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>POST</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>',
    'sender'  => '628xxxx', // optional
    'limit'   => 20 // optional
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/messages') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data))
);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
                </code></pre>
            </div>
        </div>
        
        <hr>
        
        <h4>Check Message Status</h4>
        <table class="table table-striped">
            <tr>
                <td>
                    <span class="text-warning">apikey</span>
                </td>
                <td>Your api key</td>
            </tr>
            <tr>
                <td>
                    <span class="text-warning">message_id</span>
                </td>
                <td>ID of the message to check</td>
            </tr>
        </table>
        <br>
        <h4>GET</h4>
        <div class="example-container">
            <div class="example-code">
                <pre><code class="html"><?= base_url('api/message-status') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">message_id</span>=123</code></pre>
            </div>
        </div>
        <br>
        <h4>POST</h4>
        <div class="example-container">
            <div class="example-code">
                <pre><code class="php">$data = [
    'api_key' => '<?= $user->api_key ?>',
    'message_id' => 123
];

$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "<?= base_url('api/message-status') ?>",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => json_encode($data))
);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
                </code></pre>
            </div>
        </div>
    </div>
                                    <div class="tab-pane fade" id="webhooksss" role="tabpanel" aria-labelledby="pills-contact-tab">
                                        <br>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="php">header('content-type: application/json');
$data = json_decode(file_get_contents('php://input'), true);
file_put_contents('whatsapp.txt', '[' . date('Y-m-d H:i:s') . "]\n" . json_encode($data) . "\n\n", FILE_APPEND);
$message = $data['message']; // it catches incoming message
$from = $data['from']; // it catches message sender number


if (strtolower($message) == 'hai') {
    $result = [
        'mode' => 'chat', // mode chat = normal chat
        'pesan' => 'Hai juga'
    ];
} else if (strtolower($message) == 'hallo') {
    $result = [
        'mode' => 'reply', // mode reply = reply pessan
        'pesan' => 'Halo juga'
    ];
} else if (strtolower($message) == 'gambar') {
    $result = [
        'mode' => 'picture', // type picture = send picture message
        'data' => [
            'caption' => '*webhook picture*',
            'url' => 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRZ2Ox4zgP799q86H56GbPMNWAdQQrfIWD-Mw&usqp=CAU'
        ]
    ];
}

print json_encode($result);
                                                </code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-webhook" role="tabpanel" aria-labelledby="pills-webhook-tab">
                                        <p>Configure a webhook to receive notifications when messages are received or status changes occur.</p>
                                        <table class="table table-striped">
                                            <tr>
                                                <td>
                                                    <span class="text-warning">apikey</span>
                                                </td>
                                                <td>Your api key</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">url</span>
                                                </td>
                                                <td>URL to receive webhook notifications</td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="text-warning">events</span>
                                                </td>
                                                <td>Events to subscribe to (comma-separated): message, status, all (default: all)</td>
                                            </tr>
                                        </table>
                                        <br>
                                        <h4>Configure Webhook</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/webhook') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?>&<span class="text-warning">url</span>=https://your-website.com/webhook&<span class="text-warning">events</span>=all</code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>Delete Webhook</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="html"><?= base_url('api/delete-webhook') ?>?<span class="text-warning">apikey</span>=<?= $user->api_key ?></code></pre>
                                            </div>
                                        </div>
                                        <br>
                                        <h4>Webhook Payload Example</h4>
                                        <div class="example-container">
                                            <div class="example-code">
                                                <pre><code class="json">{
    "event": "message",
    "data": {
        "id": "ABCDEF123456",
        "from": "628xxxxxxxx",
        "to": "628xxxxxxxx",
        "message": "Hello, this is a test message",
        "timestamp": "2023-05-24T13:06:46.403Z",
        "type": "text"
    }
}</code></pre>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="p-integration" role="tabpanel" aria-labelledby="pills-integration-tab">
                                        <h4>Integration Examples</h4>
                                        <p>Below are examples of how to integrate this WhatsApp API with various programming languages and frameworks.</p>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5>PHP (Native)</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Examples for sending messages using native PHP.</p>
                                                        <a href="<?= base_url('examples/php') ?>" class="btn btn-primary">View Examples</a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6 mb-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5>Laravel</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Integration examples for Laravel framework.</p>
                                                        <a href="<?= base_url('examples/laravel') ?>" class="btn btn-primary">View Examples</a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6 mb-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5>CodeIgniter</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Integration examples for CodeIgniter framework.</p>
                                                        <a href="<?= base_url('examples/codeigniter') ?>" class="btn btn-primary">View Examples</a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6 mb-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5>JavaScript/Node.js</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Examples for integrating with JavaScript and Node.js.</p>
                                                        <a href="<?= base_url('examples/javascript') ?>" class="btn btn-primary">View Examples</a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6 mb-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5>Python</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Integration examples for Python applications.</p>
                                                        <a href="<?= base_url('examples/python') ?>" class="btn btn-primary">View Examples</a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6 mb-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5>Webhook Handler</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>Examples for handling incoming webhook notifications.</p>
                                                        <a href="<?= base_url('examples/webhook') ?>" class="btn btn-primary">View Examples</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Javascripts -->
    <script src="<?= _assets() ?>/plugins/jquery/jquery-3.5.1.min.js"></script>
    <script src="<?= _assets() ?>/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="<?= _assets() ?>/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
    <script src="<?= _assets() ?>/plugins/pace/pace.min.js"></script>
    <script src="<?= _assets() ?>/plugins/select2/js/select2.full.min.js"></script>
    <script src="<?= _assets() ?>/plugins/highlight/highlight.pack.js"></script>
    <script src="<?= _assets() ?>/js/main.min.js"></script>
    <script src="<?= _assets() ?>/js/custom.js"></script>
    <script>
        $('select').select2();
    </script>
    <?php require_once('include_file.php') ?>
</body>

</html>

// Tambahkan di dalam fungsi index() atau create_tables()

// Tabel untuk webhook
$this->db->query("CREATE TABLE IF NOT EXISTS `webhooks` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `url` varchar(255) NOT NULL,
    `events` varchar(255) NOT NULL DEFAULT 'all',
    `created_at` datetime NOT NULL,
    `updated_at` datetime NOT NULL,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

// Tabel untuk menyimpan pesan masuk
$this->db->query("CREATE TABLE IF NOT EXISTS `messages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `message_id` varchar(255) NOT NULL,
    `sender` varchar(50) NOT NULL,
    `receiver` varchar(50) NOT NULL,
    `message` text NOT NULL,
    `type` varchar(50) NOT NULL,
    `timestamp` datetime NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
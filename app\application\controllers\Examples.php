<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Examples extends CI_Controller {

    public function __construct() {
        parent::__construct();
        // Perbaikan pengecekan login - gunakan is_login() helper
        if (!is_login()) {
            redirect(base_url('login'));
        }
        date_default_timezone_set('Asia/Jakarta');
    }

    public function php() {
        $data['title'] = 'PHP Integration Examples';
        // Perbaikan ID user - gunakan id_login sesuai dengan session
        $data['user'] = $this->db->get_where('account', ['id' => $this->session->userdata('id_login')])->row();
        
        view('examples/php', $data); // Gunakan helper view() bukan load->view
    }

    public function laravel() {
        $data['title'] = 'Laravel Integration Examples';
        $data['user'] = $this->db->get_where('account', ['id' => $this->session->userdata('id_login')])->row();
        
        view('examples/laravel', $data);
    }

    public function codeigniter() {
        $data['title'] = 'CodeIgniter Integration Examples';
        $data['user'] = $this->db->get_where('account', ['id' => $this->session->userdata('id_login')])->row();
        
        view('examples/codeigniter', $data);
    }

    public function javascript() {
        $data['title'] = 'JavaScript Integration Examples';
        $data['user'] = $this->db->get_where('account', ['id' => $this->session->userdata('id_login')])->row();
        
        view('examples/javascript', $data);
    }

    public function python() {
        $data['title'] = 'Python Integration Examples';
        $data['user'] = $this->db->get_where('account', ['id' => $this->session->userdata('id_login')])->row();
        
        view('examples/python', $data);
    }

    public function webhook() {
        // Debugging
        error_log('Examples/webhook method called');
        
        $data['title'] = 'Webhook Handler Examples';
        $data['user'] = $this->db->get_where('account', ['id' => $this->session->userdata('id_login')])->row();
        
        view('examples/webhook', $data);
    }
}



{"version": 3, "file": "popper.min.js", "sources": ["../../src/dom-utils/getBoundingClientRect.js", "../../src/dom-utils/getWindow.js", "../../src/dom-utils/getWindowScroll.js", "../../src/dom-utils/instanceOf.js", "../../src/dom-utils/getNodeName.js", "../../src/dom-utils/getDocumentElement.js", "../../src/dom-utils/getWindowScrollBarX.js", "../../src/dom-utils/getComputedStyle.js", "../../src/dom-utils/isScrollParent.js", "../../src/dom-utils/getCompositeRect.js", "../../src/dom-utils/getNodeScroll.js", "../../src/dom-utils/getHTMLElementScroll.js", "../../src/dom-utils/getLayoutRect.js", "../../src/dom-utils/getParentNode.js", "../../src/dom-utils/getScrollParent.js", "../../src/dom-utils/listScrollParents.js", "../../src/dom-utils/getOffsetParent.js", "../../src/dom-utils/isTableElement.js", "../../src/utils/orderModifiers.js", "../../src/utils/debounce.js", "../../src/utils/getBasePlacement.js", "../../src/dom-utils/contains.js", "../../src/utils/rectToClientRect.js", "../../src/dom-utils/getClippingRect.js", "../../src/enums.js", "../../src/dom-utils/getViewportRect.js", "../../src/dom-utils/getDocumentRect.js", "../../src/utils/getMainAxisFromPlacement.js", "../../src/utils/computeOffsets.js", "../../src/utils/getVariation.js", "../../src/utils/mergePaddingObject.js", "../../src/utils/getFreshSideObject.js", "../../src/utils/expandToHashMap.js", "../../src/utils/detectOverflow.js", "../../src/createPopper.js", "../../src/utils/mergeByName.js", "../../src/modifiers/computeStyles.js", "../../src/utils/getOppositePlacement.js", "../../src/utils/getOppositeVariationPlacement.js", "../../src/modifiers/hide.js", "../../src/utils/math.js", "../../src/modifiers/eventListeners.js", "../../src/modifiers/popperOffsets.js", "../../src/modifiers/applyStyles.js", "../../src/modifiers/offset.js", "../../src/modifiers/flip.js", "../../src/utils/computeAutoPlacement.js", "../../src/modifiers/preventOverflow.js", "../../src/utils/getAltAxis.js", "../../src/utils/within.js", "../../src/modifiers/arrow.js", "../../src/popper-lite.js", "../../src/popper.js"], "sourcesContent": ["// @flow\nimport type { ClientRectObject, VirtualElement } from '../types';\n\nexport default function getBoundingClientRect(\n  element: Element | VirtualElement\n): ClientRectObject {\n  const rect = element.getBoundingClientRect();\n\n  return {\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    y: rect.top,\n  };\n}\n", "// @flow\nimport type { Window } from '../types';\ndeclare function getWindow(node: Node | Window): Window;\n\nexport default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    const ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport type { Window } from '../types';\n\nexport default function getWindowScroll(node: Node | Window) {\n  const win = getWindow(node);\n  const scrollLeft = win.pageXOffset;\n  const scrollTop = win.pageYOffset;\n\n  return {\n    scrollLeft,\n    scrollTop,\n  };\n}\n", "// @flow\nimport getWindow from './getWindow';\n\ndeclare function isElement(node: mixed): boolean %checks(node instanceof\n  Element);\nfunction isElement(node) {\n  const OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\ndeclare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement);\nfunction isHTMLElement(node) {\n  const OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\ndeclare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot);\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };\n", "// @flow\nimport type { Window } from '../types';\n\nexport default function getNodeName(element: ?Node | Window): ?string {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n", "// @flow\nimport { isElement } from './instanceOf';\nimport type { Window } from '../types';\n\nexport default function getDocumentElement(\n  element: Element | Window\n): HTMLElement {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (\n    (isElement(element)\n      ? element.ownerDocument\n      : // $FlowFixMe[prop-missing]\n        element.document) || window.document\n  ).documentElement;\n}\n", "// @flow\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScroll from './getWindowScroll';\n\nexport default function getWindowScrollBarX(element: Element): number {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (\n    getBoundingClientRect(getDocumentElement(element)).left +\n    getWindowScroll(element).scrollLeft\n  );\n}\n", "// @flow\nimport getWindow from './getWindow';\n\nexport default function getComputedStyle(\n  element: Element\n): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "// @flow\nimport getComputedStyle from './getComputedStyle';\n\nexport default function isScrollParent(element: HTMLElement): boolean {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n", "// @flow\nimport type { Rect, VirtualElement, Window } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getNodeScroll from './getNodeScroll';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getDocumentElement from './getDocumentElement';\nimport isScrollParent from './isScrollParent';\n\n// Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nexport default function getCompositeRect(\n  elementOrVirtualElement: Element | VirtualElement,\n  offsetParent: Element | Window,\n  isFixed: boolean = false\n): Rect {\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(elementOrVirtualElement);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n\n  let scroll = { scrollLeft: 0, scrollTop: 0 };\n  let offsets = { x: 0, y: 0 };\n\n  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {\n    if (\n      getNodeName(offsetParent) !== 'body' ||\n      // https://github.com/popperjs/popper-core/issues/1078\n      isScrollParent(documentElement)\n    ) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height,\n  };\n}\n", "// @flow\nimport getWindowScroll from './getWindowScroll';\nimport getWindow from './getWindow';\nimport { isHTMLElement } from './instanceOf';\nimport getHTMLElementScroll from './getHTMLElementScroll';\nimport type { Window } from '../types';\n\nexport default function getNodeScroll(node: Node | Window) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n", "// @flow\n\nexport default function getHTMLElementScroll(element: HTMLElement) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\n\n// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element: HTMLElement): Rect {\n  const clientRect = getBoundingClientRect(element);\n\n  // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width,\n    height,\n  };\n}\n", "// @flow\nimport getNodeName from './getNodeName';\nimport getDocumentElement from './getDocumentElement';\nimport { isShadowRoot } from './instanceOf';\n\nexport default function getParentNode(element: Node | ShadowRoot): Node {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}\n", "// @flow\nimport getParentNode from './getParentNode';\nimport isScrollParent from './isScrollParent';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\n\nexport default function getScrollParent(node: Node): HTMLElement {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n", "// @flow\nimport getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getWindow from './getWindow';\nimport type { Window, VisualViewport } from '../types';\nimport isScrollParent from './isScrollParent';\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\nexport default function listScrollParents(\n  element: Node,\n  list: Array<Element | Window> = []\n): Array<Element | Window | VisualViewport> {\n  const scrollParent = getScrollParent(element);\n  const isBody = scrollParent === element.ownerDocument?.body;\n  const win = getWindow(scrollParent);\n  const target = isBody\n    ? [win].concat(\n        win.visualViewport || [],\n        isScrollParent(scrollParent) ? scrollParent : []\n      )\n    : scrollParent;\n  const updatedList = list.concat(target);\n\n  return isBody\n    ? updatedList\n    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n      updatedList.concat(listScrollParents(getParentNode(target)));\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getNodeName from './getNodeName';\nimport getComputedStyle from './getComputedStyle';\nimport { isHTMLElement } from './instanceOf';\nimport isTableElement from './isTableElement';\nimport getParentNode from './getParentNode';\n\nfunction getTrueOffsetParent(element: Element): ?Element {\n  if (\n    !isHTMLElement(element) ||\n    // https://github.com/popperjs/popper-core/issues/837\n    getComputedStyle(element).position === 'fixed'\n  ) {\n    return null;\n  }\n\n  return element.offsetParent;\n}\n\n// `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element: Element) {\n  const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  let currentNode = getParentNode(element);\n\n  while (\n    isHTMLElement(currentNode) &&\n    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0\n  ) {\n    const css = getComputedStyle(currentNode);\n\n    // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    if (\n      css.transform !== 'none' ||\n      css.perspective !== 'none' ||\n      css.contain === 'paint' ||\n      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||\n      (isFirefox && css.willChange === 'filter') ||\n      (isFirefox && css.filter && css.filter !== 'none')\n    ) {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nexport default function getOffsetParent(element: Element) {\n  const window = getWindow(element);\n\n  let offsetParent = getTrueOffsetParent(element);\n\n  while (\n    offsetParent &&\n    isTableElement(offsetParent) &&\n    getComputedStyle(offsetParent).position === 'static'\n  ) {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (\n    offsetParent &&\n    (getNodeName(offsetParent) === 'html' ||\n      (getNodeName(offsetParent) === 'body' &&\n        getComputedStyle(offsetParent).position === 'static'))\n  ) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n", "// @flow\nimport getNodeName from './getNodeName';\n\nexport default function isTableElement(element: Element): boolean {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n", "// @flow\nimport type { Modifier } from '../types';\nimport { modifierPhases } from '../enums';\n\n// source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n  const map = new Map();\n  const visited = new Set();\n  const result = [];\n\n  modifiers.forEach(modifier => {\n    map.set(modifier.name, modifier);\n  });\n\n  // On visiting object, check for its dependencies and visit them recursively\n  function sort(modifier: Modifier<any, any>) {\n    visited.add(modifier.name);\n\n    const requires = [\n      ...(modifier.requires || []),\n      ...(modifier.requiresIfExists || []),\n    ];\n\n    requires.forEach(dep => {\n      if (!visited.has(dep)) {\n        const depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n\n    result.push(modifier);\n  }\n\n  modifiers.forEach(modifier => {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n\n  return result;\n}\n\nexport default function orderModifiers(\n  modifiers: Array<Modifier<any, any>>\n): Array<Modifier<any, any>> {\n  // order based on dependencies\n  const orderedModifiers = order(modifiers);\n\n  // order based on phase\n  return modifierPhases.reduce((acc, phase) => {\n    return acc.concat(\n      orderedModifiers.filter(modifier => modifier.phase === phase)\n    );\n  }, []);\n}\n", "// @flow\n\nexport default function debounce<T>(fn: Function): () => Promise<T> {\n  let pending;\n  return () => {\n    if (!pending) {\n      pending = new Promise<T>(resolve => {\n        Promise.resolve().then(() => {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n", "// @flow\nimport { type BasePlacement, type Placement, auto } from '../enums';\n\nexport default function getBasePlacement(\n  placement: Placement | typeof auto\n): BasePlacement {\n  return (placement.split('-')[0]: any);\n}\n", "// @flow\nimport { isShadowRoot } from './instanceOf';\n\nexport default function contains(parent: Element, child: Element) {\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      }\n      // $FlowFixMe[prop-missing]: need a better way to handle this...\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n", "// @flow\nimport type { Rect, ClientRectObject } from '../types';\n\nexport default function rectToClientRect(rect: Rect): ClientRectObject {\n  return {\n    ...rect,\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height,\n  };\n}\n", "// @flow\nimport type { ClientRectObject } from '../types';\nimport type { Boundary, RootBoundary } from '../enums';\nimport { viewport } from '../enums';\nimport getViewportRect from './getViewportRect';\nimport getDocumentRect from './getDocumentRect';\nimport listScrollParents from './listScrollParents';\nimport getOffsetParent from './getOffsetParent';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getParentNode from './getParentNode';\nimport contains from './contains';\nimport getNodeName from './getNodeName';\nimport rectToClientRect from '../utils/rectToClientRect';\nimport { max, min } from '../utils/math';\n\nfunction getInnerBoundingClientRect(element: Element) {\n  const rect = getBoundingClientRect(element);\n\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n\n  return rect;\n}\n\nfunction getClientRectFromMixedType(\n  element: Element,\n  clippingParent: Element | RootBoundary\n): ClientRectObject {\n  return clippingParent === viewport\n    ? rectToClientRect(getViewportRect(element))\n    : isHTMLElement(clippingParent)\n    ? getInnerBoundingClientRect(clippingParent)\n    : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n}\n\n// A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element: Element): Array<Element> {\n  const clippingParents = listScrollParents(getParentNode(element));\n  const canEscapeClipping =\n    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  const clipperElement =\n    canEscapeClipping && isHTMLElement(element)\n      ? getOffsetParent(element)\n      : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  }\n\n  // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n  return clippingParents.filter(\n    (clippingParent) =>\n      isElement(clippingParent) &&\n      contains(clippingParent, clipperElement) &&\n      getNodeName(clippingParent) !== 'body'\n  );\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nexport default function getClippingRect(\n  element: Element,\n  boundary: Boundary,\n  rootBoundary: RootBoundary\n): ClientRectObject {\n  const mainClippingParents =\n    boundary === 'clippingParents'\n      ? getClippingParents(element)\n      : [].concat(boundary);\n  const clippingParents = [...mainClippingParents, rootBoundary];\n  const firstClippingParent = clippingParents[0];\n\n  const clippingRect = clippingParents.reduce((accRect, clippingParent) => {\n    const rect = getClientRectFromMixedType(element, clippingParent);\n\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n\n  return clippingRect;\n}\n", "// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary =\n  | HTMLElement\n  | Array<HTMLElement>\n  | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n", "// @flow\nimport getWindow from './getWindow';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScrollBarX from './getWindowScrollBarX';\n\nexport default function getViewportRect(element: Element) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n\n  // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n\n    // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width,\n    height,\n    x: x + getWindowScrollBarX(element),\n    y,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getWindowScroll from './getWindowScroll';\nimport { max } from '../utils/math';\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nexport default function getDocumentRect(element: HTMLElement): Rect {\n  const html = getDocumentElement(element);\n  const winScroll = getWindowScroll(element);\n  const body = element.ownerDocument?.body;\n\n  const width = max(\n    html.scrollWidth,\n    html.clientWidth,\n    body ? body.scrollWidth : 0,\n    body ? body.clientWidth : 0\n  );\n  const height = max(\n    html.scrollHeight,\n    html.clientHeight,\n    body ? body.scrollHeight : 0,\n    body ? body.clientHeight : 0\n  );\n\n  let x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return { width, height, x, y };\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nexport default function getMainAxisFromPlacement(\n  placement: Placement\n): 'x' | 'y' {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n", "// @flow\nimport getBasePlacement from './getBasePlacement';\nimport getVariation from './getVariation';\nimport getMainAxisFromPlacement from './getMainAxisFromPlacement';\nimport type {\n  Rect,\n  PositioningStrategy,\n  Offsets,\n  ClientRectObject,\n} from '../types';\nimport { top, right, bottom, left, start, end, type Placement } from '../enums';\n\nexport default function computeOffsets({\n  reference,\n  element,\n  placement,\n}: {\n  reference: Rect | ClientRectObject,\n  element: Rect | ClientRectObject,\n  strategy: PositioningStrategy,\n  placement?: Placement,\n}): Offsets {\n  const basePlacement = placement ? getBasePlacement(placement) : null;\n  const variation = placement ? getVariation(placement) : null;\n  const commonX = reference.x + reference.width / 2 - element.width / 2;\n  const commonY = reference.y + reference.height / 2 - element.height / 2;\n\n  let offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height,\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height,\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY,\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY,\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y,\n      };\n  }\n\n  const mainAxis = basePlacement\n    ? getMainAxisFromPlacement(basePlacement)\n    : null;\n\n  if (mainAxis != null) {\n    const len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] =\n          offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] =\n          offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n\n  return offsets;\n}\n", "// @flow\nimport { type Variation, type Placement } from '../enums';\n\nexport default function getVariation(placement: Placement): ?Variation {\n  return (placement.split('-')[1]: any);\n}\n", "// @flow\nimport type { SideObject } from '../types';\nimport getFreshSideObject from './getFreshSideObject';\n\nexport default function mergePaddingObject(\n  paddingObject: $Shape<SideObject>\n): SideObject {\n  return {\n    ...getFreshSideObject(),\n    ...paddingObject,\n  };\n}\n", "// @flow\nimport type { SideObject } from '../types';\n\nexport default function getFreshSideObject(): SideObject {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n  };\n}\n", "// @flow\n\nexport default function expandToHashMap<\n  T: number | string | boolean,\n  K: string\n>(value: T, keys: Array<K>): { [key: string]: T } {\n  return keys.reduce((hashMap, key) => {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n", "// @flow\nimport type { State, SideObject, Padding } from '../types';\nimport type { Placement, Boundary, RootBoundary, Context } from '../enums';\nimport getBoundingClientRect from '../dom-utils/getBoundingClientRect';\nimport getClippingRect from '../dom-utils/getClippingRect';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport computeOffsets from './computeOffsets';\nimport rectToClientRect from './rectToClientRect';\nimport {\n  clippingParents,\n  reference,\n  popper,\n  bottom,\n  top,\n  right,\n  basePlacements,\n  viewport,\n} from '../enums';\nimport { isElement } from '../dom-utils/instanceOf';\nimport mergePaddingObject from './mergePaddingObject';\nimport expandToHashMap from './expandToHashMap';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  placement: Placement,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  elementContext: Context,\n  altBoundary: boolean,\n  padding: Padding,\n};\n\nexport default function detectOverflow(\n  state: State,\n  options: $Shape<Options> = {}\n): SideObject {\n  const {\n    placement = state.placement,\n    boundary = clippingParents,\n    rootBoundary = viewport,\n    elementContext = popper,\n    altBoundary = false,\n    padding = 0,\n  } = options;\n\n  const paddingObject = mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n\n  const altContext = elementContext === popper ? reference : popper;\n\n  const referenceElement = state.elements.reference;\n  const popperRect = state.rects.popper;\n  const element = state.elements[altBoundary ? altContext : elementContext];\n\n  const clippingClientRect = getClippingRect(\n    isElement(element)\n      ? element\n      : element.contextElement || getDocumentElement(state.elements.popper),\n    boundary,\n    rootBoundary\n  );\n\n  const referenceClientRect = getBoundingClientRect(referenceElement);\n\n  const popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement,\n  });\n\n  const popperClientRect = rectToClientRect({\n    ...popperRect,\n    ...popperOffsets,\n  });\n\n  const elementClientRect =\n    elementContext === popper ? popperClientRect : referenceClientRect;\n\n  // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n  const overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom:\n      elementClientRect.bottom -\n      clippingClientRect.bottom +\n      paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right:\n      elementClientRect.right - clippingClientRect.right + paddingObject.right,\n  };\n\n  const offsetData = state.modifiersData.offset;\n\n  // Offsets can be applied only to the popper element\n  if (elementContext === popper && offsetData) {\n    const offset = offsetData[placement];\n\n    Object.keys(overflowOffsets).forEach((key) => {\n      const multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      const axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n", "// @flow\nimport type {\n  State,\n  OptionsGeneric,\n  Modifier,\n  Instance,\n  VirtualElement,\n} from './types';\nimport getCompositeRect from './dom-utils/getCompositeRect';\nimport getLayoutRect from './dom-utils/getLayoutRect';\nimport listScrollParents from './dom-utils/listScrollParents';\nimport getOffsetParent from './dom-utils/getOffsetParent';\nimport getComputedStyle from './dom-utils/getComputedStyle';\nimport orderModifiers from './utils/orderModifiers';\nimport debounce from './utils/debounce';\nimport validateModifiers from './utils/validateModifiers';\nimport uniqueBy from './utils/uniqueBy';\nimport getBasePlacement from './utils/getBasePlacement';\nimport mergeByName from './utils/mergeByName';\nimport detectOverflow from './utils/detectOverflow';\nimport { isElement } from './dom-utils/instanceOf';\nimport { auto } from './enums';\n\nconst INVALID_ELEMENT_ERROR =\n  'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nconst INFINITE_LOOP_ERROR =\n  'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\n\nconst DEFAULT_OPTIONS: OptionsGeneric<any> = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute',\n};\n\ntype PopperGeneratorArgs = {\n  defaultModifiers?: Array<Modifier<any, any>>,\n  defaultOptions?: $Shape<OptionsGeneric<any>>,\n};\n\nfunction areValidElements(...args: Array<any>): boolean {\n  return !args.some(\n    (element) =>\n      !(element && typeof element.getBoundingClientRect === 'function')\n  );\n}\n\nexport function popperGenerator(generatorOptions: PopperGeneratorArgs = {}) {\n  const {\n    defaultModifiers = [],\n    defaultOptions = DEFAULT_OPTIONS,\n  } = generatorOptions;\n\n  return function createPopper<TModifier: $Shape<Modifier<any, any>>>(\n    reference: Element | VirtualElement,\n    popper: HTMLElement,\n    options: $Shape<OptionsGeneric<TModifier>> = defaultOptions\n  ): Instance {\n    let state: $Shape<State> = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: { ...DEFAULT_OPTIONS, ...defaultOptions },\n      modifiersData: {},\n      elements: {\n        reference,\n        popper,\n      },\n      attributes: {},\n      styles: {},\n    };\n\n    let effectCleanupFns: Array<() => void> = [];\n    let isDestroyed = false;\n\n    const instance = {\n      state,\n      setOptions(options) {\n        cleanupModifierEffects();\n\n        state.options = {\n          // $FlowFixMe[exponential-spread]\n          ...defaultOptions,\n          ...state.options,\n          ...options,\n        };\n\n        state.scrollParents = {\n          reference: isElement(reference)\n            ? listScrollParents(reference)\n            : reference.contextElement\n            ? listScrollParents(reference.contextElement)\n            : [],\n          popper: listScrollParents(popper),\n        };\n\n        // Orders the modifiers based on their dependencies and `phase`\n        // properties\n        const orderedModifiers = orderModifiers(\n          mergeByName([...defaultModifiers, ...state.options.modifiers])\n        );\n\n        // Strip out disabled modifiers\n        state.orderedModifiers = orderedModifiers.filter((m) => m.enabled);\n\n        // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n        if (__DEV__) {\n          const modifiers = uniqueBy(\n            [...orderedModifiers, ...state.options.modifiers],\n            ({ name }) => name\n          );\n\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            const flipModifier = state.orderedModifiers.find(\n              ({ name }) => name === 'flip'\n            );\n\n            if (!flipModifier) {\n              console.error(\n                [\n                  'Popper: \"auto\" placements require the \"flip\" modifier be',\n                  'present and enabled to work.',\n                ].join(' ')\n              );\n            }\n          }\n\n          const {\n            marginTop,\n            marginRight,\n            marginBottom,\n            marginLeft,\n          } = getComputedStyle(popper);\n\n          // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n          if (\n            [marginTop, marginRight, marginBottom, marginLeft].some((margin) =>\n              parseFloat(margin)\n            )\n          ) {\n            console.warn(\n              [\n                'Popper: CSS \"margin\" styles cannot be used to apply padding',\n                'between the popper and its reference element or boundary.',\n                'To replicate margin, use the `offset` modifier, as well as',\n                'the `padding` option in the `preventOverflow` and `flip`',\n                'modifiers.',\n              ].join(' ')\n            );\n          }\n        }\n\n        runModifierEffects();\n\n        return instance.update();\n      },\n\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        const { reference, popper } = state.elements;\n\n        // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n        if (!areValidElements(reference, popper)) {\n          if (__DEV__) {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n          return;\n        }\n\n        // Store the reference and popper rects to be read by modifiers\n        state.rects = {\n          reference: getCompositeRect(\n            reference,\n            getOffsetParent(popper),\n            state.options.strategy === 'fixed'\n          ),\n          popper: getLayoutRect(popper),\n        };\n\n        // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n        state.reset = false;\n\n        state.placement = state.options.placement;\n\n        // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n        state.orderedModifiers.forEach(\n          (modifier) =>\n            (state.modifiersData[modifier.name] = {\n              ...modifier.data,\n            })\n        );\n\n        let __debug_loops__ = 0;\n        for (let index = 0; index < state.orderedModifiers.length; index++) {\n          if (__DEV__) {\n            __debug_loops__ += 1;\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          const { fn, options = {}, name } = state.orderedModifiers[index];\n\n          if (typeof fn === 'function') {\n            state = fn({ state, options, name, instance }) || state;\n          }\n        }\n      },\n\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce<$Shape<State>>(\n        () =>\n          new Promise<$Shape<State>>((resolve) => {\n            instance.forceUpdate();\n            resolve(state);\n          })\n      ),\n\n      destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      },\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (__DEV__) {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n      return instance;\n    }\n\n    instance.setOptions(options).then((state) => {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    });\n\n    // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(({ name, options = {}, effect }) => {\n        if (typeof effect === 'function') {\n          const cleanupFn = effect({ state, name, instance, options });\n          const noopFn = () => {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach((fn) => fn());\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nexport const createPopper = popperGenerator();\n\n// eslint-disable-next-line import/no-unused-modules\nexport { detectOverflow };\n", "// @flow\nimport type { Modifier } from '../types';\n\nexport default function mergeByName(\n  modifiers: Array<$Shape<Modifier<any, any>>>\n): Array<$Shape<Modifier<any, any>>> {\n  const merged = modifiers.reduce((merged, current) => {\n    const existing = merged[current.name];\n    merged[current.name] = existing\n      ? {\n          ...existing,\n          ...current,\n          options: { ...existing.options, ...current.options },\n          data: { ...existing.data, ...current.data },\n        }\n      : current;\n    return merged;\n  }, {});\n\n  // IE11 does not support Object.values\n  return Object.keys(merged).map(key => merged[key]);\n}\n", "// @flow\nimport type {\n  PositioningStrategy,\n  Offsets,\n  Modifier,\n  ModifierArguments,\n  Rect,\n  Window,\n} from '../types';\nimport { type BasePlacement, top, left, right, bottom } from '../enums';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getWindow from '../dom-utils/getWindow';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getComputedStyle from '../dom-utils/getComputedStyle';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport { round } from '../utils/math';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type RoundOffsets = (\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>\n) => Offsets;\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets?: boolean | RoundOffsets,\n};\n\nconst unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto',\n};\n\n// Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\nfunction roundOffsetsByDPR({ x, y }): Offsets {\n  const win: Window = window;\n  const dpr = win.devicePixelRatio || 1;\n\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0,\n  };\n}\n\nexport function mapToStyles({\n  popper,\n  popperRect,\n  placement,\n  offsets,\n  position,\n  gpuAcceleration,\n  adaptive,\n  roundOffsets,\n}: {\n  popper: HTMLElement,\n  popperRect: Rect,\n  placement: BasePlacement,\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>,\n  position: PositioningStrategy,\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets: boolean | RoundOffsets,\n}) {\n  let { x = 0, y = 0 } =\n    roundOffsets === true\n      ? roundOffsetsByDPR(offsets)\n      : typeof roundOffsets === 'function'\n      ? roundOffsets(offsets)\n      : offsets;\n\n  const hasX = offsets.hasOwnProperty('x');\n  const hasY = offsets.hasOwnProperty('y');\n\n  let sideX: string = left;\n  let sideY: string = top;\n\n  const win: Window = window;\n\n  if (adaptive) {\n    let offsetParent = getOffsetParent(popper);\n    let heightProp = 'clientHeight';\n    let widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    }\n\n    // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n    offsetParent = (offsetParent: Element);\n\n    if (placement === top) {\n      sideY = bottom;\n      // $FlowFixMe[prop-missing]\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left) {\n      sideX = right;\n      // $FlowFixMe[prop-missing]\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  const commonStyles = {\n    position,\n    ...(adaptive && unsetSides),\n  };\n\n  if (gpuAcceleration) {\n    return {\n      ...commonStyles,\n      [sideY]: hasY ? '0' : '',\n      [sideX]: hasX ? '0' : '',\n      // Layer acceleration can disable subpixel rendering which causes slightly\n      // blurry text on low PPI displays, so we want to use 2D transforms\n      // instead\n      transform:\n        (win.devicePixelRatio || 1) < 2\n          ? `translate(${x}px, ${y}px)`\n          : `translate3d(${x}px, ${y}px, 0)`,\n    };\n  }\n\n  return {\n    ...commonStyles,\n    [sideY]: hasY ? `${y}px` : '',\n    [sideX]: hasX ? `${x}px` : '',\n    transform: '',\n  };\n}\n\nfunction computeStyles({ state, options }: ModifierArguments<Options>) {\n  const {\n    gpuAcceleration = true,\n    adaptive = true,\n    // defaults to use builtin `roundOffsetsByDPR`\n    roundOffsets = true,\n  } = options;\n\n  if (__DEV__) {\n    const transitionProperty =\n      getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (\n      adaptive &&\n      ['transform', 'top', 'right', 'bottom', 'left'].some(\n        (property) => transitionProperty.indexOf(property) >= 0\n      )\n    ) {\n      console.warn(\n        [\n          'Popper: Detected CSS transitions on at least one of the following',\n          'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".',\n          '\\n\\n',\n          'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow',\n          'for smooth transitions, or remove these properties from the CSS',\n          'transition declaration on the popper element if only transitioning',\n          'opacity or background-color for example.',\n          '\\n\\n',\n          'We recommend using the popper element as a wrapper around an inner',\n          'element that can have any CSS property transitioned for animations.',\n        ].join(' ')\n      );\n    }\n  }\n\n  const commonStyles = {\n    placement: getBasePlacement(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration,\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = {\n      ...state.styles.popper,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.popperOffsets,\n        position: state.options.strategy,\n        adaptive,\n        roundOffsets,\n      }),\n    };\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = {\n      ...state.styles.arrow,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.arrow,\n        position: 'absolute',\n        adaptive: false,\n        roundOffsets,\n      }),\n    };\n  }\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-placement': state.placement,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ComputeStylesModifier = Modifier<'computeStyles', Options>;\nexport default ({\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {},\n}: ComputeStylesModifier);\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n\nexport default function getOppositePlacement(placement: Placement): Placement {\n  return (placement.replace(\n    /left|right|bottom|top/g,\n    matched => hash[matched]\n  ): any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { start: 'end', end: 'start' };\n\nexport default function getOppositeVariationPlacement(\n  placement: Placement\n): Placement {\n  return (placement.replace(/start|end/g, matched => hash[matched]): any);\n}\n", "// @flow\nimport type {\n  ModifierArguments,\n  Modifier,\n  Rect,\n  SideObject,\n  Offsets,\n} from '../types';\nimport { top, bottom, left, right } from '../enums';\nimport detectOverflow from '../utils/detectOverflow';\n\nfunction getSideOffsets(\n  overflow: SideObject,\n  rect: Rect,\n  preventedOffsets: Offsets = { x: 0, y: 0 }\n): SideObject {\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x,\n  };\n}\n\nfunction isAnySideFullyClipped(overflow: SideObject): boolean {\n  return [top, right, bottom, left].some((side) => overflow[side] >= 0);\n}\n\nfunction hide({ state, name }: ModifierArguments<{||}>) {\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const preventedOffsets = state.modifiersData.preventOverflow;\n\n  const referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference',\n  });\n  const popperAltOverflow = detectOverflow(state, {\n    altBoundary: true,\n  });\n\n  const referenceClippingOffsets = getSideOffsets(\n    referenceOverflow,\n    referenceRect\n  );\n  const popperEscapeOffsets = getSideOffsets(\n    popperAltOverflow,\n    popperRect,\n    preventedOffsets\n  );\n\n  const isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  const hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n\n  state.modifiersData[name] = {\n    referenceClippingOffsets,\n    popperEscapeOffsets,\n    isReferenceHidden,\n    hasPopperEscaped,\n  };\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type HideModifier = Modifier<'hide', {||}>;\nexport default ({\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide,\n}: HideModifier);\n", "// @flow\nexport const max = Math.max;\nexport const min = Math.min;\nexport const round = Math.round;\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport getWindow from '../dom-utils/getWindow';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  scroll: boolean,\n  resize: boolean,\n};\n\nconst passive = { passive: true };\n\nfunction effect({ state, instance, options }: ModifierArguments<Options>) {\n  const { scroll = true, resize = true } = options;\n\n  const window = getWindow(state.elements.popper);\n  const scrollParents = [\n    ...state.scrollParents.reference,\n    ...state.scrollParents.popper,\n  ];\n\n  if (scroll) {\n    scrollParents.forEach(scrollParent => {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return () => {\n    if (scroll) {\n      scrollParents.forEach(scrollParent => {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type EventListenersModifier = Modifier<'eventListeners', Options>;\nexport default ({\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: () => {},\n  effect,\n  data: {},\n}: EventListenersModifier);\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport computeOffsets from '../utils/computeOffsets';\n\nfunction popperOffsets({ state, name }: ModifierArguments<{||}>) {\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement,\n  });\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PopperOffsetsModifier = Modifier<'popperOffsets', {||}>;\nexport default ({\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {},\n}: PopperOffsetsModifier);\n", "// @flow\nimport type { Modifier, ModifierArguments } from '../types';\nimport getNodeName from '../dom-utils/getNodeName';\nimport { isHTMLElement } from '../dom-utils/instanceOf';\n\n// This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles({ state }: ModifierArguments<{||}>) {\n  Object.keys(state.elements).forEach((name) => {\n    const style = state.styles[name] || {};\n\n    const attributes = state.attributes[name] || {};\n    const element = state.elements[name];\n\n    // arrow is optional + virtual elements\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    }\n\n    // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n    Object.assign(element.style, style);\n\n    Object.keys(attributes).forEach((name) => {\n      const value = attributes[name];\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect({ state }: ModifierArguments<{||}>) {\n  const initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0',\n    },\n    arrow: {\n      position: 'absolute',\n    },\n    reference: {},\n  };\n\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return () => {\n    Object.keys(state.elements).forEach((name) => {\n      const element = state.elements[name];\n      const attributes = state.attributes[name] || {};\n\n      const styleProperties = Object.keys(\n        state.styles.hasOwnProperty(name)\n          ? state.styles[name]\n          : initialStyles[name]\n      );\n\n      // Set all values to an empty string to unset them\n      const style = styleProperties.reduce((style, property) => {\n        style[property] = '';\n        return style;\n      }, {});\n\n      // arrow is optional + virtual elements\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n\n      Object.keys(attributes).forEach((attribute) => {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ApplyStylesModifier = Modifier<'applyStyles', {||}>;\nexport default ({\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect,\n  requires: ['computeStyles'],\n}: ApplyStylesModifier);\n", "// @flow\nimport type { Placement } from '../enums';\nimport type { ModifierArguments, Modifier, Rect, Offsets } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport { top, left, right, placements } from '../enums';\n\ntype OffsetsFunction = ({\n  popper: Rect,\n  reference: Rect,\n  placement: Placement,\n}) => [?number, ?number];\n\ntype Offset = OffsetsFunction | [?number, ?number];\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  offset: Offset,\n};\n\nexport function distanceAndSkiddingToXY(\n  placement: Placement,\n  rects: { popper: Rect, reference: Rect },\n  offset: Offset\n): Offsets {\n  const basePlacement = getBasePlacement(placement);\n  const invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  let [skidding, distance] =\n    typeof offset === 'function'\n      ? offset({\n          ...rects,\n          placement,\n        })\n      : offset;\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n\n  return [left, right].indexOf(basePlacement) >= 0\n    ? { x: distance, y: skidding }\n    : { x: skidding, y: distance };\n}\n\nfunction offset({ state, options, name }: ModifierArguments<Options>) {\n  const { offset = [0, 0] } = options;\n\n  const data = placements.reduce((acc, placement) => {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n\n  const { x, y } = data[state.placement];\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type OffsetModifier = Modifier<'offset', Options>;\nexport default ({\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset,\n}: OffsetModifier);\n", "// @flow\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { ModifierArguments, Modifier, Padding } from '../types';\nimport getOppositePlacement from '../utils/getOppositePlacement';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getOppositeVariationPlacement from '../utils/getOppositeVariationPlacement';\nimport detectOverflow from '../utils/detectOverflow';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\nimport { bottom, top, start, right, left, auto } from '../enums';\nimport getVariation from '../utils/getVariation';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  mainAxis: boolean,\n  altAxis: boolean,\n  fallbackPlacements: Array<Placement>,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  altBoundary: boolean,\n  flipVariations: boolean,\n  allowedAutoPlacements: Array<Placement>,\n};\n\nfunction getExpandedFallbackPlacements(placement: Placement): Array<Placement> {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  const oppositePlacement = getOppositePlacement(placement);\n\n  return [\n    getOppositeVariationPlacement(placement),\n    oppositePlacement,\n    getOppositeVariationPlacement(oppositePlacement),\n  ];\n}\n\nfunction flip({ state, options, name }: ModifierArguments<Options>) {\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = true,\n    fallbackPlacements: specifiedFallbackPlacements,\n    padding,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    flipVariations = true,\n    allowedAutoPlacements,\n  } = options;\n\n  const preferredPlacement = state.options.placement;\n  const basePlacement = getBasePlacement(preferredPlacement);\n  const isBasePlacement = basePlacement === preferredPlacement;\n\n  const fallbackPlacements =\n    specifiedFallbackPlacements ||\n    (isBasePlacement || !flipVariations\n      ? [getOppositePlacement(preferredPlacement)]\n      : getExpandedFallbackPlacements(preferredPlacement));\n\n  const placements = [preferredPlacement, ...fallbackPlacements].reduce(\n    (acc, placement) => {\n      return acc.concat(\n        getBasePlacement(placement) === auto\n          ? computeAutoPlacement(state, {\n              placement,\n              boundary,\n              rootBoundary,\n              padding,\n              flipVariations,\n              allowedAutoPlacements,\n            })\n          : placement\n      );\n    },\n    []\n  );\n\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n\n  const checksMap = new Map();\n  let makeFallbackChecks = true;\n  let firstFittingPlacement = placements[0];\n\n  for (let i = 0; i < placements.length; i++) {\n    const placement = placements[i];\n    const basePlacement = getBasePlacement(placement);\n    const isStartVariation = getVariation(placement) === start;\n    const isVertical = [top, bottom].indexOf(basePlacement) >= 0;\n    const len = isVertical ? 'width' : 'height';\n\n    const overflow = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      altBoundary,\n      padding,\n    });\n\n    let mainVariationSide: any = isVertical\n      ? isStartVariation\n        ? right\n        : left\n      : isStartVariation\n      ? bottom\n      : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    const altVariationSide: any = getOppositePlacement(mainVariationSide);\n\n    const checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(\n        overflow[mainVariationSide] <= 0,\n        overflow[altVariationSide] <= 0\n      );\n    }\n\n    if (checks.every((check) => check)) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    const numberOfChecks = flipVariations ? 3 : 1;\n\n    for (let i = numberOfChecks; i > 0; i--) {\n      const fittingPlacement = placements.find((placement) => {\n        const checks = checksMap.get(placement);\n        if (checks) {\n          return checks.slice(0, i).every((check) => check);\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        break;\n      }\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type FlipModifier = Modifier<'flip', Options>;\nexport default ({\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: { _skip: false },\n}: FlipModifier);\n", "// @flow\nimport type { State, Padding } from '../types';\nimport type {\n  Placement,\n  ComputedPlacement,\n  Boundary,\n  RootBoundary,\n} from '../enums';\nimport getVariation from './getVariation';\nimport {\n  variationPlacements,\n  basePlacements,\n  placements as allPlacements,\n} from '../enums';\nimport detectOverflow from './detectOverflow';\nimport getBasePlacement from './getBasePlacement';\n\ntype Options = {\n  placement: Placement,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  flipVariations: boolean,\n  allowedAutoPlacements?: Array<Placement>,\n};\n\ntype OverflowsMap = { [ComputedPlacement]: number };\n\nexport default function computeAutoPlacement(\n  state: $Shape<State>,\n  options: Options = {}\n): Array<ComputedPlacement> {\n  const {\n    placement,\n    boundary,\n    rootBoundary,\n    padding,\n    flipVariations,\n    allowedAutoPlacements = allPlacements,\n  } = options;\n\n  const variation = getVariation(placement);\n\n  const placements = variation\n    ? flipVariations\n      ? variationPlacements\n      : variationPlacements.filter(\n          (placement) => getVariation(placement) === variation\n        )\n    : basePlacements;\n\n  let allowedPlacements = placements.filter(\n    (placement) => allowedAutoPlacements.indexOf(placement) >= 0\n  );\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (__DEV__) {\n      console.error(\n        [\n          'Popper: The `allowedAutoPlacements` option did not allow any',\n          'placements. Ensure the `placement` option matches the variation',\n          'of the allowed placements.',\n          'For example, \"auto\" cannot be used to allow \"bottom-start\".',\n          'Use \"auto-start\" instead.',\n        ].join(' ')\n      );\n    }\n  }\n\n  // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n  const overflows: OverflowsMap = allowedPlacements.reduce((acc, placement) => {\n    acc[placement] = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      padding,\n    })[getBasePlacement(placement)];\n\n    return acc;\n  }, {});\n\n  return Object.keys(overflows).sort((a, b) => overflows[a] - overflows[b]);\n}\n", "// @flow\nimport { top, left, right, bottom, start } from '../enums';\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { Rect, ModifierArguments, Modifier, Padding } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport getAltAxis from '../utils/getAltAxis';\nimport within from '../utils/within';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport detectOverflow from '../utils/detectOverflow';\nimport getVariation from '../utils/getVariation';\nimport getFreshSideObject from '../utils/getFreshSideObject';\nimport { max as mathMax, min as mathMin } from '../utils/math';\n\ntype TetherOffset =\n  | (({\n      popper: Rect,\n      reference: Rect,\n      placement: Placement,\n    }) => number)\n  | number;\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  /* Prevents boundaries overflow on the main axis */\n  mainAxis: boolean,\n  /* Prevents boundaries overflow on the alternate axis */\n  altAxis: boolean,\n  /* The area to check the popper is overflowing in */\n  boundary: Boundary,\n  /* If the popper is not overflowing the main area, fallback to this one */\n  rootBoundary: RootBoundary,\n  /* Use the reference's \"clippingParents\" boundary context */\n  altBoundary: boolean,\n  /**\n   * Allows the popper to overflow from its boundaries to keep it near its\n   * reference element\n   */\n  tether: boolean,\n  /* Offsets when the `tether` option should activate */\n  tetherOffset: TetherOffset,\n  /* Sets a padding to the provided boundary */\n  padding: Padding,\n};\n\nfunction preventOverflow({ state, options, name }: ModifierArguments<Options>) {\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = false,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    padding,\n    tether = true,\n    tetherOffset = 0,\n  } = options;\n\n  const overflow = detectOverflow(state, {\n    boundary,\n    rootBoundary,\n    padding,\n    altBoundary,\n  });\n  const basePlacement = getBasePlacement(state.placement);\n  const variation = getVariation(state.placement);\n  const isBasePlacement = !variation;\n  const mainAxis = getMainAxisFromPlacement(basePlacement);\n  const altAxis = getAltAxis(mainAxis);\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const tetherOffsetValue =\n    typeof tetherOffset === 'function'\n      ? tetherOffset({\n          ...state.rects,\n          placement: state.placement,\n        })\n      : tetherOffset;\n\n  const data = { x: 0, y: 0 };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    const mainSide = mainAxis === 'y' ? top : left;\n    const altSide = mainAxis === 'y' ? bottom : right;\n    const len = mainAxis === 'y' ? 'height' : 'width';\n    const offset = popperOffsets[mainAxis];\n\n    const min = popperOffsets[mainAxis] + overflow[mainSide];\n    const max = popperOffsets[mainAxis] - overflow[altSide];\n\n    const additive = tether ? -popperRect[len] / 2 : 0;\n\n    const minLen = variation === start ? referenceRect[len] : popperRect[len];\n    const maxLen = variation === start ? -popperRect[len] : -referenceRect[len];\n\n    // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n    const arrowElement = state.elements.arrow;\n    const arrowRect =\n      tether && arrowElement\n        ? getLayoutRect(arrowElement)\n        : { width: 0, height: 0 };\n    const arrowPaddingObject = state.modifiersData['arrow#persistent']\n      ? state.modifiersData['arrow#persistent'].padding\n      : getFreshSideObject();\n    const arrowPaddingMin = arrowPaddingObject[mainSide];\n    const arrowPaddingMax = arrowPaddingObject[altSide];\n\n    // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n    const arrowLen = within(0, referenceRect[len], arrowRect[len]);\n\n    const minOffset = isBasePlacement\n      ? referenceRect[len] / 2 -\n        additive -\n        arrowLen -\n        arrowPaddingMin -\n        tetherOffsetValue\n      : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    const maxOffset = isBasePlacement\n      ? -referenceRect[len] / 2 +\n        additive +\n        arrowLen +\n        arrowPaddingMax +\n        tetherOffsetValue\n      : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n\n    const arrowOffsetParent =\n      state.elements.arrow && getOffsetParent(state.elements.arrow);\n    const clientOffset = arrowOffsetParent\n      ? mainAxis === 'y'\n        ? arrowOffsetParent.clientTop || 0\n        : arrowOffsetParent.clientLeft || 0\n      : 0;\n\n    const offsetModifierValue = state.modifiersData.offset\n      ? state.modifiersData.offset[state.placement][mainAxis]\n      : 0;\n\n    const tetherMin =\n      popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    const tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      const preventedOffset = within(\n        tether ? mathMin(min, tetherMin) : min,\n        offset,\n        tether ? mathMax(max, tetherMax) : max\n      );\n\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      const mainSide = mainAxis === 'x' ? top : left;\n      const altSide = mainAxis === 'x' ? bottom : right;\n      const offset = popperOffsets[altAxis];\n\n      const min = offset + overflow[mainSide];\n      const max = offset - overflow[altSide];\n\n      const preventedOffset = within(\n        tether ? mathMin(min, tetherMin) : min,\n        offset,\n        tether ? mathMax(max, tetherMax) : max\n      );\n\n      popperOffsets[altAxis] = preventedOffset;\n      data[altAxis] = preventedOffset - offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PreventOverflowModifier = Modifier<'preventOverflow', Options>;\nexport default ({\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset'],\n}: PreventOverflowModifier);\n", "// @flow\n\nexport default function getAltAxis(axis: 'x' | 'y'): 'x' | 'y' {\n  return axis === 'x' ? 'y' : 'x';\n}\n", "// @flow\nimport { max as mathMax, min as mathMin } from './math';\n\nexport default function within(\n  min: number,\n  value: number,\n  max: number\n): number {\n  return mathMax(min, mathMin(value, max));\n}\n", "// @flow\nimport type { Modifier, ModifierArguments, Padding, Rect } from '../types';\nimport type { Placement } from '../enums';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport contains from '../dom-utils/contains';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport within from '../utils/within';\nimport mergePaddingObject from '../utils/mergePaddingObject';\nimport expandToHashMap from '../utils/expandToHashMap';\nimport { left, right, basePlacements, top, bottom } from '../enums';\nimport { isHTMLElement } from '../dom-utils/instanceOf';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  element: HTMLElement | string | null,\n  padding:\n    | Padding\n    | (({|\n        popper: Rect,\n        reference: Rect,\n        placement: Placement,\n      |}) => Padding),\n};\n\nconst toPaddingObject = (padding, state) => {\n  padding =\n    typeof padding === 'function'\n      ? padding({ ...state.rects, placement: state.placement })\n      : padding;\n\n  return mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n};\n\nfunction arrow({ state, name, options }: ModifierArguments<Options>) {\n  const arrowElement = state.elements.arrow;\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const basePlacement = getBasePlacement(state.placement);\n  const axis = getMainAxisFromPlacement(basePlacement);\n  const isVertical = [left, right].indexOf(basePlacement) >= 0;\n  const len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  const paddingObject = toPaddingObject(options.padding, state);\n  const arrowRect = getLayoutRect(arrowElement);\n  const minProp = axis === 'y' ? top : left;\n  const maxProp = axis === 'y' ? bottom : right;\n\n  const endDiff =\n    state.rects.reference[len] +\n    state.rects.reference[axis] -\n    popperOffsets[axis] -\n    state.rects.popper[len];\n  const startDiff = popperOffsets[axis] - state.rects.reference[axis];\n\n  const arrowOffsetParent = getOffsetParent(arrowElement);\n  const clientSize = arrowOffsetParent\n    ? axis === 'y'\n      ? arrowOffsetParent.clientHeight || 0\n      : arrowOffsetParent.clientWidth || 0\n    : 0;\n\n  const centerToReference = endDiff / 2 - startDiff / 2;\n\n  // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n  const min = paddingObject[minProp];\n  const max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  const center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  const offset = within(min, center, max);\n\n  // Prevents breaking syntax highlighting...\n  const axisProp: string = axis;\n  state.modifiersData[name] = {\n    [axisProp]: offset,\n    centerOffset: offset - center,\n  };\n}\n\nfunction effect({ state, options }: ModifierArguments<Options>) {\n  let { element: arrowElement = '[data-popper-arrow]' } = options;\n\n  if (arrowElement == null) {\n    return;\n  }\n\n  // CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (__DEV__) {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(\n        [\n          'Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).',\n          'To use an SVG arrow, wrap it in an HTMLElement that will be used as',\n          'the arrow.',\n        ].join(' ')\n      );\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (__DEV__) {\n      console.error(\n        [\n          'Popper: \"arrow\" modifier\\'s `element` must be a child of the popper',\n          'element.',\n        ].join(' ')\n      );\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ArrowModifier = Modifier<'arrow', Options>;\nexport default ({\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow'],\n}: ArrowModifier);\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\nimport offset from './modifiers/offset';\nimport flip from './modifiers/flip';\nimport preventOverflow from './modifiers/preventOverflow';\nimport arrow from './modifiers/arrow';\nimport hide from './modifiers/hide';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n  offset,\n  flip,\n  preventOverflow,\n  arrow,\n  hide,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper as createPopperLite } from './popper-lite';\n// eslint-disable-next-line import/no-unused-modules\nexport * from './modifiers';\n"], "names": ["getBoundingClientRect", "element", "width", "rect", "height", "top", "right", "bottom", "left", "x", "y", "getWindow", "node", "window", "ownerDocument", "getWindowScroll", "scrollLeft", "win", "scrollTop", "isElement", "isHTMLElement", "isShadowRoot", "getNodeName", "getDocumentElement", "getWindowScrollBarX", "getComputedStyle", "isScrollParent", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "documentElement", "isOffsetParentAnElement", "scroll", "offsets", "getLayoutRect", "clientRect", "Math", "getParentNode", "getScrollParent", "listScrollParents", "list", "scrollParent", "_element$ownerDocumen", "isBody", "target", "updatedList", "getTrueOffsetParent", "getOffsetParent", "a", "isFirefox", "navigator", "currentNode", "getContainingBlock", "css", "order", "modifiers", "modifier", "visited", "dep", "depModifier", "map", "sort", "Map", "Set", "result", "debounce", "fn", "pending", "Promise", "resolve", "undefined", "getBasePlacement", "placement", "contains", "parent", "child", "rootNode", "next", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "viewport", "html", "visualViewport", "winScroll", "body", "max", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "getClippingParents", "clippingParents", "clipperElement", "accRect", "min", "clippingRect", "getMainAxisFromPlacement", "computeOffsets", "reference", "basePlacement", "commonX", "commonY", "mainAxis", "len", "variation", "start", "end", "mergePaddingObject", "paddingObject", "expandToHashMap", "value", "keys", "hashMap", "key", "detectOverflow", "state", "options", "popper", "altBoundary", "padding", "basePlacements", "referenceElement", "elementContext", "popperRect", "strategy", "popperOffsets", "popperClientRect", "referenceClientRect", "overflowOffsets", "clippingClientRect", "elementClientRect", "offsetData", "offset", "multiply", "axis", "areValidElements", "args", "popperGenerator", "generatorOptions", "defaultModifiers", "defaultOptions", "DEFAULT_OPTIONS", "effectCleanupFns", "orderedModifiers", "modifiersData", "elements", "attributes", "styles", "isDestroyed", "instance", "setOptions", "cleanupModifierEffects", "orderModifiers", "acc", "phase", "mergeByName", "merged", "current", "existing", "data", "m", "name", "cleanupFn", "effect", "noopFn", "forceUpdate", "index", "update", "destroy", "mapToStyles", "position", "gpuAcceleration", "adaptive", "roundOffsets", "roundOffsetsByDPR", "dpr", "round", "e", "hasX", "sideX", "sideY", "heightProp", "widthProp", "commonStyles", "unsetSides", "hasY", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getSideOffsets", "overflow", "preventedOffsets", "isAnySideFullyClipped", "side", "variationPlacements", "placements", "auto", "modifierPhases", "passive", "enabled", "effect$2", "resize", "scrollParents", "computeStyles", "applyStyles", "style", "Object", "effect$1", "initialStyles", "margin", "arrow", "property", "attribute", "requires", "distanceAndSkiddingToXY", "invertDistance", "rects", "distance", "skidding", "hash", "flip", "specifiedFallbackPlacements", "flipVariations", "allowedAutoPlacements", "preferredPlacement", "getExpandedFallbackPlacements", "oppositePlacement", "fallbackPlacements", "computeAutoPlacement", "allPlacements", "allowedPlacements", "overflows", "b", "checksMap", "firstFittingPlacement", "i", "isStartVariation", "isVertical", "mainVariationSide", "checks", "altVariationSide", "check", "makeFallbackChecks", "fittingPlacement", "requiresIfExists", "_skip", "preventOverflow", "checkMainAxis", "checkAltAxis", "tetherOffset", "isBasePlacement", "referenceRect", "tetherOffsetValue", "mainSide", "altSide", "additive", "tether", "minLen", "arrowElement", "arrowPaddingObject", "mathMax", "min$1", "mathMin", "arrowRect", "arrowLen", "arrowPaddingMin", "arrowPaddingMax", "maxLen", "minOffset", "offsetModifierValue", "arrowOffsetParent", "maxOffset", "tetherMin", "tetherMax", "preventedOffset", "altAxis", "minProp", "maxProp", "endDiff", "startDiff", "center", "clientSize", "hide", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "createPopper", "defaultModifiers$1", "eventListeners"], "mappings": ";;;;8OAGeA,WACbC,SAIO,CACLC,OAHIC,EAAOF,iCAIXG,OAAQD,SACRE,IAAKF,MACLG,MAAOH,QACPI,OAAQJ,SACRK,KAAML,OACNM,EAAGN,OACHO,EAAGP,OCZQQ,WAAmBC,gBAC5BA,EACKC,OAGe,oBAApBD,cACIE,EAAgBF,kBACCE,eAAsCD,OAGxDD,ECVMG,WAAyBH,SAK/B,CACLI,YALIC,EAAMN,EAAUC,gBAMpBM,UAJgBD,eCFpBE,WAAmBP,uBACED,EAAUC,YACQA,qBAKvCQ,WAAuBR,uBACFD,EAAUC,gBACQA,yBAKvCS,WAAsBT,SAEM,8CAGPD,EAAUC,eACQA,yBCtBxBU,WAAqBrB,aAChBA,YAAoB,kBAAoB,KCA7CsB,WACbtB,WAIGkB,EAAUlB,GACPA,gBAEAA,aAAqBY,iCCPdW,WAA6BvB,YASlBsB,EAAmBtB,SACzCc,EAAgBd,cCZLwB,WACbxB,YAEiBA,oBAA0BA,GCH9ByB,WAAwBzB,YAEMwB,EAAiBxB,GACrD,sECMM0B,WACbC,EACAC,EACAC,YAAAA,IAAAA,GAAmB,OAEbC,EAAkBR,EAAmBM,KAC9B7B,EAAsB4B,OAC7BI,EAA0BZ,EAAcS,GAE1CI,EAAS,CAAEjB,WAAY,EAAGE,UAAW,GACrCgB,EAAU,CAAEzB,EAAG,EAAGC,EAAG,UAErBsB,IAA6BA,IAA4BF,MAE3B,SAA9BR,EAAYO,IAEZH,EAAeK,QAEQF,ICtBdlB,EDsBckB,ICtBMT,EDsBNS,GE3BpB,CACLb,WF0ByBa,aEzBzBX,UFyByBW,aCrBlBd,EDqBkBc,MAGPA,KAChBK,EAAUlC,EAAsB6B,OACnBA,aACbK,KAAaL,aACJE,IACTG,IAAYV,EAAoBO,KAI7B,CACLtB,EAAGN,OAAY8B,aAAoBC,IACnCxB,EAAGP,MAAW8B,YAAmBC,IACjChC,MAAOC,QACPC,OAAQD,UGxCGgC,WAAuBlC,OAC9BmC,EAAapC,EAAsBC,GAIrCC,EAAQD,cACRG,EAASH,yBAEToC,SAASD,QAAmBlC,KAC9BA,EAAQkC,YAGNC,SAASD,SAAoBhC,KAC/BA,EAASgC,UAGJ,CACL3B,EAAGR,aACHS,EAAGT,YACHC,MAAAA,EACAE,OAAAA,GCrBWkC,WAAuBrC,SACP,SAAzBqB,EAAYrB,GACPA,EAOPA,gBACAA,eACCoB,EAAapB,GAAWA,OAAe,OAExCsB,EAAmBtB,GCZRsC,WAAyB3B,aAClC,CAAC,OAAQ,OAAQ,qBAAqBU,EAAYV,IAE7CA,qBAGLQ,EAAcR,IAASc,EAAed,GACjCA,EAGF2B,EAAgBD,EAAc1B,ICHxB4B,WACbvC,EACAwC,kBAAAA,IAAAA,EAAgC,QAE1BC,EAAeH,EAAgBtC,YACtByC,cAAiBzC,wBAAA0C,UACpBhC,EAAU+B,KACPE,EACX,CAAC3B,UACCA,kBAAsB,GACtBS,EAAegB,GAAgBA,EAAe,IAEhDA,IACgBD,SAAYI,KAG5BC,EAEAA,SAAmBN,EAAkBF,EAAcO,KCvBzDE,WAA6B9C,YAEVA,IAEwB,UAAvCwB,EAAiBxB,YAKZA,eAHE,KAwCI+C,WAAyB/C,WAChCY,EAASF,EAAUV,GAErB4B,EAAekB,EAAoB9C,GAGrC4B,GCxD4D,GAAvD,CAAC,QAAS,KAAM,cAAcP,EDyDpBO,KAC6B,WAA5CJ,EAAiBI,aAEjBA,EAAekB,EAAoBlB,MAInCA,IAC+B,SAA9BP,EAAYO,IACoB,SAA9BP,EAAYO,IACiC,WAA5CJ,EAAiBI,0BAKhBA,EAtDqCoB,EAAA,KACtCC,OAAYC,0CAA0C,WACxDC,EAAcd,EAoDKe,GAjDrBjC,EAAcgC,IACuC,EAArD,CAAC,OAAQ,gBAAgB9B,EAAY8B,KACrC,KACME,EAAM7B,EAAiB2B,MAMT,SAAlBE,aACoB,SAApBA,eACgB,UAAhBA,gBACA,CAAC,YAAa,uBAAuBA,eACpCJ,GAAgC,WAAnBI,cACbJ,GAAaI,UAA6B,SAAfA,SAC5B,GACOF,YAEOA,eAIX,eA2B+CvC,EEvExD0C,WAAeC,cAUCC,GACZC,MAAYD,kBAGNA,YAAqB,GACrBA,oBAA6B,aAGlB,SAAAE,GACVD,MAAYC,KACTC,EAAcC,MAAQF,KAG1BG,EAAKF,aAKCH,OA3BRI,EAAM,IAAIE,IACVL,EAAU,IAAIM,IACdC,EAAS,qBAEG,SAAAR,GAChBI,MAAQJ,OAAeA,iBAyBP,SAAAA,GACXC,MAAYD,SAEfK,EAAKL,QCrCIS,WAAqBC,OAC9BC,2BAEGA,IACHA,EAAU,IAAIC,SAAW,SAAAC,GACvBD,wBAAuB,WACrBD,OAAUG,IACFJ,eCNHK,WACbC,kBAEwB,KAAK,GCHhBC,WAAkBC,EAAiBC,OAC1CC,EAAWD,eAAqBA,mBAGlCD,WAAgBC,UACX,KAGAC,GAAYxD,EAAawD,KAE7B,IACGC,GAAQH,aAAkBG,UACrB,IAGFA,cAAmBA,aACnBA,UAIJ,ECpBMC,WAA0B5E,2BAElCA,GACHK,KAAML,IACNE,IAAKF,IACLG,MAAOH,IAASA,QAChBI,OAAQJ,IAASA,WCwBrB6E,WACE/E,EACAgF,GAEOA,GCnB2BC,aDmB3BD,EAAAA,CE/BDhE,EAAMN,EFgCRoE,OE/BEI,EAAO5D,EF+BTwD,KE9BmB9D,qBAEnBf,EAAQiF,gBACCA,mBACT1E,EAAI,EACJC,EAAI,MAQNR,EAAQkF,QACRhF,EAASgF,SAWJ,sCAAsCjC,uBACzC1C,EAAI2E,aACJ1E,EAAI0E,gBFGJL,IECG,CACL7E,MAAAA,EACAE,OAAAA,EACAK,EAAGA,EAAIe,EFJLuD,GEKFrE,EAAAA,WFJEU,KApBEjB,EAAOH,EAoBToB,SAAAA,YAjBJjB,QAiBIiB,aAhBJjB,SAAcA,MAgBViB,eAfJjB,QAAaA,OAeTiB,cAdJjB,QAcIiB,cAbJjB,SAaIiB,eAZJjB,IAASA,OACTA,IAASA,QAWLiB,EAAAA,EAAAA,GG5BE+D,EAAO5D,EAAmBtB,GAC1BoF,EAAYtE,EAAgBd,GAC5BqF,WAAOrF,wBAAA0C,OAEPzC,EAAQqF,EACZJ,cACAA,cACAG,EAAOA,cAAmB,EAC1BA,EAAOA,cAAmB,GAEtBlF,EAASmF,EACbJ,eACAA,eACAG,EAAOA,eAAoB,EAC3BA,EAAOA,eAAoB,GAGzB7E,GAAK4E,aAAuB7D,EAAoBvB,GAC9CS,GAAK2E,YAEsC,QAA7C5D,EAAiB6D,GAAQH,eAC3B1E,GAAK8E,EAAIJ,cAAkBG,EAAOA,cAAmB,GAAKpF,GHOxDkB,EAAAA,EGJG,CAAElB,MAAAA,EAAOE,OAAAA,EAAQK,EAAAA,EAAGC,EAAAA,cHoCd8E,WACbvF,EACAwF,EACAC,UAEMC,EACS,oBAAbF,EA9BJG,SAA4B3F,OACpB4F,EAAkBrD,EAAkBF,EAAcrC,IAGlD6F,EADiE,GAArE,CAAC,WAAY,iBAAiBrE,EAAiBxB,cAE1BmB,EAAcnB,GAC/B+C,EAAgB/C,GAChBA,WAES6F,GAKRD,UACL,SAACZ,YACWA,IACVP,EAASO,EAAgBa,IACO,SAAhCxE,EAAY2D,MARP,GAqBHW,CAAmB3F,GACnB,UAAUwF,mBACYE,GAAqBD,aAGL,SAACK,EAASd,UAC9C9E,EAAO6E,EAA2B/E,EAASgF,SAEnCM,EAAIpF,MAAU4F,eACZC,EAAI7F,QAAY4F,kBACfC,EAAI7F,SAAa4F,iBACnBR,EAAIpF,OAAW4F,YAG7Bf,EAA2B/E,EAXF4F,EAAgB,YAavBI,QAAqBA,gBACpBA,SAAsBA,UAC3BA,WACAA,QI9FJC,WACbzB,aAEO,CAAC,MAAO,kBAAkBA,GAAkB,IAAM,ICM5C0B,cASH,IARVC,cACAnG,YAQMoG,GAPN5B,eAOkCD,EAAiBC,GAAa,OAC9CA,EAAyBA,QCnBnB,KAAK,GDmB2B,SAClD6B,EAAUF,IAAcA,QAAkB,EAAInG,QAAgB,EAC9DsG,EAAUH,IAAcA,SAAmB,EAAInG,SAAiB,SAG9DoG,OJ3BgBhG,MI6BpB6B,EAAU,CACRzB,EAAG6F,EACH5F,EAAG0F,IAAcnG,oBJ9BOM,SIkC1B2B,EAAU,CACRzB,EAAG6F,EACH5F,EAAG0F,IAAcA,oBJnCK9F,QIuCxB4B,EAAU,CACRzB,EAAG2F,IAAcA,QACjB1F,EAAG6F,aJxCiB/F,OI4CtB0B,EAAU,CACRzB,EAAG2F,IAAcnG,QACjBS,EAAG6F,iBAILrE,EAAU,CACRzB,EAAG2F,IACH1F,EAAG0F,QAQO,OAJVI,EAAWH,EACbH,EAAyBG,GACzB,aAGII,EAAmB,MAAbD,EAAmB,SAAW,QAElCE,OJtDkBC,QIwDtBzE,EAAQsE,IACeJ,EAAUK,GAAO,EAAIxG,EAAQwG,GAAO,YJxDzCG,MI2DlB1E,EAAQsE,IACeJ,EAAUK,GAAO,EAAIxG,EAAQwG,GAAO,WEtEpDI,WACbC,2BCDO,CACLzG,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,GDCHsG,GEPQC,WAGbC,EAAUC,oBACS,SAACC,EAASC,UAC3BD,EAAQC,GAAOH,MAEd,ICuBUI,WACbC,EACAC,YAAAA,IAAAA,EAA2B,UASvBA,6BANUD,+BACZ5B,aTrB8CI,oBSsB9CH,8BTrBgCR,6CAOJqC,+BSgB5BC,kBAIoBX,EACD,0CAJT,KAKNY,EACAV,EAAgBU,EAASC,QAKzBC,EAAmBN,uBACNA,iBAGQ7B,EACzBrE,IAHckG,WAAeG,ET9BDD,WS0BXK,ETzBiBxB,YADNmB,SS8B4BK,IAIpD3H,EACAA,kBAA0BsB,EAAmB8F,mBACjD5B,EACAC,KAKoBS,EAAe,CACnCC,YAH0BpG,EAAsB2H,GAIhD1H,QAAS4H,EACTC,SAAU,WACVrD,UAAAA,MAGuBM,mBACpB8C,EACAE,MTnDyBR,WSuD5BK,EAA4BI,EAAmBC,MAI3CC,EAAkB,CACtB7H,IAAK8H,MAAyBC,MAAwBtB,MACtDvG,OACE6H,SACAD,SACArB,SACFtG,KAAM2H,OAA0BC,OAAyBtB,OACzDxG,MACE8H,QAA0BD,QAA2BrB,cAGtCO,uBTtEWE,WSyE1BK,GAA6BS,EAAY,KACrCC,EAASD,EAAW5D,eAEdyD,YAAyB,SAACf,OAC9BoB,EAA2C,GAAhC,CTnGOjI,QADEC,kBSoGe4G,GAAY,KAC/CqB,EAAqC,GAA9B,CTtGOnI,MACME,kBSqGS4G,GAAY,IAAM,MACrCA,IAAQmB,EAAOE,GAAQD,cCjE7CE,iBAAwD,uBAA3BC,uBAAAA,yBACnBA,QACN,SAACzI,WACGA,GAAoD,+CAIrD0I,WAAyBC,YAAAA,IAAAA,EAAwC,6BAEpEC,aAAmB,KACnBC,gCAAiBC,oBAIjB3C,EACAmB,EACAD,gBAgOE0B,WAAyB,SAAC7E,mBACP,YAjOrBmD,IAAAA,EAA6CwB,OAEzCzB,EAAuB,CACzB5C,UAAW,SACXwE,iBAAkB,GAClB3B,yBAAcyB,EAAoBD,GAClCI,cAAe,GACfC,SAAU,CACR/C,UAAAA,EACAmB,OAAAA,GAEF6B,WAAY,GACZC,OAAQ,IAGNL,EAAsC,GACtCM,GAAc,EAEZC,EAAW,CACflC,MAAAA,EACAmC,oBAAWlC,UACTmC,+BAIKX,EACAzB,UACAC,mBAGiB,CACpBlB,UAAWjF,EAAUiF,GACjB5D,EAAkB4D,GAClBA,iBACA5D,EAAkB4D,kBAClB,GACJmB,OAAQ/E,EAAkB+E,MhB7CrBmC,SACblG,OAGMyF,EAAmB1F,EAAMC,oBAGF,SAACmG,EAAKC,mBAE/BX,UAAwB,SAAAxF,oBAA+BmG,QAExD,IgBuC4BF,CC7FlBG,SACbrG,OAEMsG,EAAStG,UAAiB,SAACsG,EAAQC,OACjCC,EAAWF,EAAOC,iBACjBA,QAAgBC,mBAEdA,EACAD,GACHzC,yBAAc0C,UAAqBD,WACnCE,sBAAWD,OAAkBD,UAE/BA,MAEH,uBAGgBD,QAAY,SAAA3C,YAAcA,MD6ErC0C,WAAgBhB,EAAqBxB,0CAId4B,UAAwB,SAACiB,uBAwKpD7C,4BAA+B,YAAoC,IAAjC8C,kCAAgB,sCAExCC,EAAYC,EAAO,CAAEhD,MAAAA,EAAO8C,KAAAA,EAAMZ,SAAAA,EAAUjC,QAAAA,IAElD0B,OAAsBoB,GADPE,8BA5GnBC,2BACMjB,GADQ,MAKkBjC,WAAtBjB,iBAIHqC,EAAiBrC,kBAQtBiB,QAAc,CACZjB,UAAWzE,EACTyE,EACApD,EAAgBuE,GACW,UAA3BF,oBAEFE,OAAQpF,EAAcoF,IAQxBF,SAAc,EAEdA,YAAkBA,oBAMlBA,4BACE,SAAC5D,0BACsBA,yBAChBA,WAKA+G,EAAQ,EAAGA,EAAQnD,0BAA+BmD,QASrC,IAAhBnD,QACFA,SAAc,EACdmD,UAXgE,MAe/BnD,mBAAuBmD,uCAApC,qCAGpBnD,EAAQlD,EAAG,CAAEkD,MAAAA,EAAOC,QAAAA,EAAS6C,KAAAA,EAAMZ,SAAAA,KAAelC,MAOxDoD,OAAQvG,GACN,sBACMG,SAAuB,SAACC,GAC1BiF,kBACQlC,SAIdqD,mBACEjB,OACc,WAIbhB,EAAiBrC,EAAWmB,iBAObD,SAAc,SAACD,IAC5BiC,GAAehC,iBAClBA,gBAAsBD,YElNvBsD,oBACLpD,WACAM,eACApD,cACAvC,YACA0I,aACAC,oBACAC,iBAamB,uBAAjBC,CA9B4BrK,EA+BxBsK,QA7BAC,EADcpK,yBACgB,IAE7B,CACLJ,EAAGyK,EAAMA,EA0BLF,IA1BeC,GAAOA,IAAQ,EAClCvK,EAAGwK,EAAMA,EAAMxK,EAAIuK,GAAOA,IAAQ,UA0B9B,qBAAAhI,EAAAkI,GAAAA,mBAFJJ,MADQ,uBAAO,QAOXK,EAAOlJ,iBAAuB,OACvBA,iBAAuB,WAEhCmJ,EZ1EsB7K,OY2EtB8K,EZ9EoBjL,MYgFlBY,EAAcJ,UAEhBiK,EAAU,KACRjJ,EAAemB,EAAgBuE,GAC/BgE,EAAa,eACbC,EAAY,kBAEK7K,EAAU4G,KAGmB,WAA5C9F,EAFJI,EAAeN,EAAmBgG,eAGhCgE,EAAa,eACbC,EAAY,wBAOZ/G,IACF6G,EZnG0B/K,SYqG1BG,GAAKmB,EAAa0J,GAAc1D,SAChCnH,GAAKmK,EAAkB,eAGrBpG,IACF4G,EZzGwB/K,QY2GxBG,GAAKoB,EAAa2J,GAAa3D,QAC/BpH,GAAKoK,EAAkB,aAIrBY,iBACJb,SAAAA,GACIE,GAAYY,GAGdb,mBAEGY,UACFH,GAAQK,EAAO,IAAM,KACrBN,GAAQD,EAAO,IAAM,eAKU,GAA7BnK,oBAAwB,gBACRR,SAAQC,uBACND,SAAQC,gCAK5B+K,UACFH,GAAQK,EAAUjL,OAAQ,KAC1B2K,GAAQD,EAAU3K,OAAQ,eAChB,OCtIAmL,WAA8BnH,oBAEzC,0BACA,SAAAoH,YAAgBA,MCHLC,WACbrH,oBAE0B,cAAc,SAAAoH,aAAgBA,MCG1DE,WACEC,EACA7L,EACA8L,mBAAAA,IAAAA,EAA4B,CAAExL,EAAG,EAAGC,EAAG,IAEhC,CACLL,IAAK2L,MAAe7L,SAAc8L,IAClC3L,MAAO0L,QAAiB7L,QAAa8L,IACrC1L,OAAQyL,SAAkB7L,SAAc8L,IACxCzL,KAAMwL,OAAgB7L,QAAa8L,KAIvCC,WAA+BF,SACtB,CfxBiB3L,MAEIC,QADEC,SAEJC,ceqBa,SAAC2L,aAASH,EAASG,MfdrD,IAAMzE,EAAuC,CAV1BrH,MACME,SACFD,QACFE,QAsCf4L,EAAiD1E,UAC5D,SAACiC,EAAgClF,mBACpB,CAAKA,WAAgCA,aAClD,IAEW4H,EAA+B,UAAI3E,GA1CpB4E,iBA2C1B,SACE3C,EACAlF,mBAEW,CACTA,EACIA,WACAA,aAER,IAeW8H,EAAwC,yFAAA,KgBvExChH,EAAMlD,SACN2D,EAAM3D,SACN6I,EAAQ7I,WNyBf0G,EAAuC,CAC3CtE,UAAW,SACXjB,UAAW,GACXsE,SAAU,YOrBN0E,EAAU,CAAEA,SAAS,KAoCX,CACdrC,KAAM,iBACNsC,SAAS,EACT7C,MAAO,QACPzF,GAAIA,aACJkG,OAvCFqC,YAA0E,IAAxDrF,UAAOkC,oCACftH,gBAAe0K,cAAkBrF,aAEnCzG,EAASF,EAAU0G,mBACnBuF,YACDvF,0BACAA,kCAIHuF,WAAsB,SAAAlK,GACpBA,mBAA8B,SAAU6G,SAAiBiD,SAK3D3L,mBAAwB,SAAU0I,SAAiBiD,cAI/CvK,GACF2K,WAAsB,SAAAlK,GACpBA,sBAAiC,SAAU6G,SAAiBiD,SAK9D3L,sBAA2B,SAAU0I,SAAiBiD,KAa1DvC,KAAM,MCjCQ,CACdE,KAAM,gBACNsC,SAAS,EACT7C,MAAO,OACPzF,GAnBF4D,YAAiE,IAAxCV,kCAKKlB,EAAe,CACzCC,UAAWiB,kBACXpH,QAASoH,eACTS,SAAU,WACVrD,UAAW4C,eAWb4C,KAAM,INKFyB,EAAa,CACjBrL,IAAK,OACLC,MAAO,OACPC,OAAQ,OACRC,KAAM,UA0LQ,CACd2J,KAAM,gBACNsC,SAAS,EACT7C,MAAO,cACPzF,GAhFF0I,YAAuE,IAA9CxF,UAAOC,0BAM1BA,4BAAAA,yCAAAA,qBA6BiB,CACnB7C,UAAWD,EAAiB6C,aAC5BE,OAAQF,kBACRQ,WAAYR,eACZwD,gBAAAA,SAGExD,gCACFA,iCACKA,gBACAsD,mBACEc,GACHvJ,QAASmF,8BACTuD,SAAUvD,mBACVyD,SAAAA,EACAC,aAAAA,aAKF1D,wBACFA,gCACKA,eACAsD,mBACEc,GACHvJ,QAASmF,sBACTuD,SAAU,WACVE,UAAU,EACVC,aAAAA,4CAMD1D,6CACsBA,eAW3B4C,KAAM,MOtIQ,CACdE,KAAM,cACNsC,SAAS,EACT7C,MAAO,QACPzF,GAtFF2I,gBAAuBzF,sBACTA,qBAAwB,SAAC8C,OAC7B4C,EAAQ1F,SAAa8C,IAAS,GAE9Bf,EAAa/B,aAAiB8C,IAAS,GACvClK,EAAUoH,WAAe8C,KAGZlK,IAAaqB,EAAYrB,KAO5C+M,cAAc/M,QAAe8M,GAE7BC,YAAY5D,YAAoB,SAACe,OACzBnD,EAAQoC,EAAWe,QACrBnD,EACF/G,kBAAwBkK,GAExBlK,eAAqBkK,GAAgB,IAAVnD,EAAiB,GAAKA,WAiEvDqD,OA3DF4C,gBAAkB5F,UACV6F,EAAgB,CACpB3F,OAAQ,CACNqD,SAAUvD,mBACV7G,KAAM,IACNH,IAAK,IACL8M,OAAQ,KAEVC,MAAO,CACLxC,SAAU,YAEZxE,UAAW,yBAGCiB,wBAA6B6F,mBAC5BA,oBAGbF,cAAc3F,uBAA4B6F,oBAI1CF,YAAY3F,qBAAwB,SAAC8C,OAC7BlK,EAAUoH,WAAe8C,GACzBf,EAAa/B,aAAiB8C,IAAS,KAErB6C,YACtB3F,wBAA4B8C,GACxB9C,SAAa8C,GACb+C,EAAc/C,YAIiB,SAAC4C,EAAOM,UAC3CN,EAAMM,GAAY,OAEjB,MAGgBpN,IAAaqB,EAAYrB,KAI5C+M,cAAc/M,QAAe8M,GAE7BC,YAAY5D,YAAoB,SAACkE,GAC/BrN,kBAAwBqN,YAc9BC,SAAU,CAAC,oBCjCG,CACdpD,KAAM,SACNsC,SAAS,EACT7C,MAAO,OACP2D,SAAU,CAAC,iBACXpJ,GAzBFmE,YAAsE,IAApDjB,UAAgB8C,SACxB7B,gCAAS,CAAC,EAAG,UAER+D,UAAkB,SAAC1C,EAAKlF,GAClB+I,IAAmCnG,EAAAA,QAvBhDhB,EAAgB7B,EAuBqBC,GAtBrCgJ,EAAuD,GAAtC,CpBrBGjN,OAHFH,eoBwBmBgG,MAA2B,IAGlD,qBAmB+CiC,mBAjBxDoF,GACHjJ,UAgBmCA,KAAwB6D,qBAZ5C,eACC,GAAKmF,IAEkB,GAAxC,CpBlCmBjN,OADEF,iBoBmCC+F,GACzB,CAAE5F,EAAGkN,EAAUjN,EAAGkN,GAClB,CAAEnN,EAAGmN,EAAUlN,EAAGiN,KAOhBlJ,GAAa+I,MAEhB,KAEmBnG,aAAd5G,kBAEJ4G,gCACFA,iCAAuC5G,EACvC4G,iCAAuC3G,mBAGrByJ,GAAQF,IPvDxB4D,EAAO,CAAErN,KAAM,QAASF,MAAO,OAAQC,OAAQ,MAAOF,IAAK,UCA3DwN,GAAO,CAAElH,MAAO,MAAOC,IAAK,YOsKlB,CACduD,KAAM,OACNsC,SAAS,EACT7C,MAAO,OACPzF,GAvIF2J,YAAoE,IAApDzG,UAAOC,yBACjBD,gBAAoB8C,UAD0C,MAe9D7C,iCAAAA,8BAPkByG,EAOlBzG,qBANFG,EAMEH,UALF7B,EAKE6B,WAJF5B,EAIE4B,eAHFE,EAGEF,gBAAAA,iBAFF0G,gBACAC,EACE3G,0BAGkB9C,IADK6C,uBAKzB0G,IAHsB1H,IAAkB6H,GAInBF,EArCzBG,SAAuC1J,MrBnBX6H,SqBoBtB9H,EAAiBC,SACZ,OAGH2J,EAAoBxC,EAAqBnH,SAExC,CACLqH,EAA8BrH,GAC9B2J,EACAtC,EAA8BsC,IA6B1BD,CAA8BD,GAD9B,CAACtC,EAAqBsC,SAGtB7B,EAAa,CAAC6B,UAAuBG,WACzC,SAAC1E,EAAKlF,mBrB7DkB6H,SqB+DpB9H,EAAiBC,GCxCV6J,SACbjH,EACAC,YAAAA,IAAAA,EAAmB,QAIjB7B,aACAC,iBACA+B,YACAuG,6CACAC,aAAwBM,IAGpB7H,oBjBrCkB,KAAK,aiBuCVA,EACfsH,EACE5B,EACAA,UACE,SAAC3H,kBjB3Ce,KAAK,KiB2CsBiC,KAE/CgB,WAGF,SAACjD,aAAcwJ,UAA8BxJ,gBAI7C+J,EAAoBnC,OAgBhBoC,EAA0BD,UAAyB,SAAC7E,EAAKlF,UAC7DkF,EAAIlF,GAAa2C,EAAeC,EAAO,CACrC5C,UAAAA,EACAgB,SAAAA,EACAC,aAAAA,EACA+B,QAAAA,IACCjD,EAAiBC,QAGnB,uBAEgBgK,SAAgB,SAACxL,EAAGyL,YAAgBzL,GAAKwL,EAAUC,MDd5DJ,CAAqBjH,EAAO,CAC1B5C,UAAAA,EACAgB,SAAAA,EACAC,aAAAA,EACA+B,QAAAA,EACAuG,eAAAA,EACAC,sBAAAA,IAEFxJ,KAGR,MAGoB4C,oBACHA,mBAEbsH,EAAY,IAAI5K,OACG,UACrB6K,EAAwBvC,EAAW,GAE9BwC,EAAI,EAAGA,EAAIxC,SAAmBwC,IAAK,KACpCpK,EAAY4H,EAAWwC,GACvBxI,EAAgB7B,EAAiBC,GACjCqK,ErBhFoBnI,UqBgFYlC,QhBzFhB,KAAK,GgB0FrBsK,EAAqD,GAAxC,CrB7FG1O,MACME,kBqB4Fa8F,GACnCI,EAAMsI,EAAa,QAAU,SAE7B/C,EAAW5E,EAAeC,EAAO,CACrC5C,UAAAA,EACAgB,SAAAA,EACAC,aAAAA,EACA8B,YAAAA,EACAC,QAAAA,SAG2BsH,EACzBD,ErBvGsBxO,QACFE,OqByGpBsO,ErB3GwBvO,SADNF,QqBgHJoG,GAAOoB,EAAWpB,KAClCuI,EAAoBpD,EAAqBoD,MAGbpD,EAAqBoD,KAEpC,MAGbC,OAAuC,GAA3BjD,EAAS3F,OAIrB4I,OACiC,GAA/BjD,EAASgD,GACqB,GAA9BhD,EAASkD,IAITD,SAAa,SAACE,eAAkB,CAClCP,EAAwBnK,KACH,QAIvBkK,MAAclK,EAAWwK,MAGvBG,iBAIOP,OACDQ,EAAmBhD,QAAgB,SAAC5H,MAClCwK,EAASN,MAAclK,kBAEP,EAAGoK,UAAS,SAACM,qBAIjCE,WACsBA,WATnBR,EAFcb,EAAiB,EAAI,EAEX,EAAJa,eAApBA,GAA2BA,KAelCxH,cAAoBuH,IACtBvH,gBAAoB8C,UAAc,EAClC9C,YAAkBuH,EAClBvH,SAAc,KAWhBiI,iBAAkB,CAAC,UACnBrF,KAAM,CAAEsF,OAAO,OEWD,CACdpF,KAAM,kBACNsC,SAAS,EACT7C,MAAO,OACPzF,GAhJFqL,YAA+E,IAApDnI,UAAOC,2BAU5BA,WARQmI,gBACDC,cAOPpI,4BAAAA,mBAAAA,eADFqI,aAAe,IAGX3D,EAAW5E,EAAeC,EAAO,CACrC5B,SAHE6B,WAIF5B,aAJE4B,eAKFG,QALEH,UAMFE,YANEF,kBAQkB9C,EAAiB6C,iBACjCX,EAAyBW,kBlB7DP,KAAK,GkB8DvBuI,GAAmBlJ,EACnBF,EAAWN,EAAyBG,KChE1B,MDiEWG,ECjEL,IAAM,MDkENa,kCAChBwI,EAAgBxI,kBAChBQ,EAAaR,eACbyI,EACoB,qBACpBH,mBACKtI,SACH5C,UAAW4C,eAEbsI,OAEO,CAAElP,EAAG,EAAGC,EAAG,GAEnBqH,MAID0H,GAAiBC,EAAc,KAC3BK,EAAwB,MAAbvJ,EvBtFKnG,MAGEG,OuBoFlBwP,EAAuB,MAAbxJ,EvBtFYjG,SACFD,QuBsFpBmG,EAAmB,MAAbD,EAAmB,SAAW,QACpC8B,EAASP,EAAcvB,GAEvBR,EAAM+B,EAAcvB,GAAYwF,EAAS+D,GACzCxK,EAAMwC,EAAcvB,GAAYwF,EAASgE,GAEzCC,EAAWC,GAAUrI,EAAWpB,GAAO,EAAI,EAE3C0J,EvBpFoBxJ,UuBoFXD,EAAsBmJ,EAAcpJ,GAAOoB,EAAWpB,KvBpF3CE,UuBqFXD,GAAuBmB,EAAWpB,IAAQoJ,EAAcpJ,KAIlDY,mBAEnB6I,GAAUE,EACNjO,EAAciO,GACd,CAAElQ,MAAO,EAAGE,OAAQ,OACpBiQ,EAAqBhJ,gBAAoB,oBAC3CA,gBAAoB,4BhBxGnB,CACLhH,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,KgBsGkB6P,EAAmBN,KACnBM,EAAmBL,KEvGtCM,EF8GmBC,EE9GNC,EF8GSX,EAAcpJ,GAAMgK,EAAUhK,OAEvCmJ,EACdC,EAAcpJ,GAAO,EACrBwJ,EACAS,EACAC,EACAb,EACAK,EAASO,EAAWC,EAAkBb,IACxBF,GACbC,EAAcpJ,GAAO,EACtBwJ,EACAS,EACAE,EACAd,EACAe,EAASH,EAAWE,EAAkBd,IAGxCzI,kBAAwBrE,EAAgBqE,oBAOdA,uBACxBA,uBAA2BA,aAAiBb,GAC5C,IAGFuB,EAAcvB,GAAYsK,EAAYC,GAXnBC,EACJ,MAAbxK,EACEwK,aAA+B,EAC/BA,cAAgC,EAClC,KAQcjJ,EAAcvB,GAAYyK,EAAYF,MAIpDb,EAAAA,EAASM,EAAQxK,EAAKkL,GAAalL,EAEnCkK,EAAAA,EAASI,EAAQ/K,EAAK4L,GAAa5L,IEnJlC+K,EAAQtK,EAAKwK,EFkJdlI,EElJ6B/C,IFsJ/BwC,EAAcvB,GAAY4K,EAC1BnH,EAAKzD,GAAY4K,EAAkB9I,OAQ7BtC,GAFAsC,EAASP,EAAcsJ,IAERrF,EAJS,MAAbxF,EvBlKGnG,MAGEG,QuBoKhB+E,EAAM+C,EAAS0D,EAJQ,MAAbxF,EvBlKUjG,SACFD,SuBwKtB4P,EAAAA,EAASM,EAAQxK,EAAKkL,GAAalL,EAEnCkK,EAAAA,EAASI,EAAQ/K,EAAK4L,GAAa5L,IErKlC+K,EAAQtK,EAAKwK,EFoKdlI,EEpK6B/C,IFwK/BwC,EAAcsJ,GAAWD,EACzBnH,EAAKoH,GAAWD,EAAkB9I,GAItCjB,gBAAoB8C,GAAQF,IAU5BqF,iBAAkB,CAAC,cG1DL,CACdnF,KAAM,QACNsC,SAAS,EACT7C,MAAO,OACPzF,GAlGFiJ,kBAAiB/F,UAAO8C,SAAM7C,YACtB8I,EAAe/I,iBACfU,EAAgBV,8BAChBhB,EAAgB7B,EAAiB6C,kBAC1BnB,EAAyBG,KACqB,GAAxC,C1BxCO7F,OADEF,iB0ByCa+F,GAChB,SAAW,QAE/B+J,GAAiBrI,KAfflB,EACc,mBALA,mBAuBiBS,EAAAA,WAtBhCG,mBAsBiDJ,SAtBvB5C,UAsBuB4C,eArBjDI,GAIAA,EACAV,EAAgBU,EAASC,QAiBzB+I,EAAYtO,EAAciO,GAC1BkB,EAAmB,MAAT9I,E1BpDQnI,MAGEG,O0BkDpB+Q,EAAmB,MAAT/I,E1BpDcjI,SACFD,Q0BqDtBkR,EACJnK,kBAAsBZ,GACtBY,kBAAsBmB,GACtBT,EAAcS,GACdnB,eAAmBZ,KACHsB,EAAcS,GAAQnB,kBAAsBmB,SAExDwI,EAAoBhO,EAAgBoN,IAE7B,MAAT5H,EACEwI,gBAAkC,EAClCA,eAAiC,EACnC,GAQwB,EAAIP,EAAUhK,GAAO,GANvB+K,EAAU,EAAIC,EAAY,KD9D7CnB,ECkEKxJ,EAAcwK,GDlENd,ECqEOkB,EAFfC,EAAalB,EAAUhK,GAAOK,EAAcyK,qBAMpCpH,WADK3B,GAEXF,iBACEA,EAASoJ,OAuDzBrH,OAnDFA,YAAgE,IAA9ChD,aAGI,wCAFU,6BAOF,sBAC1B+I,EAAe/I,gCAAoC+I,aAmBvC/I,kBAAuB+I,KAarC/I,iBAAuB+I,KAWvB7C,SAAU,CAAC,iBACX+B,iBAAkB,CAAC,uBXvEL,CACdnF,KAAM,OACNsC,SAAS,EACT7C,MAAO,OACP0F,iBAAkB,CAAC,mBACnBnL,GA9CFyN,YAAwD,IAAxCvK,uBACRwI,EAAgBxI,kBAChBQ,EAAaR,eACb4E,EAAmB5E,gCAEnBwK,EAAoBzK,EAAeC,EAAO,CAC9CO,eAAgB,cAEZkK,EAAoB1K,EAAeC,EAAO,CAC9CG,aAAa,MAGkBuE,EAC/B8F,EACAhC,KAE0B9D,EAC1B+F,EACAjK,EACAoE,KAGwBC,EAAsB6F,KACvB7F,EAAsB8F,mBAE3B7H,GAAQ,CAC1B4H,yBAAAA,EACAC,oBAAAA,EACAC,kBAAAA,EACAC,iBAAAA,wCAIG7K,oDAC6B4K,wBACTC,MY9CrBC,GAAexJ,EAAgB,CAAEE,iBAPduJ,CACvBC,EACAtK,EACA8E,EACAC,KCCIjE,GAAmB,CACvBwJ,EACAtK,EACA8E,EACAC,EACAxE,EACAwF,GACA0B,GACApC,GACAwE,IAGIO,GAAexJ,EAAgB,CAAEE,iBAAAA"}
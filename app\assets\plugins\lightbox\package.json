{"name": "fslightbox", "version": "3.2.3", "description": "Modern and easy plugin for displaying images and videos in clean overlaying box. Display single source or create beautiful gallery with powerful lightbox.", "keywords": ["lightbox", "slide gallery", "image lightbox", "slider", "carousel"], "main": "index.js", "scripts": {"test": "jest", "production": "webpack --mode production --config webpack.prod.config.js --display-modules && gulp", "watch": "webpack-dev-server  --mode development --host 0.0.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/banthagroup/fslightbox"}, "author": "banthagroup", "license": "MIT", "bugs": {"url": "https://github.com/banthagroup/fslightbox/issues"}, "homepage": "https://fslightbox.com", "jest": {"verbose": true, "testPathIgnorePatterns": ["/node_modules/", "/demo/", "/dist/"], "collectCoverage": false}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/preset-react": "^7.0.0", "@babel/register": "^7.4.4", "babel-jest": "^24.7.1", "babel-loader": "^8.0.5", "babel-polyfill": "^6.26.0", "browser-sync": "^2.26.7", "copy-webpack-plugin": "^5.0.3", "core-js": "^3.0.1", "css-loader": "^2.1.0", "gulp": "^4.0.2", "gulp-clean-css": "^4.2.0", "gulp-rename": "^1.4.0", "gulp-sass": "^4.0.2", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "jest": "^24.7.1", "node-sass": "^4.12.0", "prop-types": "^15.6.2", "sass-loader": "^7.1.0", "style-loader": "^0.23.1", "uglifyjs-webpack-plugin": "^2.1.1", "webpack": "^4.30.0", "webpack-cli": "^3.3.1", "webpack-dev-server": "^3.3.1"}}